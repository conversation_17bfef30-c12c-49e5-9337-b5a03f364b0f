﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Irrlicht">
      <UniqueIdentifier>{72c30315-bbc0-4109-9ccd-fb7107ba316a}</UniqueIdentifier>
    </Filter>
    <Filter Include="Irrlicht\video">
      <UniqueIdentifier>{3cb7865d-a5e9-4b22-8f54-dde759b88c51}</UniqueIdentifier>
    </Filter>
    <Filter Include="Irrlicht\video\Null">
      <UniqueIdentifier>{a9ca9d4d-7678-4687-b78b-15236c0dcf53}</UniqueIdentifier>
    </Filter>
    <Filter Include="Irrlicht\video\Null\Writer">
      <UniqueIdentifier>{d694e7b0-0fb0-4685-ace7-56d9ec65a3d0}</UniqueIdentifier>
    </Filter>
    <Filter Include="Irrlicht\video\Null\Loader">
      <UniqueIdentifier>{e2571a61-945c-4509-b47c-daea464916ab}</UniqueIdentifier>
    </Filter>
    <Filter Include="Irrlicht\irr">
      <UniqueIdentifier>{1173499e-79e8-4c34-8046-abc325e2f2a9}</UniqueIdentifier>
    </Filter>
    <Filter Include="Irrlicht\irr\device">
      <UniqueIdentifier>{6e842906-e193-451d-8716-12eaafabd0d8}</UniqueIdentifier>
    </Filter>
    <Filter Include="Irrlicht\doc">
      <UniqueIdentifier>{a72cb2e5-a5c3-41bc-9c86-fdbdae8f7866}</UniqueIdentifier>
    </Filter>
    <Filter Include="Irrlicht\writers">
      <UniqueIdentifier>{b1e0eba0-37fe-449b-9e07-a077745d83ee}</UniqueIdentifier>
    </Filter>
    <Filter Include="Irrlicht\gui">
      <UniqueIdentifier>{c043e2a2-bbc0-4105-85c5-b78c31dc1ed1}</UniqueIdentifier>
    </Filter>
    <Filter Include="Irrlicht\io">
      <UniqueIdentifier>{14c82f3c-857d-4d47-bcb8-da61aff61a1b}</UniqueIdentifier>
    </Filter>
    <Filter Include="Irrlicht\loaders">
      <UniqueIdentifier>{d6916844-a07e-4799-a32a-33068cc1114f}</UniqueIdentifier>
    </Filter>
    <Filter Include="Irrlicht\particleSystem">
      <UniqueIdentifier>{2fc39d77-34c3-4bf5-99fb-47fcb586fd78}</UniqueIdentifier>
    </Filter>
    <Filter Include="Irrlicht\scene">
      <UniqueIdentifier>{4bed7784-5e9b-42c4-ac84-06eb6145168c}</UniqueIdentifier>
    </Filter>
    <Filter Include="Irrlicht\scene\sceneNodes">
      <UniqueIdentifier>{ebc87c53-2a75-4c5b-99b8-5c30090dbd4f}</UniqueIdentifier>
    </Filter>
    <Filter Include="Irrlicht\scene\collision">
      <UniqueIdentifier>{9979be2b-fda8-4014-afbf-1fb5d0e1aa7f}</UniqueIdentifier>
    </Filter>
    <Filter Include="Irrlicht\scene\animators">
      <UniqueIdentifier>{9cf42bbf-8edb-4ae5-ad31-db670342ca57}</UniqueIdentifier>
    </Filter>
    <Filter Include="Irrlicht\include">
      <UniqueIdentifier>{b5bde5d3-f9e4-4036-8c28-2f4e8cd03846}</UniqueIdentifier>
    </Filter>
    <Filter Include="Irrlicht\include\video">
      <UniqueIdentifier>{0b0937fb-2270-4e3e-a94f-f92bc0fa74ae}</UniqueIdentifier>
    </Filter>
    <Filter Include="Irrlicht\include\core">
      <UniqueIdentifier>{67300400-93d5-4a7e-8b59-7c0d7b1f6d75}</UniqueIdentifier>
    </Filter>
    <Filter Include="Irrlicht\include\io">
      <UniqueIdentifier>{feb206b9-81b6-45c0-b4e5-9e637fe060e7}</UniqueIdentifier>
    </Filter>
    <Filter Include="Irrlicht\include\scene">
      <UniqueIdentifier>{af459bf5-2849-4a0e-9a21-91acbbf1c6b5}</UniqueIdentifier>
    </Filter>
    <Filter Include="Irrlicht\include\gui">
      <UniqueIdentifier>{aa649d49-922d-4118-8574-f05c13d67706}</UniqueIdentifier>
    </Filter>
    <Filter Include="Irrlicht\3RD">
      <UniqueIdentifier>{cbf2566a-bb2e-4028-aeb7-8e2f086db9c5}</UniqueIdentifier>
    </Filter>
    <Filter Include="Irrlicht\3RD\zlib">
      <UniqueIdentifier>{7008f7d1-f0fc-456c-9ca0-fffde2f72f6e}</UniqueIdentifier>
    </Filter>
    <Filter Include="Irrlicht\video\Direct3D9">
      <UniqueIdentifier>{1354e9fa-cea6-461e-af7f-9940bb5f0a2f}</UniqueIdentifier>
    </Filter>
    <Filter Include="Irrlicht\video\OpenGL">
      <UniqueIdentifier>{828d8df8-e8ae-4fd0-9d6c-7f9181ade971}</UniqueIdentifier>
    </Filter>
    <Filter Include="Vulkan">
      <UniqueIdentifier>{31646349-b877-48b7-a01c-d5a21f717afb}</UniqueIdentifier>
    </Filter>
    <Filter Include="Vulkan\shaderman">
      <UniqueIdentifier>{e7cbe255-89bb-4642-a4ef-3a522f2a3c3b}</UniqueIdentifier>
    </Filter>
    <Filter Include="Vulkan\base">
      <UniqueIdentifier>{ec9dfd2b-3823-496d-afc5-27b112a0494a}</UniqueIdentifier>
    </Filter>
    <Filter Include="imgui">
      <UniqueIdentifier>{d24b39d8-ea1e-416d-99d3-03c481535886}</UniqueIdentifier>
    </Filter>
    <Filter Include="imgui\Inguizmo">
      <UniqueIdentifier>{bbcfb52f-2040-4a15-b3fd-9206066f411f}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\..\include\IEventReceiver.h">
      <Filter>Irrlicht\include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\ILogger.h">
      <Filter>Irrlicht\include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IOSOperator.h">
      <Filter>Irrlicht\include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IReferenceCounted.h">
      <Filter>Irrlicht\include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\irrlicht.h">
      <Filter>Irrlicht\include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IrrlichtDevice.h">
      <Filter>Irrlicht\include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\irrTypes.h">
      <Filter>Irrlicht\include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\ITimer.h">
      <Filter>Irrlicht\include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\Keycodes.h">
      <Filter>Irrlicht\include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SIrrCreationParameters.h">
      <Filter>Irrlicht\include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SKeyMap.h">
      <Filter>Irrlicht\include</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\EDriverTypes.h">
      <Filter>Irrlicht\include\video</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IGeometryCreator.h">
      <Filter>Irrlicht\include\video</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IGPUProgrammingServices.h">
      <Filter>Irrlicht\include\video</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IImage.h">
      <Filter>Irrlicht\include\video</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IImageLoader.h">
      <Filter>Irrlicht\include\video</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IMaterialRenderer.h">
      <Filter>Irrlicht\include\video</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IMaterialRendererServices.h">
      <Filter>Irrlicht\include\video</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IShaderConstantSetCallBack.h">
      <Filter>Irrlicht\include\video</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\ITexture.h">
      <Filter>Irrlicht\include\video</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IVideoDriver.h">
      <Filter>Irrlicht\include\video</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IVideoModeList.h">
      <Filter>Irrlicht\include\video</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\S3DVertex.h">
      <Filter>Irrlicht\include\video</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SColor.h">
      <Filter>Irrlicht\include\video</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SExposedVideoData.h">
      <Filter>Irrlicht\include\video</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SLight.h">
      <Filter>Irrlicht\include\video</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SMaterial.h">
      <Filter>Irrlicht\include\video</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SMaterialLayer.h">
      <Filter>Irrlicht\include\video</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\aabbox3d.h">
      <Filter>Irrlicht\include\core</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\coreutil.h">
      <Filter>Irrlicht\include\core</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\dimension2d.h">
      <Filter>Irrlicht\include\core</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\heapsort.h">
      <Filter>Irrlicht\include\core</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\irrAllocator.h">
      <Filter>Irrlicht\include\core</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\irrArray.h">
      <Filter>Irrlicht\include\core</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\irrList.h">
      <Filter>Irrlicht\include\core</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\irrMap.h">
      <Filter>Irrlicht\include\core</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\irrMath.h">
      <Filter>Irrlicht\include\core</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\irrString.h">
      <Filter>Irrlicht\include\core</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\line2d.h">
      <Filter>Irrlicht\include\core</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\line3d.h">
      <Filter>Irrlicht\include\core</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\matrix4.h">
      <Filter>Irrlicht\include\core</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\plane3d.h">
      <Filter>Irrlicht\include\core</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\position2d.h">
      <Filter>Irrlicht\include\core</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\quaternion.h">
      <Filter>Irrlicht\include\core</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\rect.h">
      <Filter>Irrlicht\include\core</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\triangle3d.h">
      <Filter>Irrlicht\include\core</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\vector2d.h">
      <Filter>Irrlicht\include\core</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\vector3d.h">
      <Filter>Irrlicht\include\core</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\EAttributes.h">
      <Filter>Irrlicht\include\io</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IAttributeExchangingObject.h">
      <Filter>Irrlicht\include\io</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IAttributes.h">
      <Filter>Irrlicht\include\io</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IFileList.h">
      <Filter>Irrlicht\include\io</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IFileSystem.h">
      <Filter>Irrlicht\include\io</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IReadFile.h">
      <Filter>Irrlicht\include\io</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\irrXML.h">
      <Filter>Irrlicht\include\io</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IWriteFile.h">
      <Filter>Irrlicht\include\io</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IXMLReader.h">
      <Filter>Irrlicht\include\io</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IXMLWriter.h">
      <Filter>Irrlicht\include\io</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\path.h">
      <Filter>Irrlicht\include\io</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\CDynamicMeshBuffer.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\CIndexBuffer.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\CMeshBuffer.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\CVertexBuffer.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\ECullingTypes.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\EDebugSceneTypes.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\EMeshWriterEnums.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\EPrimitiveTypes.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\ESceneNodeAnimatorTypes.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\ESceneNodeTypes.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IAnimatedMesh.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IAnimatedMeshMD2.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IAnimatedMeshSceneNode.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IBillboardSceneNode.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\ICameraSceneNode.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IDummyTransformationSceneNode.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\ILightSceneNode.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IMesh.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IMeshBuffer.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IMeshCache.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IMeshLoader.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IMeshManipulator.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IMeshSceneNode.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IMeshWriter.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IMetaTriangleSelector.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IParticleAffector.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IParticleAnimatedMeshSceneNodeEmitter.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IParticleAttractionAffector.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IParticleBoxEmitter.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IParticleCylinderEmitter.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IParticleEmitter.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IParticleFadeOutAffector.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IParticleGravityAffector.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IParticleMeshEmitter.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IParticleRingEmitter.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IParticleRotationAffector.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IParticleSphereEmitter.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IParticleSystemSceneNode.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IQ3LevelMesh.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IQ3Shader.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\ISceneCollisionManager.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\ISceneManager.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\ISceneNode.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\ISceneNodeAnimator.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\ISceneNodeAnimatorCameraFPS.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\ISceneNodeAnimatorCollisionResponse.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\ISceneNodeAnimatorFactory.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\ISceneNodeFactory.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IShadowVolumeSceneNode.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\ISkinnedMesh.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\ITerrainSceneNode.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\ITextSceneNode.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\ITriangleSelector.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IVolumeLightSceneNode.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SAnimatedMesh.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SceneParameters.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SMesh.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SMeshBuffer.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SMeshBufferLightMap.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SMeshBufferTangents.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SParticle.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SSkinMeshBuffer.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\SViewFrustum.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\EGUIAlignment.h">
      <Filter>Irrlicht\include\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\EGUIElementTypes.h">
      <Filter>Irrlicht\include\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\EMessageBoxFlags.h">
      <Filter>Irrlicht\include\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\ICursorControl.h">
      <Filter>Irrlicht\include\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IGUIButton.h">
      <Filter>Irrlicht\include\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IGUICheckbox.h">
      <Filter>Irrlicht\include\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IGUIComboBox.h">
      <Filter>Irrlicht\include\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IGUIContextMenu.h">
      <Filter>Irrlicht\include\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IGUIEditBox.h">
      <Filter>Irrlicht\include\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IGUIElement.h">
      <Filter>Irrlicht\include\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IGUIElementFactory.h">
      <Filter>Irrlicht\include\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IGUIEnvironment.h">
      <Filter>Irrlicht\include\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IGUIFileOpenDialog.h">
      <Filter>Irrlicht\include\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IGUIFont.h">
      <Filter>Irrlicht\include\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IGUIImage.h">
      <Filter>Irrlicht\include\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IGUIImageList.h">
      <Filter>Irrlicht\include\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IGUIInOutFader.h">
      <Filter>Irrlicht\include\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IGUIListBox.h">
      <Filter>Irrlicht\include\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IGUIMeshViewer.h">
      <Filter>Irrlicht\include\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IGUIScrollBar.h">
      <Filter>Irrlicht\include\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IGUISkin.h">
      <Filter>Irrlicht\include\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IGUISpinBox.h">
      <Filter>Irrlicht\include\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IGUISpriteBank.h">
      <Filter>Irrlicht\include\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IGUIStaticText.h">
      <Filter>Irrlicht\include\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IGUITabControl.h">
      <Filter>Irrlicht\include\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IGUIToolbar.h">
      <Filter>Irrlicht\include\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IGUITreeView.h">
      <Filter>Irrlicht\include\gui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IGUIWindow.h">
      <Filter>Irrlicht\include\gui</Filter>
    </ClInclude>
    <ClInclude Include="CVideoModeList.h">
      <Filter>Irrlicht\video</Filter>
    </ClInclude>
    <ClInclude Include="CColorConverter.h">
      <Filter>Irrlicht\video\Null</Filter>
    </ClInclude>
    <ClInclude Include="CFPSCounter.h">
      <Filter>Irrlicht\video\Null</Filter>
    </ClInclude>
    <ClInclude Include="CImage.h">
      <Filter>Irrlicht\video\Null</Filter>
    </ClInclude>
    <ClInclude Include="CNullDriver.h">
      <Filter>Irrlicht\video\Null</Filter>
    </ClInclude>
    <ClInclude Include="IImagePresenter.h">
      <Filter>Irrlicht\video\Null</Filter>
    </ClInclude>
    <ClInclude Include="CImageWriterBMP.h">
      <Filter>Irrlicht\video\Null\Writer</Filter>
    </ClInclude>
    <ClInclude Include="CImageWriterJPG.h">
      <Filter>Irrlicht\video\Null\Writer</Filter>
    </ClInclude>
    <ClInclude Include="CImageWriterPCX.h">
      <Filter>Irrlicht\video\Null\Writer</Filter>
    </ClInclude>
    <ClInclude Include="CImageWriterPNG.h">
      <Filter>Irrlicht\video\Null\Writer</Filter>
    </ClInclude>
    <ClInclude Include="CImageWriterPPM.h">
      <Filter>Irrlicht\video\Null\Writer</Filter>
    </ClInclude>
    <ClInclude Include="CImageWriterPSD.h">
      <Filter>Irrlicht\video\Null\Writer</Filter>
    </ClInclude>
    <ClInclude Include="CImageWriterTGA.h">
      <Filter>Irrlicht\video\Null\Writer</Filter>
    </ClInclude>
    <ClInclude Include="CImageLoaderBMP.h">
      <Filter>Irrlicht\video\Null\Loader</Filter>
    </ClInclude>
    <ClInclude Include="CImageLoaderDDS.h">
      <Filter>Irrlicht\video\Null\Loader</Filter>
    </ClInclude>
    <ClInclude Include="CImageLoaderJPG.h">
      <Filter>Irrlicht\video\Null\Loader</Filter>
    </ClInclude>
    <ClInclude Include="CImageLoaderPCX.h">
      <Filter>Irrlicht\video\Null\Loader</Filter>
    </ClInclude>
    <ClInclude Include="CImageLoaderPNG.h">
      <Filter>Irrlicht\video\Null\Loader</Filter>
    </ClInclude>
    <ClInclude Include="CImageLoaderPPM.h">
      <Filter>Irrlicht\video\Null\Loader</Filter>
    </ClInclude>
    <ClInclude Include="CImageLoaderPSD.h">
      <Filter>Irrlicht\video\Null\Loader</Filter>
    </ClInclude>
    <ClInclude Include="CImageLoaderRGB.h">
      <Filter>Irrlicht\video\Null\Loader</Filter>
    </ClInclude>
    <ClInclude Include="CImageLoaderTGA.h">
      <Filter>Irrlicht\video\Null\Loader</Filter>
    </ClInclude>
    <ClInclude Include="CImageLoaderWAL.h">
      <Filter>Irrlicht\video\Null\Loader</Filter>
    </ClInclude>
    <ClInclude Include="CD3D9Driver.h">
      <Filter>Irrlicht\video\Direct3D9</Filter>
    </ClInclude>
    <ClInclude Include="CD3D9HLSLMaterialRenderer.h">
      <Filter>Irrlicht\video\Direct3D9</Filter>
    </ClInclude>
    <ClInclude Include="CD3D9MaterialRenderer.h">
      <Filter>Irrlicht\video\Direct3D9</Filter>
    </ClInclude>
    <ClInclude Include="CD3D9NormalMapRenderer.h">
      <Filter>Irrlicht\video\Direct3D9</Filter>
    </ClInclude>
    <ClInclude Include="CD3D9ParallaxMapRenderer.h">
      <Filter>Irrlicht\video\Direct3D9</Filter>
    </ClInclude>
    <ClInclude Include="CD3D9ShaderMaterialRenderer.h">
      <Filter>Irrlicht\video\Direct3D9</Filter>
    </ClInclude>
    <ClInclude Include="CD3D9Texture.h">
      <Filter>Irrlicht\video\Direct3D9</Filter>
    </ClInclude>
    <ClInclude Include="CLogger.h">
      <Filter>Irrlicht\irr</Filter>
    </ClInclude>
    <ClInclude Include="COSOperator.h">
      <Filter>Irrlicht\irr</Filter>
    </ClInclude>
    <ClInclude Include="CTimer.h">
      <Filter>Irrlicht\irr</Filter>
    </ClInclude>
    <ClInclude Include="os.h">
      <Filter>Irrlicht\irr</Filter>
    </ClInclude>
    <ClInclude Include="CIrrDeviceStub.h">
      <Filter>Irrlicht\irr\device</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IAnimatedMeshMD3.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\ISceneLoader.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\EDriverFeatures.h">
      <Filter>Irrlicht\include\video</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IRandomizer.h">
      <Filter>Irrlicht\include</Filter>
    </ClInclude>
    <ClInclude Include="CD3D9HardwareBuffer.h">
      <Filter>Irrlicht\video\Direct3D9</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\driverChoice.h">
      <Filter>Irrlicht\include</Filter>
    </ClInclude>
    <ClInclude Include="resource.h">
      <Filter>Irrlicht\doc</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\IrrCompileConfig.h" />
    <ClInclude Include="CColladaMeshWriter.h">
      <Filter>Irrlicht\writers</Filter>
    </ClInclude>
    <ClInclude Include="CIrrMeshWriter.h">
      <Filter>Irrlicht\writers</Filter>
    </ClInclude>
    <ClInclude Include="COBJMeshWriter.h">
      <Filter>Irrlicht\writers</Filter>
    </ClInclude>
    <ClInclude Include="CPLYMeshWriter.h">
      <Filter>Irrlicht\writers</Filter>
    </ClInclude>
    <ClInclude Include="CSTLMeshWriter.h">
      <Filter>Irrlicht\writers</Filter>
    </ClInclude>
    <ClInclude Include="BuiltInFont.h">
      <Filter>Irrlicht\gui</Filter>
    </ClInclude>
    <ClInclude Include="CDefaultGUIElementFactory.h">
      <Filter>Irrlicht\gui</Filter>
    </ClInclude>
    <ClInclude Include="CGUIButton.h">
      <Filter>Irrlicht\gui</Filter>
    </ClInclude>
    <ClInclude Include="CGUICheckbox.h">
      <Filter>Irrlicht\gui</Filter>
    </ClInclude>
    <ClInclude Include="CGUIColorSelectDialog.h">
      <Filter>Irrlicht\gui</Filter>
    </ClInclude>
    <ClInclude Include="CGUIComboBox.h">
      <Filter>Irrlicht\gui</Filter>
    </ClInclude>
    <ClInclude Include="CGUIContextMenu.h">
      <Filter>Irrlicht\gui</Filter>
    </ClInclude>
    <ClInclude Include="CGUIEditBox.h">
      <Filter>Irrlicht\gui</Filter>
    </ClInclude>
    <ClInclude Include="CGUIEnvironment.h">
      <Filter>Irrlicht\gui</Filter>
    </ClInclude>
    <ClInclude Include="CGUIFileOpenDialog.h">
      <Filter>Irrlicht\gui</Filter>
    </ClInclude>
    <ClInclude Include="CGUIFont.h">
      <Filter>Irrlicht\gui</Filter>
    </ClInclude>
    <ClInclude Include="CGUIImage.h">
      <Filter>Irrlicht\gui</Filter>
    </ClInclude>
    <ClInclude Include="CGUIImageList.h">
      <Filter>Irrlicht\gui</Filter>
    </ClInclude>
    <ClInclude Include="CGUIInOutFader.h">
      <Filter>Irrlicht\gui</Filter>
    </ClInclude>
    <ClInclude Include="CGUIListBox.h">
      <Filter>Irrlicht\gui</Filter>
    </ClInclude>
    <ClInclude Include="CGUIMenu.h">
      <Filter>Irrlicht\gui</Filter>
    </ClInclude>
    <ClInclude Include="CGUIMeshViewer.h">
      <Filter>Irrlicht\gui</Filter>
    </ClInclude>
    <ClInclude Include="CGUIMessageBox.h">
      <Filter>Irrlicht\gui</Filter>
    </ClInclude>
    <ClInclude Include="CGUIModalScreen.h">
      <Filter>Irrlicht\gui</Filter>
    </ClInclude>
    <ClInclude Include="CGUIScrollBar.h">
      <Filter>Irrlicht\gui</Filter>
    </ClInclude>
    <ClInclude Include="CGUISkin.h">
      <Filter>Irrlicht\gui</Filter>
    </ClInclude>
    <ClInclude Include="CGUISpinBox.h">
      <Filter>Irrlicht\gui</Filter>
    </ClInclude>
    <ClInclude Include="CGUISpriteBank.h">
      <Filter>Irrlicht\gui</Filter>
    </ClInclude>
    <ClInclude Include="CGUIStaticText.h">
      <Filter>Irrlicht\gui</Filter>
    </ClInclude>
    <ClInclude Include="CGUITabControl.h">
      <Filter>Irrlicht\gui</Filter>
    </ClInclude>
    <ClInclude Include="CGUITable.h">
      <Filter>Irrlicht\gui</Filter>
    </ClInclude>
    <ClInclude Include="CGUIToolBar.h">
      <Filter>Irrlicht\gui</Filter>
    </ClInclude>
    <ClInclude Include="CGUITreeView.h">
      <Filter>Irrlicht\gui</Filter>
    </ClInclude>
    <ClInclude Include="CGUIWindow.h">
      <Filter>Irrlicht\gui</Filter>
    </ClInclude>
    <ClInclude Include="CAttributeImpl.h">
      <Filter>Irrlicht\io</Filter>
    </ClInclude>
    <ClInclude Include="CAttributes.h">
      <Filter>Irrlicht\io</Filter>
    </ClInclude>
    <ClInclude Include="CFileList.h">
      <Filter>Irrlicht\io</Filter>
    </ClInclude>
    <ClInclude Include="CFileSystem.h">
      <Filter>Irrlicht\io</Filter>
    </ClInclude>
    <ClInclude Include="CLimitReadFile.h">
      <Filter>Irrlicht\io</Filter>
    </ClInclude>
    <ClInclude Include="CMemoryFile.h">
      <Filter>Irrlicht\io</Filter>
    </ClInclude>
    <ClInclude Include="CMountPointReader.h">
      <Filter>Irrlicht\io</Filter>
    </ClInclude>
    <ClInclude Include="CNPKReader.h">
      <Filter>Irrlicht\io</Filter>
    </ClInclude>
    <ClInclude Include="CPakReader.h">
      <Filter>Irrlicht\io</Filter>
    </ClInclude>
    <ClInclude Include="CReadFile.h">
      <Filter>Irrlicht\io</Filter>
    </ClInclude>
    <ClInclude Include="CTarReader.h">
      <Filter>Irrlicht\io</Filter>
    </ClInclude>
    <ClInclude Include="CWADReader.h">
      <Filter>Irrlicht\io</Filter>
    </ClInclude>
    <ClInclude Include="CWriteFile.h">
      <Filter>Irrlicht\io</Filter>
    </ClInclude>
    <ClInclude Include="CXMLReader.h">
      <Filter>Irrlicht\io</Filter>
    </ClInclude>
    <ClInclude Include="CXMLReaderImpl.h">
      <Filter>Irrlicht\io</Filter>
    </ClInclude>
    <ClInclude Include="CXMLWriter.h">
      <Filter>Irrlicht\io</Filter>
    </ClInclude>
    <ClInclude Include="CZipReader.h">
      <Filter>Irrlicht\io</Filter>
    </ClInclude>
    <ClInclude Include="IAttribute.h">
      <Filter>Irrlicht\io</Filter>
    </ClInclude>
    <ClInclude Include="C3DSMeshFileLoader.h">
      <Filter>Irrlicht\loaders</Filter>
    </ClInclude>
    <ClInclude Include="CAnimatedMeshHalfLife.h">
      <Filter>Irrlicht\loaders</Filter>
    </ClInclude>
    <ClInclude Include="CAnimatedMeshMD2.h">
      <Filter>Irrlicht\loaders</Filter>
    </ClInclude>
    <ClInclude Include="CAnimatedMeshMD3.h">
      <Filter>Irrlicht\loaders</Filter>
    </ClInclude>
    <ClInclude Include="CB3DMeshFileLoader.h">
      <Filter>Irrlicht\loaders</Filter>
    </ClInclude>
    <ClInclude Include="CBSPMeshFileLoader.h">
      <Filter>Irrlicht\loaders</Filter>
    </ClInclude>
    <ClInclude Include="CColladaFileLoader.h">
      <Filter>Irrlicht\loaders</Filter>
    </ClInclude>
    <ClInclude Include="CCSMLoader.h">
      <Filter>Irrlicht\loaders</Filter>
    </ClInclude>
    <ClInclude Include="CDMFLoader.h">
      <Filter>Irrlicht\loaders</Filter>
    </ClInclude>
    <ClInclude Include="CIrrMeshFileLoader.h">
      <Filter>Irrlicht\loaders</Filter>
    </ClInclude>
    <ClInclude Include="CLMTSMeshFileLoader.h">
      <Filter>Irrlicht\loaders</Filter>
    </ClInclude>
    <ClInclude Include="CLWOMeshFileLoader.h">
      <Filter>Irrlicht\loaders</Filter>
    </ClInclude>
    <ClInclude Include="CMD2MeshFileLoader.h">
      <Filter>Irrlicht\loaders</Filter>
    </ClInclude>
    <ClInclude Include="CMD3MeshFileLoader.h">
      <Filter>Irrlicht\loaders</Filter>
    </ClInclude>
    <ClInclude Include="CMS3DMeshFileLoader.h">
      <Filter>Irrlicht\loaders</Filter>
    </ClInclude>
    <ClInclude Include="CMY3DHelper.h">
      <Filter>Irrlicht\loaders</Filter>
    </ClInclude>
    <ClInclude Include="CMY3DMeshFileLoader.h">
      <Filter>Irrlicht\loaders</Filter>
    </ClInclude>
    <ClInclude Include="COBJMeshFileLoader.h">
      <Filter>Irrlicht\loaders</Filter>
    </ClInclude>
    <ClInclude Include="COCTLoader.h">
      <Filter>Irrlicht\loaders</Filter>
    </ClInclude>
    <ClInclude Include="COgreMeshFileLoader.h">
      <Filter>Irrlicht\loaders</Filter>
    </ClInclude>
    <ClInclude Include="CPLYMeshFileLoader.h">
      <Filter>Irrlicht\loaders</Filter>
    </ClInclude>
    <ClInclude Include="CQ3LevelMesh.h">
      <Filter>Irrlicht\loaders</Filter>
    </ClInclude>
    <ClInclude Include="CSceneLoaderIrr.h">
      <Filter>Irrlicht\loaders</Filter>
    </ClInclude>
    <ClInclude Include="CSkinnedMesh.h">
      <Filter>Irrlicht\loaders</Filter>
    </ClInclude>
    <ClInclude Include="CSMFMeshFileLoader.h">
      <Filter>Irrlicht\loaders</Filter>
    </ClInclude>
    <ClInclude Include="CSTLMeshFileLoader.h">
      <Filter>Irrlicht\loaders</Filter>
    </ClInclude>
    <ClInclude Include="CXMeshFileLoader.h">
      <Filter>Irrlicht\loaders</Filter>
    </ClInclude>
    <ClInclude Include="dmfsupport.h">
      <Filter>Irrlicht\loaders</Filter>
    </ClInclude>
    <ClInclude Include="CParticleAnimatedMeshSceneNodeEmitter.h">
      <Filter>Irrlicht\particleSystem</Filter>
    </ClInclude>
    <ClInclude Include="CParticleAttractionAffector.h">
      <Filter>Irrlicht\particleSystem</Filter>
    </ClInclude>
    <ClInclude Include="CParticleBoxEmitter.h">
      <Filter>Irrlicht\particleSystem</Filter>
    </ClInclude>
    <ClInclude Include="CParticleCylinderEmitter.h">
      <Filter>Irrlicht\particleSystem</Filter>
    </ClInclude>
    <ClInclude Include="CParticleFadeOutAffector.h">
      <Filter>Irrlicht\particleSystem</Filter>
    </ClInclude>
    <ClInclude Include="CParticleGravityAffector.h">
      <Filter>Irrlicht\particleSystem</Filter>
    </ClInclude>
    <ClInclude Include="CParticleMeshEmitter.h">
      <Filter>Irrlicht\particleSystem</Filter>
    </ClInclude>
    <ClInclude Include="CParticlePointEmitter.h">
      <Filter>Irrlicht\particleSystem</Filter>
    </ClInclude>
    <ClInclude Include="CParticleRingEmitter.h">
      <Filter>Irrlicht\particleSystem</Filter>
    </ClInclude>
    <ClInclude Include="CParticleRotationAffector.h">
      <Filter>Irrlicht\particleSystem</Filter>
    </ClInclude>
    <ClInclude Include="CParticleScaleAffector.h">
      <Filter>Irrlicht\particleSystem</Filter>
    </ClInclude>
    <ClInclude Include="CParticleSphereEmitter.h">
      <Filter>Irrlicht\particleSystem</Filter>
    </ClInclude>
    <ClInclude Include="CParticleSystemSceneNode.h">
      <Filter>Irrlicht\particleSystem</Filter>
    </ClInclude>
    <ClInclude Include="CDefaultSceneNodeAnimatorFactory.h">
      <Filter>Irrlicht\scene</Filter>
    </ClInclude>
    <ClInclude Include="CDefaultSceneNodeFactory.h">
      <Filter>Irrlicht\scene</Filter>
    </ClInclude>
    <ClInclude Include="CGeometryCreator.h">
      <Filter>Irrlicht\scene</Filter>
    </ClInclude>
    <ClInclude Include="CMeshCache.h">
      <Filter>Irrlicht\scene</Filter>
    </ClInclude>
    <ClInclude Include="CMeshManipulator.h">
      <Filter>Irrlicht\scene</Filter>
    </ClInclude>
    <ClInclude Include="CSceneManager.h">
      <Filter>Irrlicht\scene</Filter>
    </ClInclude>
    <ClInclude Include="Octree.h">
      <Filter>Irrlicht\scene</Filter>
    </ClInclude>
    <ClInclude Include="CAnimatedMeshSceneNode.h">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClInclude>
    <ClInclude Include="CBillboardSceneNode.h">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClInclude>
    <ClInclude Include="CBoneSceneNode.h">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClInclude>
    <ClInclude Include="CCameraSceneNode.h">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClInclude>
    <ClInclude Include="CCubeSceneNode.h">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClInclude>
    <ClInclude Include="CDummyTransformationSceneNode.h">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClInclude>
    <ClInclude Include="CEmptySceneNode.h">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClInclude>
    <ClInclude Include="CLightSceneNode.h">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClInclude>
    <ClInclude Include="CMeshSceneNode.h">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClInclude>
    <ClInclude Include="COctreeSceneNode.h">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClInclude>
    <ClInclude Include="CQuake3ShaderSceneNode.h">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClInclude>
    <ClInclude Include="CShadowVolumeSceneNode.h">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClInclude>
    <ClInclude Include="CSkyBoxSceneNode.h">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClInclude>
    <ClInclude Include="CSkyDomeSceneNode.h">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClInclude>
    <ClInclude Include="CSphereSceneNode.h">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClInclude>
    <ClInclude Include="CTerrainSceneNode.h">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClInclude>
    <ClInclude Include="CTextSceneNode.h">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClInclude>
    <ClInclude Include="CVolumeLightSceneNode.h">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClInclude>
    <ClInclude Include="CWaterSurfaceSceneNode.h">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClInclude>
    <ClInclude Include="CMetaTriangleSelector.h">
      <Filter>Irrlicht\scene\collision</Filter>
    </ClInclude>
    <ClInclude Include="COctreeTriangleSelector.h">
      <Filter>Irrlicht\scene\collision</Filter>
    </ClInclude>
    <ClInclude Include="CSceneCollisionManager.h">
      <Filter>Irrlicht\scene\collision</Filter>
    </ClInclude>
    <ClInclude Include="CTerrainTriangleSelector.h">
      <Filter>Irrlicht\scene\collision</Filter>
    </ClInclude>
    <ClInclude Include="CTriangleBBSelector.h">
      <Filter>Irrlicht\scene\collision</Filter>
    </ClInclude>
    <ClInclude Include="CTriangleSelector.h">
      <Filter>Irrlicht\scene\collision</Filter>
    </ClInclude>
    <ClInclude Include="CSceneNodeAnimatorCameraFPS.h">
      <Filter>Irrlicht\scene\animators</Filter>
    </ClInclude>
    <ClInclude Include="CSceneNodeAnimatorCollisionResponse.h">
      <Filter>Irrlicht\scene\animators</Filter>
    </ClInclude>
    <ClInclude Include="CSceneNodeAnimatorDelete.h">
      <Filter>Irrlicht\scene\animators</Filter>
    </ClInclude>
    <ClInclude Include="CSceneNodeAnimatorFlyCircle.h">
      <Filter>Irrlicht\scene\animators</Filter>
    </ClInclude>
    <ClInclude Include="CSceneNodeAnimatorFlyStraight.h">
      <Filter>Irrlicht\scene\animators</Filter>
    </ClInclude>
    <ClInclude Include="CSceneNodeAnimatorFollowSpline.h">
      <Filter>Irrlicht\scene\animators</Filter>
    </ClInclude>
    <ClInclude Include="CSceneNodeAnimatorRotation.h">
      <Filter>Irrlicht\scene\animators</Filter>
    </ClInclude>
    <ClInclude Include="CSceneNodeAnimatorTexture.h">
      <Filter>Irrlicht\scene\animators</Filter>
    </ClInclude>
    <ClInclude Include="COpenGLParallaxMapRenderer.h">
      <Filter>Irrlicht\video\OpenGL</Filter>
    </ClInclude>
    <ClInclude Include="COpenGLShaderMaterialRenderer.h">
      <Filter>Irrlicht\video\OpenGL</Filter>
    </ClInclude>
    <ClInclude Include="COpenGLSLMaterialRenderer.h">
      <Filter>Irrlicht\video\OpenGL</Filter>
    </ClInclude>
    <ClInclude Include="COpenGLTexture.h">
      <Filter>Irrlicht\video\OpenGL</Filter>
    </ClInclude>
    <ClInclude Include="COpenGLCgMaterialRenderer.h">
      <Filter>Irrlicht\video\OpenGL</Filter>
    </ClInclude>
    <ClInclude Include="COpenGLDriver.h">
      <Filter>Irrlicht\video\OpenGL</Filter>
    </ClInclude>
    <ClInclude Include="COpenGLExtensionHandler.h">
      <Filter>Irrlicht\video\OpenGL</Filter>
    </ClInclude>
    <ClInclude Include="COpenGLMaterialRenderer.h">
      <Filter>Irrlicht\video\OpenGL</Filter>
    </ClInclude>
    <ClInclude Include="COpenGLNormalMapRenderer.h">
      <Filter>Irrlicht\video\OpenGL</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\VkDefineShareWithShader.h">
      <Filter>Vulkan</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\VkDriver.h">
      <Filter>Vulkan</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\VkFixedFunctionMaterialRenderer.h">
      <Filter>Vulkan</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\VkHardwareBuffer.h">
      <Filter>Vulkan</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\VkMaterialRenderer.h">
      <Filter>Vulkan</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\VkTexture.h">
      <Filter>Vulkan</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\VkVertexDeclaration.h">
      <Filter>Vulkan</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\VkShaderMan\VkFxBase.h">
      <Filter>Vulkan\shaderman</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\VkShaderMan\VkFxConstantBuffer.h">
      <Filter>Vulkan\shaderman</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\base\VulkanDevice.hpp">
      <Filter>Vulkan\base</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\base\VulkanTexture.hpp">
      <Filter>Vulkan\base</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\base\VulkanTools.h">
      <Filter>Vulkan\base</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\base\VulkanBuffer.hpp">
      <Filter>Vulkan\base</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\base\VulkanDebug.h">
      <Filter>Vulkan\base</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\base\VulkanSwapChain.hpp">
      <Filter>Vulkan\base</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\VkShaderMan\VkFxDescriptorSetManager.h">
      <Filter>Vulkan\shaderman</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\imgui\imgui_internal.h">
      <Filter>imgui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\imgui\imstb_rectpack.h">
      <Filter>imgui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\imgui\imstb_textedit.h">
      <Filter>imgui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\imgui\imstb_truetype.h">
      <Filter>imgui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\imgui\imconfig.h">
      <Filter>imgui</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\imgui\imgui.h">
      <Filter>imgui</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\base\VulkanUIOverlay.h">
      <Filter>Vulkan\base</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\VkDriverBase.h">
      <Filter>Vulkan</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\VkHeader.h" />
    <ClInclude Include="CImageLoaderWIC.h">
      <Filter>Irrlicht\video\Null\Loader</Filter>
    </ClInclude>
    <ClInclude Include="CImageLoaderSTB.h">
      <Filter>Irrlicht\video\Null\Loader</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\DefineShareWithShader.h">
      <Filter>Vulkan</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\vulkanRenderPass.h">
      <Filter>Vulkan</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\VkMr2D.h">
      <Filter>Vulkan</Filter>
    </ClInclude>
    <ClInclude Include="CImageWriterSTB.h">
      <Filter>Irrlicht\video\Null\Writer</Filter>
    </ClInclude>
    <ClInclude Include="CSceneNodeAnimatorCameraTouchControl.h">
      <Filter>Irrlicht\scene\animators</Filter>
    </ClInclude>
    <ClInclude Include="..\..\include\ISceneNodeAnimatorCameraTouchControl.h">
      <Filter>Irrlicht\include\scene</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\VkShaderMan\VkFxShaderRunner.h">
      <Filter>Vulkan\shaderman</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\VkMrFF_MMD.h">
      <Filter>Vulkan</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\VkMrFF_SSAO.h">
      <Filter>Vulkan</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\base\threadpool.hpp">
      <Filter>Vulkan\base</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\ImGuizmo\ImCurveEdit.h">
      <Filter>imgui\Inguizmo</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\ImGuizmo\ImGradient.h">
      <Filter>imgui\Inguizmo</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\ImGuizmo\ImGuizmo.h">
      <Filter>imgui\Inguizmo</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\ImGuizmo\ImZoomSlider.h">
      <Filter>imgui\Inguizmo</Filter>
    </ClInclude>
    <ClInclude Include="..\..\external\ImGuizmo\ImSequencer.h">
      <Filter>imgui\Inguizmo</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\VkRaytracing.h">
      <Filter>Vulkan</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\VkRaytracingSceneExample.h">
      <Filter>Vulkan</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\VkRaytracingSceneManager.h">
      <Filter>Vulkan</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\VkRaytracingShaders.h">
      <Filter>Vulkan</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\VkRaytracingDemo.h">
      <Filter>Vulkan</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\VkRaytracingPipeline.h">
      <Filter>Vulkan</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\VkAccelerationStructure.h">
      <Filter>Vulkan</Filter>
    </ClInclude>
    <ClInclude Include="VulkanRenderer\shader\RaytracingShared.h">
      <Filter>Vulkan</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="CVideoModeList.cpp">
      <Filter>Irrlicht\video</Filter>
    </ClCompile>
    <ClCompile Include="CColorConverter.cpp">
      <Filter>Irrlicht\video\Null</Filter>
    </ClCompile>
    <ClCompile Include="CFPSCounter.cpp">
      <Filter>Irrlicht\video\Null</Filter>
    </ClCompile>
    <ClCompile Include="CImage.cpp">
      <Filter>Irrlicht\video\Null</Filter>
    </ClCompile>
    <ClCompile Include="CNullDriver.cpp">
      <Filter>Irrlicht\video\Null</Filter>
    </ClCompile>
    <ClCompile Include="CImageWriterBMP.cpp">
      <Filter>Irrlicht\video\Null\Writer</Filter>
    </ClCompile>
    <ClCompile Include="CImageWriterJPG.cpp">
      <Filter>Irrlicht\video\Null\Writer</Filter>
    </ClCompile>
    <ClCompile Include="CImageWriterPCX.cpp">
      <Filter>Irrlicht\video\Null\Writer</Filter>
    </ClCompile>
    <ClCompile Include="CImageWriterPNG.cpp">
      <Filter>Irrlicht\video\Null\Writer</Filter>
    </ClCompile>
    <ClCompile Include="CImageWriterPPM.cpp">
      <Filter>Irrlicht\video\Null\Writer</Filter>
    </ClCompile>
    <ClCompile Include="CImageWriterPSD.cpp">
      <Filter>Irrlicht\video\Null\Writer</Filter>
    </ClCompile>
    <ClCompile Include="CImageWriterTGA.cpp">
      <Filter>Irrlicht\video\Null\Writer</Filter>
    </ClCompile>
    <ClCompile Include="CImageLoaderBMP.cpp">
      <Filter>Irrlicht\video\Null\Loader</Filter>
    </ClCompile>
    <ClCompile Include="CImageLoaderDDS.cpp">
      <Filter>Irrlicht\video\Null\Loader</Filter>
    </ClCompile>
    <ClCompile Include="CImageLoaderJPG.cpp">
      <Filter>Irrlicht\video\Null\Loader</Filter>
    </ClCompile>
    <ClCompile Include="CImageLoaderPCX.cpp">
      <Filter>Irrlicht\video\Null\Loader</Filter>
    </ClCompile>
    <ClCompile Include="CImageLoaderPNG.cpp">
      <Filter>Irrlicht\video\Null\Loader</Filter>
    </ClCompile>
    <ClCompile Include="CImageLoaderPPM.cpp">
      <Filter>Irrlicht\video\Null\Loader</Filter>
    </ClCompile>
    <ClCompile Include="CImageLoaderPSD.cpp">
      <Filter>Irrlicht\video\Null\Loader</Filter>
    </ClCompile>
    <ClCompile Include="CImageLoaderRGB.cpp">
      <Filter>Irrlicht\video\Null\Loader</Filter>
    </ClCompile>
    <ClCompile Include="CImageLoaderTGA.cpp">
      <Filter>Irrlicht\video\Null\Loader</Filter>
    </ClCompile>
    <ClCompile Include="CImageLoaderWAL.cpp">
      <Filter>Irrlicht\video\Null\Loader</Filter>
    </ClCompile>
    <ClCompile Include="CD3D9Driver.cpp">
      <Filter>Irrlicht\video\Direct3D9</Filter>
    </ClCompile>
    <ClCompile Include="CD3D9HLSLMaterialRenderer.cpp">
      <Filter>Irrlicht\video\Direct3D9</Filter>
    </ClCompile>
    <ClCompile Include="CD3D9NormalMapRenderer.cpp">
      <Filter>Irrlicht\video\Direct3D9</Filter>
    </ClCompile>
    <ClCompile Include="CD3D9ParallaxMapRenderer.cpp">
      <Filter>Irrlicht\video\Direct3D9</Filter>
    </ClCompile>
    <ClCompile Include="CD3D9ShaderMaterialRenderer.cpp">
      <Filter>Irrlicht\video\Direct3D9</Filter>
    </ClCompile>
    <ClCompile Include="CD3D9Texture.cpp">
      <Filter>Irrlicht\video\Direct3D9</Filter>
    </ClCompile>
    <ClCompile Include="CLogger.cpp">
      <Filter>Irrlicht\irr</Filter>
    </ClCompile>
    <ClCompile Include="COSOperator.cpp">
      <Filter>Irrlicht\irr</Filter>
    </ClCompile>
    <ClCompile Include="Irrlicht.cpp">
      <Filter>Irrlicht\irr</Filter>
    </ClCompile>
    <ClCompile Include="os.cpp">
      <Filter>Irrlicht\irr</Filter>
    </ClCompile>
    <ClCompile Include="CIrrDeviceStub.cpp">
      <Filter>Irrlicht\irr\device</Filter>
    </ClCompile>
    <ClCompile Include="CD3D9HardwareBuffer.cpp">
      <Filter>Irrlicht\video\Direct3D9</Filter>
    </ClCompile>
    <ClCompile Include="CColladaMeshWriter.cpp">
      <Filter>Irrlicht\writers</Filter>
    </ClCompile>
    <ClCompile Include="CIrrMeshWriter.cpp">
      <Filter>Irrlicht\writers</Filter>
    </ClCompile>
    <ClCompile Include="COBJMeshWriter.cpp">
      <Filter>Irrlicht\writers</Filter>
    </ClCompile>
    <ClCompile Include="CPLYMeshWriter.cpp">
      <Filter>Irrlicht\writers</Filter>
    </ClCompile>
    <ClCompile Include="CSTLMeshWriter.cpp">
      <Filter>Irrlicht\writers</Filter>
    </ClCompile>
    <ClCompile Include="CDefaultGUIElementFactory.cpp">
      <Filter>Irrlicht\gui</Filter>
    </ClCompile>
    <ClCompile Include="CGUIButton.cpp">
      <Filter>Irrlicht\gui</Filter>
    </ClCompile>
    <ClCompile Include="CGUICheckbox.cpp">
      <Filter>Irrlicht\gui</Filter>
    </ClCompile>
    <ClCompile Include="CGUIColorSelectDialog.cpp">
      <Filter>Irrlicht\gui</Filter>
    </ClCompile>
    <ClCompile Include="CGUIComboBox.cpp">
      <Filter>Irrlicht\gui</Filter>
    </ClCompile>
    <ClCompile Include="CGUIContextMenu.cpp">
      <Filter>Irrlicht\gui</Filter>
    </ClCompile>
    <ClCompile Include="CGUIEditBox.cpp">
      <Filter>Irrlicht\gui</Filter>
    </ClCompile>
    <ClCompile Include="CGUIEnvironment.cpp">
      <Filter>Irrlicht\gui</Filter>
    </ClCompile>
    <ClCompile Include="CGUIFileOpenDialog.cpp">
      <Filter>Irrlicht\gui</Filter>
    </ClCompile>
    <ClCompile Include="CGUIFont.cpp">
      <Filter>Irrlicht\gui</Filter>
    </ClCompile>
    <ClCompile Include="CGUIImage.cpp">
      <Filter>Irrlicht\gui</Filter>
    </ClCompile>
    <ClCompile Include="CGUIImageList.cpp">
      <Filter>Irrlicht\gui</Filter>
    </ClCompile>
    <ClCompile Include="CGUIInOutFader.cpp">
      <Filter>Irrlicht\gui</Filter>
    </ClCompile>
    <ClCompile Include="CGUIListBox.cpp">
      <Filter>Irrlicht\gui</Filter>
    </ClCompile>
    <ClCompile Include="CGUIMenu.cpp">
      <Filter>Irrlicht\gui</Filter>
    </ClCompile>
    <ClCompile Include="CGUIMeshViewer.cpp">
      <Filter>Irrlicht\gui</Filter>
    </ClCompile>
    <ClCompile Include="CGUIMessageBox.cpp">
      <Filter>Irrlicht\gui</Filter>
    </ClCompile>
    <ClCompile Include="CGUIModalScreen.cpp">
      <Filter>Irrlicht\gui</Filter>
    </ClCompile>
    <ClCompile Include="CGUIScrollBar.cpp">
      <Filter>Irrlicht\gui</Filter>
    </ClCompile>
    <ClCompile Include="CGUISkin.cpp">
      <Filter>Irrlicht\gui</Filter>
    </ClCompile>
    <ClCompile Include="CGUISpinBox.cpp">
      <Filter>Irrlicht\gui</Filter>
    </ClCompile>
    <ClCompile Include="CGUISpriteBank.cpp">
      <Filter>Irrlicht\gui</Filter>
    </ClCompile>
    <ClCompile Include="CGUIStaticText.cpp">
      <Filter>Irrlicht\gui</Filter>
    </ClCompile>
    <ClCompile Include="CGUITabControl.cpp">
      <Filter>Irrlicht\gui</Filter>
    </ClCompile>
    <ClCompile Include="CGUITable.cpp">
      <Filter>Irrlicht\gui</Filter>
    </ClCompile>
    <ClCompile Include="CGUIToolBar.cpp">
      <Filter>Irrlicht\gui</Filter>
    </ClCompile>
    <ClCompile Include="CGUITreeView.cpp">
      <Filter>Irrlicht\gui</Filter>
    </ClCompile>
    <ClCompile Include="CGUIWindow.cpp">
      <Filter>Irrlicht\gui</Filter>
    </ClCompile>
    <ClCompile Include="CAttributes.cpp">
      <Filter>Irrlicht\io</Filter>
    </ClCompile>
    <ClCompile Include="CFileList.cpp">
      <Filter>Irrlicht\io</Filter>
    </ClCompile>
    <ClCompile Include="CLimitReadFile.cpp">
      <Filter>Irrlicht\io</Filter>
    </ClCompile>
    <ClCompile Include="CMemoryFile.cpp">
      <Filter>Irrlicht\io</Filter>
    </ClCompile>
    <ClCompile Include="CMountPointReader.cpp">
      <Filter>Irrlicht\io</Filter>
    </ClCompile>
    <ClCompile Include="CNPKReader.cpp">
      <Filter>Irrlicht\io</Filter>
    </ClCompile>
    <ClCompile Include="CPakReader.cpp">
      <Filter>Irrlicht\io</Filter>
    </ClCompile>
    <ClCompile Include="CReadFile.cpp">
      <Filter>Irrlicht\io</Filter>
    </ClCompile>
    <ClCompile Include="CTarReader.cpp">
      <Filter>Irrlicht\io</Filter>
    </ClCompile>
    <ClCompile Include="CWADReader.cpp">
      <Filter>Irrlicht\io</Filter>
    </ClCompile>
    <ClCompile Include="CWriteFile.cpp">
      <Filter>Irrlicht\io</Filter>
    </ClCompile>
    <ClCompile Include="CXMLReader.cpp">
      <Filter>Irrlicht\io</Filter>
    </ClCompile>
    <ClCompile Include="CXMLWriter.cpp">
      <Filter>Irrlicht\io</Filter>
    </ClCompile>
    <ClCompile Include="CZipReader.cpp">
      <Filter>Irrlicht\io</Filter>
    </ClCompile>
    <ClCompile Include="irrXML.cpp">
      <Filter>Irrlicht\io</Filter>
    </ClCompile>
    <ClCompile Include="C3DSMeshFileLoader.cpp">
      <Filter>Irrlicht\loaders</Filter>
    </ClCompile>
    <ClCompile Include="CAnimatedMeshHalfLife.cpp">
      <Filter>Irrlicht\loaders</Filter>
    </ClCompile>
    <ClCompile Include="CAnimatedMeshMD2.cpp">
      <Filter>Irrlicht\loaders</Filter>
    </ClCompile>
    <ClCompile Include="CAnimatedMeshMD3.cpp">
      <Filter>Irrlicht\loaders</Filter>
    </ClCompile>
    <ClCompile Include="CB3DMeshFileLoader.cpp">
      <Filter>Irrlicht\loaders</Filter>
    </ClCompile>
    <ClCompile Include="CBSPMeshFileLoader.cpp">
      <Filter>Irrlicht\loaders</Filter>
    </ClCompile>
    <ClCompile Include="CColladaFileLoader.cpp">
      <Filter>Irrlicht\loaders</Filter>
    </ClCompile>
    <ClCompile Include="CCSMLoader.cpp">
      <Filter>Irrlicht\loaders</Filter>
    </ClCompile>
    <ClCompile Include="CDMFLoader.cpp">
      <Filter>Irrlicht\loaders</Filter>
    </ClCompile>
    <ClCompile Include="CIrrMeshFileLoader.cpp">
      <Filter>Irrlicht\loaders</Filter>
    </ClCompile>
    <ClCompile Include="CLMTSMeshFileLoader.cpp">
      <Filter>Irrlicht\loaders</Filter>
    </ClCompile>
    <ClCompile Include="CLWOMeshFileLoader.cpp">
      <Filter>Irrlicht\loaders</Filter>
    </ClCompile>
    <ClCompile Include="CMD2MeshFileLoader.cpp">
      <Filter>Irrlicht\loaders</Filter>
    </ClCompile>
    <ClCompile Include="CMD3MeshFileLoader.cpp">
      <Filter>Irrlicht\loaders</Filter>
    </ClCompile>
    <ClCompile Include="CMS3DMeshFileLoader.cpp">
      <Filter>Irrlicht\loaders</Filter>
    </ClCompile>
    <ClCompile Include="CMY3DMeshFileLoader.cpp">
      <Filter>Irrlicht\loaders</Filter>
    </ClCompile>
    <ClCompile Include="COBJMeshFileLoader.cpp">
      <Filter>Irrlicht\loaders</Filter>
    </ClCompile>
    <ClCompile Include="COCTLoader.cpp">
      <Filter>Irrlicht\loaders</Filter>
    </ClCompile>
    <ClCompile Include="COgreMeshFileLoader.cpp">
      <Filter>Irrlicht\loaders</Filter>
    </ClCompile>
    <ClCompile Include="CPLYMeshFileLoader.cpp">
      <Filter>Irrlicht\loaders</Filter>
    </ClCompile>
    <ClCompile Include="CQ3LevelMesh.cpp">
      <Filter>Irrlicht\loaders</Filter>
    </ClCompile>
    <ClCompile Include="CSceneLoaderIrr.cpp">
      <Filter>Irrlicht\loaders</Filter>
    </ClCompile>
    <ClCompile Include="CSkinnedMesh.cpp">
      <Filter>Irrlicht\loaders</Filter>
    </ClCompile>
    <ClCompile Include="CSMFMeshFileLoader.cpp">
      <Filter>Irrlicht\loaders</Filter>
    </ClCompile>
    <ClCompile Include="CSTLMeshFileLoader.cpp">
      <Filter>Irrlicht\loaders</Filter>
    </ClCompile>
    <ClCompile Include="CXMeshFileLoader.cpp">
      <Filter>Irrlicht\loaders</Filter>
    </ClCompile>
    <ClCompile Include="CParticleAnimatedMeshSceneNodeEmitter.cpp">
      <Filter>Irrlicht\particleSystem</Filter>
    </ClCompile>
    <ClCompile Include="CParticleAttractionAffector.cpp">
      <Filter>Irrlicht\particleSystem</Filter>
    </ClCompile>
    <ClCompile Include="CParticleBoxEmitter.cpp">
      <Filter>Irrlicht\particleSystem</Filter>
    </ClCompile>
    <ClCompile Include="CParticleCylinderEmitter.cpp">
      <Filter>Irrlicht\particleSystem</Filter>
    </ClCompile>
    <ClCompile Include="CParticleFadeOutAffector.cpp">
      <Filter>Irrlicht\particleSystem</Filter>
    </ClCompile>
    <ClCompile Include="CParticleGravityAffector.cpp">
      <Filter>Irrlicht\particleSystem</Filter>
    </ClCompile>
    <ClCompile Include="CParticleMeshEmitter.cpp">
      <Filter>Irrlicht\particleSystem</Filter>
    </ClCompile>
    <ClCompile Include="CParticlePointEmitter.cpp">
      <Filter>Irrlicht\particleSystem</Filter>
    </ClCompile>
    <ClCompile Include="CParticleRingEmitter.cpp">
      <Filter>Irrlicht\particleSystem</Filter>
    </ClCompile>
    <ClCompile Include="CParticleRotationAffector.cpp">
      <Filter>Irrlicht\particleSystem</Filter>
    </ClCompile>
    <ClCompile Include="CParticleScaleAffector.cpp">
      <Filter>Irrlicht\particleSystem</Filter>
    </ClCompile>
    <ClCompile Include="CParticleSphereEmitter.cpp">
      <Filter>Irrlicht\particleSystem</Filter>
    </ClCompile>
    <ClCompile Include="CParticleSystemSceneNode.cpp">
      <Filter>Irrlicht\particleSystem</Filter>
    </ClCompile>
    <ClCompile Include="CDefaultSceneNodeAnimatorFactory.cpp">
      <Filter>Irrlicht\scene</Filter>
    </ClCompile>
    <ClCompile Include="CDefaultSceneNodeFactory.cpp">
      <Filter>Irrlicht\scene</Filter>
    </ClCompile>
    <ClCompile Include="CGeometryCreator.cpp">
      <Filter>Irrlicht\scene</Filter>
    </ClCompile>
    <ClCompile Include="CMeshCache.cpp">
      <Filter>Irrlicht\scene</Filter>
    </ClCompile>
    <ClCompile Include="CMeshManipulator.cpp">
      <Filter>Irrlicht\scene</Filter>
    </ClCompile>
    <ClCompile Include="CSceneManager.cpp">
      <Filter>Irrlicht\scene</Filter>
    </ClCompile>
    <ClCompile Include="CAnimatedMeshSceneNode.cpp">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClCompile>
    <ClCompile Include="CBillboardSceneNode.cpp">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClCompile>
    <ClCompile Include="CBoneSceneNode.cpp">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClCompile>
    <ClCompile Include="CCameraSceneNode.cpp">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClCompile>
    <ClCompile Include="CCubeSceneNode.cpp">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClCompile>
    <ClCompile Include="CDummyTransformationSceneNode.cpp">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClCompile>
    <ClCompile Include="CEmptySceneNode.cpp">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClCompile>
    <ClCompile Include="CLightSceneNode.cpp">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClCompile>
    <ClCompile Include="CMeshSceneNode.cpp">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClCompile>
    <ClCompile Include="COctreeSceneNode.cpp">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClCompile>
    <ClCompile Include="CQuake3ShaderSceneNode.cpp">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClCompile>
    <ClCompile Include="CShadowVolumeSceneNode.cpp">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClCompile>
    <ClCompile Include="CSkyBoxSceneNode.cpp">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClCompile>
    <ClCompile Include="CSkyDomeSceneNode.cpp">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClCompile>
    <ClCompile Include="CSphereSceneNode.cpp">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClCompile>
    <ClCompile Include="CTerrainSceneNode.cpp">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClCompile>
    <ClCompile Include="CTextSceneNode.cpp">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClCompile>
    <ClCompile Include="CVolumeLightSceneNode.cpp">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClCompile>
    <ClCompile Include="CWaterSurfaceSceneNode.cpp">
      <Filter>Irrlicht\scene\sceneNodes</Filter>
    </ClCompile>
    <ClCompile Include="CMetaTriangleSelector.cpp">
      <Filter>Irrlicht\scene\collision</Filter>
    </ClCompile>
    <ClCompile Include="COctreeTriangleSelector.cpp">
      <Filter>Irrlicht\scene\collision</Filter>
    </ClCompile>
    <ClCompile Include="CSceneCollisionManager.cpp">
      <Filter>Irrlicht\scene\collision</Filter>
    </ClCompile>
    <ClCompile Include="CTerrainTriangleSelector.cpp">
      <Filter>Irrlicht\scene\collision</Filter>
    </ClCompile>
    <ClCompile Include="CTriangleBBSelector.cpp">
      <Filter>Irrlicht\scene\collision</Filter>
    </ClCompile>
    <ClCompile Include="CTriangleSelector.cpp">
      <Filter>Irrlicht\scene\collision</Filter>
    </ClCompile>
    <ClCompile Include="CSceneNodeAnimatorCameraFPS.cpp">
      <Filter>Irrlicht\scene\animators</Filter>
    </ClCompile>
    <ClCompile Include="CSceneNodeAnimatorCollisionResponse.cpp">
      <Filter>Irrlicht\scene\animators</Filter>
    </ClCompile>
    <ClCompile Include="CSceneNodeAnimatorDelete.cpp">
      <Filter>Irrlicht\scene\animators</Filter>
    </ClCompile>
    <ClCompile Include="CSceneNodeAnimatorFlyCircle.cpp">
      <Filter>Irrlicht\scene\animators</Filter>
    </ClCompile>
    <ClCompile Include="CSceneNodeAnimatorFlyStraight.cpp">
      <Filter>Irrlicht\scene\animators</Filter>
    </ClCompile>
    <ClCompile Include="CSceneNodeAnimatorFollowSpline.cpp">
      <Filter>Irrlicht\scene\animators</Filter>
    </ClCompile>
    <ClCompile Include="CSceneNodeAnimatorRotation.cpp">
      <Filter>Irrlicht\scene\animators</Filter>
    </ClCompile>
    <ClCompile Include="CSceneNodeAnimatorTexture.cpp">
      <Filter>Irrlicht\scene\animators</Filter>
    </ClCompile>
    <ClCompile Include="leakHunter.cpp">
      <Filter>Irrlicht</Filter>
    </ClCompile>
    <ClCompile Include="CFileSystem.cpp">
      <Filter>Irrlicht\io</Filter>
    </ClCompile>
    <ClCompile Include="CSceneNodeAnimatorFollowSplineRotation.cpp">
      <Filter>Irrlicht\scene</Filter>
    </ClCompile>
    <ClCompile Include="ISceneNode.cpp">
      <Filter>Irrlicht</Filter>
    </ClCompile>
    <ClCompile Include="zlib\gzlib.c">
      <Filter>Irrlicht\3RD\zlib</Filter>
    </ClCompile>
    <ClCompile Include="zlib\gzread.c">
      <Filter>Irrlicht\3RD\zlib</Filter>
    </ClCompile>
    <ClCompile Include="zlib\gzwrite.c">
      <Filter>Irrlicht\3RD\zlib</Filter>
    </ClCompile>
    <ClCompile Include="zlib\infback.c">
      <Filter>Irrlicht\3RD\zlib</Filter>
    </ClCompile>
    <ClCompile Include="zlib\inffast.c">
      <Filter>Irrlicht\3RD\zlib</Filter>
    </ClCompile>
    <ClCompile Include="zlib\inflate.c">
      <Filter>Irrlicht\3RD\zlib</Filter>
    </ClCompile>
    <ClCompile Include="zlib\inftrees.c">
      <Filter>Irrlicht\3RD\zlib</Filter>
    </ClCompile>
    <ClCompile Include="zlib\trees.c">
      <Filter>Irrlicht\3RD\zlib</Filter>
    </ClCompile>
    <ClCompile Include="zlib\uncompr.c">
      <Filter>Irrlicht\3RD\zlib</Filter>
    </ClCompile>
    <ClCompile Include="zlib\zutil.c">
      <Filter>Irrlicht\3RD\zlib</Filter>
    </ClCompile>
    <ClCompile Include="zlib\adler32.c">
      <Filter>Irrlicht\3RD\zlib</Filter>
    </ClCompile>
    <ClCompile Include="zlib\compress.c">
      <Filter>Irrlicht\3RD\zlib</Filter>
    </ClCompile>
    <ClCompile Include="zlib\crc32.c">
      <Filter>Irrlicht\3RD\zlib</Filter>
    </ClCompile>
    <ClCompile Include="zlib\deflate.c">
      <Filter>Irrlicht\3RD\zlib</Filter>
    </ClCompile>
    <ClCompile Include="zlib\gzclose.c">
      <Filter>Irrlicht\3RD\zlib</Filter>
    </ClCompile>
    <ClCompile Include="CIrrDeviceWin32.cpp">
      <Filter>Irrlicht</Filter>
    </ClCompile>
    <ClCompile Include="COpenGLParallaxMapRenderer.cpp">
      <Filter>Irrlicht\video\OpenGL</Filter>
    </ClCompile>
    <ClCompile Include="COpenGLShaderMaterialRenderer.cpp">
      <Filter>Irrlicht\video\OpenGL</Filter>
    </ClCompile>
    <ClCompile Include="COpenGLSLMaterialRenderer.cpp">
      <Filter>Irrlicht\video\OpenGL</Filter>
    </ClCompile>
    <ClCompile Include="COpenGLTexture.cpp">
      <Filter>Irrlicht\video\OpenGL</Filter>
    </ClCompile>
    <ClCompile Include="COpenGLCgMaterialRenderer.cpp">
      <Filter>Irrlicht\video\OpenGL</Filter>
    </ClCompile>
    <ClCompile Include="COpenGLExtensionHandler.cpp">
      <Filter>Irrlicht\video\OpenGL</Filter>
    </ClCompile>
    <ClCompile Include="COpenGLNormalMapRenderer.cpp">
      <Filter>Irrlicht\video\OpenGL</Filter>
    </ClCompile>
    <ClCompile Include="COpenGLDriver.cpp">
      <Filter>Irrlicht\video\OpenGL</Filter>
    </ClCompile>
    <ClCompile Include="VulkanRenderer\VkDriver.cpp">
      <Filter>Vulkan</Filter>
    </ClCompile>
    <ClCompile Include="VulkanRenderer\VkFixedFunctionMaterialRenderer.cpp">
      <Filter>Vulkan</Filter>
    </ClCompile>
    <ClCompile Include="VulkanRenderer\VkHardwareBuffer.cpp">
      <Filter>Vulkan</Filter>
    </ClCompile>
    <ClCompile Include="VulkanRenderer\VkTexture.cpp">
      <Filter>Vulkan</Filter>
    </ClCompile>
    <ClCompile Include="VulkanRenderer\VkVertexDeclaration.cpp">
      <Filter>Vulkan</Filter>
    </ClCompile>
    <ClCompile Include="VulkanRenderer\VkShaderMan\VkFxBase.cpp">
      <Filter>Vulkan\shaderman</Filter>
    </ClCompile>
    <ClCompile Include="VulkanRenderer\base\VulkanTools.cpp">
      <Filter>Vulkan\base</Filter>
    </ClCompile>
    <ClCompile Include="VulkanRenderer\base\VulkanDebug.cpp">
      <Filter>Vulkan\base</Filter>
    </ClCompile>
    <ClCompile Include="VulkanRenderer\base\VulkanBuffer.cpp">
      <Filter>Vulkan\base</Filter>
    </ClCompile>
    <ClCompile Include="VulkanRenderer\VkShaderMan\VkFxDescriptorSetManager.cpp">
      <Filter>Vulkan\shaderman</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\imgui\imgui_draw.cpp">
      <Filter>imgui</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\imgui\imgui_widgets.cpp">
      <Filter>imgui</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\imgui\imgui.cpp">
      <Filter>imgui</Filter>
    </ClCompile>
    <ClCompile Include="VulkanRenderer\base\VulkanUIOverlay.cpp">
      <Filter>Vulkan\base</Filter>
    </ClCompile>
    <ClCompile Include="VulkanRenderer\VkDriverBase.cpp">
      <Filter>Vulkan</Filter>
    </ClCompile>
    <ClCompile Include="CImageLoaderWIC.cpp">
      <Filter>Irrlicht\video\Null\Loader</Filter>
    </ClCompile>
    <ClCompile Include="CImageLoaderSTB.cpp">
      <Filter>Irrlicht\video\Null\Loader</Filter>
    </ClCompile>
    <ClCompile Include="VulkanRenderer\vulkanRenderPass.cpp">
      <Filter>Vulkan</Filter>
    </ClCompile>
    <ClCompile Include="VulkanRenderer\VkMr2D.cpp">
      <Filter>Vulkan</Filter>
    </ClCompile>
    <ClCompile Include="CImageWriterSTB.cpp">
      <Filter>Irrlicht\video\Null\Writer</Filter>
    </ClCompile>
    <ClCompile Include="VulkanRenderer\VkShaderMan\VkFxShaderRunner.cpp">
      <Filter>Vulkan\shaderman</Filter>
    </ClCompile>
    <ClCompile Include="CSceneNodeAnimatorCameraTouchControl.cpp">
      <Filter>Irrlicht\scene\animators</Filter>
    </ClCompile>
    <ClCompile Include="..\..\include\ISceneNodeAnimator.cpp" />
    <ClCompile Include="VulkanRenderer\VkMrFF_MMD.cpp">
      <Filter>Vulkan</Filter>
    </ClCompile>
    <ClCompile Include="VulkanRenderer\VkMrFF_SSAO.cpp">
      <Filter>Vulkan</Filter>
    </ClCompile>
    <ClCompile Include="VulkanRenderer\VkMaterialRenderer.cpp">
      <Filter>Vulkan</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\imgui\imgui_tables.cpp">
      <Filter>imgui</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\imgui\backends\imgui_impl_win32.cpp" />
    <ClCompile Include="..\..\external\imgui\misc\cpp\imgui_stdlib.cpp">
      <Filter>imgui</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\ImGuizmo\ImCurveEdit.cpp">
      <Filter>imgui\Inguizmo</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\ImGuizmo\ImGradient.cpp">
      <Filter>imgui\Inguizmo</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\ImGuizmo\ImSequencer.cpp">
      <Filter>imgui\Inguizmo</Filter>
    </ClCompile>
    <ClCompile Include="..\..\external\ImGuizmo\ImGuizmo.cpp">
      <Filter>imgui\Inguizmo</Filter>
    </ClCompile>
    <ClCompile Include="VulkanRenderer\VkDriverRaytracing.cpp">
      <Filter>Vulkan</Filter>
    </ClCompile>
    <ClCompile Include="VulkanRenderer\VkRaytracingSceneManager.cpp">
      <Filter>Vulkan</Filter>
    </ClCompile>
    <ClCompile Include="VulkanRenderer\VkRaytracingSceneExample.cpp">
      <Filter>Vulkan</Filter>
    </ClCompile>
    <ClCompile Include="VulkanRenderer\VkRaytracing.cpp">
      <Filter>Vulkan</Filter>
    </ClCompile>
    <ClCompile Include="VulkanRenderer\VkRaytracingDemo.cpp">
      <Filter>Vulkan</Filter>
    </ClCompile>
    <ClCompile Include="VulkanRenderer\VkRaytracingPipeline.cpp">
      <Filter>Vulkan</Filter>
    </ClCompile>
    <ClCompile Include="VulkanRenderer\VkRaytracingShaders.cpp">
      <Filter>Vulkan</Filter>
    </ClCompile>
    <ClCompile Include="VulkanRenderer\VkAccelerationStructure.cpp">
      <Filter>Vulkan</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="cpp.hint" />
  </ItemGroup>
</Project>