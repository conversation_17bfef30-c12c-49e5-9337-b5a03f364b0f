#include "VkRaytracingDemo.h"

#ifdef _IRR_COMPILE_WITH_VULKAN_
#if VK_ENABLE_RAYTRACING

#include "VkDriver.h"
#include "VkTexture.h"
#include "VkAccelerationStructure.h"
#include "VkRaytracing.h"
#include "shader/RaytracingShared.h"
#include "os.h"

namespace irr {
namespace video {

VkRaytracingDemo::VkRaytracingDemo(VkDriver* driver)
    : driver(driver), pipeline(nullptr), shaders(nullptr), outputTexture(nullptr),
      skyTexture(nullptr), descriptorSetLayout(VK_NULL_HANDLE), geometryDescriptorSetLayout(VK_NULL_HANDLE),
      descriptorPool(VK_NULL_HANDLE), descriptorSet(VK_NULL_HANDLE), geometryDescriptorSet(VK_NULL_HANDLE),
      testBLAS(nullptr), testTLAS(nullptr), testVertexBuffer(nullptr), testIndexBuffer(nullptr),
      testMaterialIDsBuffer(nullptr), testMaterialsBuffer(nullptr), cameraBuffer(nullptr), isInitialized(false) {
}

VkRaytracingDemo::~VkRaytracingDemo() {
    shutdown();
}

bool VkRaytracingDemo::initialize() {
    if (!driver || !driver->isRaytracingSupported()) {
        return false;
    }

    // Get raytracing shaders from driver
    shaders = driver->getRaytracingShaders();
    if (!shaders || !shaders->areShadersAvailable()) {
        return false;
    }

    // Create descriptor sets first (needed for pipeline layout)
    if (!createDescriptorSets()) {
        return false;
    }

    // Create raytracing pipeline
    if (!createRaytracingPipeline()) {
        return false;
    }

    // Create simple test geometry
    if (!createTestGeometry()) {
        return false;
    }

    // Create output texture (will be resized as needed)
    if (!createOutputTexture(800, 600)) {
        return false;
    }

    // Create sky texture for environment mapping
    if (!createSkyTexture()) {
        return false;
    }

    // Create camera uniform buffer
    if (!createCameraBuffer()) {
        return false;
    }

    isInitialized = true;
    return true;
}

bool VkRaytracingDemo::createRaytracingPipeline() {
    if (!driver || !shaders) {
        return false;
    }

    pipeline = new VkRaytracingPipeline(driver);
    if (!pipeline) {
        return false;
    }

    // Add ray generation shader
    const SRaytracingShaderData* raygenShader = shaders->getShaderData(ERTST_RAY_GENERATION);
    if (raygenShader) {
        SRaytracingShaderInfo raygenInfo;
        raygenInfo.type = ERST_RAY_GENERATION;
        raygenInfo.shaderCode = raygenShader->spirvData;
        raygenInfo.shaderSize = static_cast<u32>(raygenShader->spirvSize);
        raygenInfo.entryPoint = "main";
        raygenInfo.groupIndex = 0;
        
        if (!pipeline->addShader(raygenInfo)) {
            delete pipeline;
            pipeline = nullptr;
            return false;
        }
    }

    // Add primary miss shader
    const SRaytracingShaderData* missShader = shaders->getShaderData(ERTST_MISS_PRIMARY);
    if (missShader) {
        SRaytracingShaderInfo missInfo;
        missInfo.type = ERST_MISS;
        missInfo.shaderCode = missShader->spirvData;
        missInfo.shaderSize = static_cast<u32>(missShader->spirvSize);
        missInfo.entryPoint = "main";
        missInfo.groupIndex = 1;
        
        if (!pipeline->addShader(missInfo)) {
            delete pipeline;
            pipeline = nullptr;
            return false;
        }
    }

    // Add closest hit shader
    const SRaytracingShaderData* chitShader = shaders->getShaderData(ERTST_CLOSEST_HIT_PRIMARY);
    if (chitShader) {
        SRaytracingShaderInfo chitInfo;
        chitInfo.type = ERST_CLOSEST_HIT;
        chitInfo.shaderCode = chitShader->spirvData;
        chitInfo.shaderSize = static_cast<u32>(chitShader->spirvSize);
        chitInfo.entryPoint = "main";
        chitInfo.groupIndex = 2;
        
        if (!pipeline->addShader(chitInfo)) {
            delete pipeline;
            pipeline = nullptr;
            return false;
        }
    }

    // Add shadow miss shader
    const SRaytracingShaderData* shadowMissShader = shaders->getShaderData(ERTST_MISS_SHADOW);
    if (shadowMissShader) {
        SRaytracingShaderInfo shadowMissInfo;
        shadowMissInfo.type = ERST_MISS;
        shadowMissInfo.shaderCode = shadowMissShader->spirvData;
        shadowMissInfo.shaderSize = static_cast<u32>(shadowMissShader->spirvSize);
        shadowMissInfo.entryPoint = "main";
        shadowMissInfo.groupIndex = 3;
        
        if (!pipeline->addShader(shadowMissInfo)) {
            delete pipeline;
            pipeline = nullptr;
            return false;
        }
    }

    // Add shadow closest hit shader
    const SRaytracingShaderData* shadowChitShader = shaders->getShaderData(ERTST_CLOSEST_HIT_SHADOW);
    if (shadowChitShader) {
        SRaytracingShaderInfo shadowChitInfo;
        shadowChitInfo.type = ERST_CLOSEST_HIT;
        shadowChitInfo.shaderCode = shadowChitShader->spirvData;
        shadowChitInfo.shaderSize = static_cast<u32>(shadowChitShader->spirvSize);
        shadowChitInfo.entryPoint = "main";
        shadowChitInfo.groupIndex = 4;
        
        if (!pipeline->addShader(shadowChitInfo)) {
            delete pipeline;
            pipeline = nullptr;
            return false;
        }
    }

    // Set descriptor set layouts before building
    if (descriptorSetLayout != VK_NULL_HANDLE && geometryDescriptorSetLayout != VK_NULL_HANDLE) {
        VkDescriptorSetLayout layouts[2] = { descriptorSetLayout, geometryDescriptorSetLayout };
        pipeline->setDescriptorSetLayouts(layouts, 2);
    }

    // Build the pipeline
    if (!pipeline->build()) {
        delete pipeline;
        pipeline = nullptr;
        return false;
    }

    return true;
}

bool VkRaytracingDemo::createDescriptorSets() {
    if (!driver) {
        return false;
    }

    // Create descriptor set layout for raytracing (Set 0)
    VkDescriptorSetLayoutBinding set0Bindings[4] = {};
    
    // Binding 0: Output image (storage image)
    set0Bindings[0].binding = RT_BINDING_OUTPUT_IMAGE;
    set0Bindings[0].descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_IMAGE;
    set0Bindings[0].descriptorCount = 1;
    set0Bindings[0].stageFlags = VK_SHADER_STAGE_RAYGEN_BIT_KHR;
    
    // Binding 1: Top-level acceleration structure
    set0Bindings[1].binding = RT_BINDING_ACCELERATION_STRUCTURE;
    set0Bindings[1].descriptorType = VK_DESCRIPTOR_TYPE_ACCELERATION_STRUCTURE_KHR;
    set0Bindings[1].descriptorCount = 1;
    set0Bindings[1].stageFlags = VK_SHADER_STAGE_RAYGEN_BIT_KHR | VK_SHADER_STAGE_CLOSEST_HIT_BIT_KHR;
    
    // Binding 2: Camera uniform buffer
    set0Bindings[2].binding = RT_BINDING_CAMERA_UBO;
    set0Bindings[2].descriptorType = VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER;
    set0Bindings[2].descriptorCount = 1;
    set0Bindings[2].stageFlags = VK_SHADER_STAGE_RAYGEN_BIT_KHR;
    
    // Binding 3: Sky texture (environment map)
    set0Bindings[3].binding = 3;
    set0Bindings[3].descriptorType = VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER;
    set0Bindings[3].descriptorCount = 1;
    set0Bindings[3].stageFlags = VK_SHADER_STAGE_MISS_BIT_KHR;

    VkDescriptorSetLayoutCreateInfo set0LayoutInfo = {};
    set0LayoutInfo.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_SET_LAYOUT_CREATE_INFO;
    set0LayoutInfo.bindingCount = 4;
    set0LayoutInfo.pBindings = set0Bindings;

    VkResult result = vkCreateDescriptorSetLayout(driver->getDevice(), &set0LayoutInfo, nullptr, &descriptorSetLayout);
    if (result != VK_SUCCESS) {
        return false;
    }

    // Create descriptor set layout for geometry data (Set 1)
    VkDescriptorSetLayoutBinding set1Bindings[4] = {};
    
    // Binding 0: Vertex attributes buffer
    set1Bindings[0].binding = RT_BINDING_VERTEX_BUFFER;
    set1Bindings[0].descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_BUFFER;
    set1Bindings[0].descriptorCount = 1;
    set1Bindings[0].stageFlags = VK_SHADER_STAGE_CLOSEST_HIT_BIT_KHR;
    
    // Binding 1: Index/faces buffer
    set1Bindings[1].binding = RT_BINDING_INDEX_BUFFER;
    set1Bindings[1].descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_BUFFER;
    set1Bindings[1].descriptorCount = 1;
    set1Bindings[1].stageFlags = VK_SHADER_STAGE_CLOSEST_HIT_BIT_KHR;
    
    // Binding 2: Material IDs buffer
    set1Bindings[2].binding = 2;  // MaterialIDsBuffer at binding 2
    set1Bindings[2].descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_BUFFER;
    set1Bindings[2].descriptorCount = 1;
    set1Bindings[2].stageFlags = VK_SHADER_STAGE_CLOSEST_HIT_BIT_KHR;
    
    // Binding 3: Materials buffer
    set1Bindings[3].binding = RT_BINDING_MATERIAL_BUFFER;
    set1Bindings[3].descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_BUFFER;
    set1Bindings[3].descriptorCount = 1;
    set1Bindings[3].stageFlags = VK_SHADER_STAGE_CLOSEST_HIT_BIT_KHR;

    VkDescriptorSetLayoutCreateInfo set1LayoutInfo = {};
    set1LayoutInfo.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_SET_LAYOUT_CREATE_INFO;
    set1LayoutInfo.bindingCount = 4;
    set1LayoutInfo.pBindings = set1Bindings;

    VkDescriptorSetLayout geometryDescriptorSetLayout;
    result = vkCreateDescriptorSetLayout(driver->getDevice(), &set1LayoutInfo, nullptr, &geometryDescriptorSetLayout);
    if (result != VK_SUCCESS) {
        vkDestroyDescriptorSetLayout(driver->getDevice(), descriptorSetLayout, nullptr);
        descriptorSetLayout = VK_NULL_HANDLE;
        return false;
    }

    // Store the geometry descriptor set layout as a member variable
    this->geometryDescriptorSetLayout = geometryDescriptorSetLayout;

    // Create descriptor pool for both sets
    VkDescriptorPoolSize poolSizes[8] = {};
    // Set 0 descriptors
    poolSizes[0].type = VK_DESCRIPTOR_TYPE_STORAGE_IMAGE;
    poolSizes[0].descriptorCount = 1;
    poolSizes[1].type = VK_DESCRIPTOR_TYPE_ACCELERATION_STRUCTURE_KHR;
    poolSizes[1].descriptorCount = 1;
    poolSizes[2].type = VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER;
    poolSizes[2].descriptorCount = 1;
    poolSizes[3].type = VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER;
    poolSizes[3].descriptorCount = 1;
    // Set 1 descriptors
    poolSizes[4].type = VK_DESCRIPTOR_TYPE_STORAGE_BUFFER;
    poolSizes[4].descriptorCount = 4;  // 4 storage buffers in set 1

    VkDescriptorPoolCreateInfo poolInfo = {};
    poolInfo.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_POOL_CREATE_INFO;
    poolInfo.flags = VK_DESCRIPTOR_POOL_CREATE_FREE_DESCRIPTOR_SET_BIT;
    poolInfo.maxSets = 2;  // Now we need 2 descriptor sets
    poolInfo.poolSizeCount = 5;  // Using 5 pool sizes
    poolInfo.pPoolSizes = poolSizes;

    result = vkCreateDescriptorPool(driver->getDevice(), &poolInfo, nullptr, &descriptorPool);
    if (result != VK_SUCCESS) {
        vkDestroyDescriptorSetLayout(driver->getDevice(), descriptorSetLayout, nullptr);
        vkDestroyDescriptorSetLayout(driver->getDevice(), geometryDescriptorSetLayout, nullptr);
        descriptorSetLayout = VK_NULL_HANDLE;
        return false;
    }

    // Allocate descriptor set for Set 0 (raytracing globals)
    VkDescriptorSetAllocateInfo allocInfo = {};
    allocInfo.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_SET_ALLOCATE_INFO;
    allocInfo.descriptorPool = descriptorPool;
    allocInfo.descriptorSetCount = 1;
    allocInfo.pSetLayouts = &descriptorSetLayout;

    result = vkAllocateDescriptorSets(driver->getDevice(), &allocInfo, &descriptorSet);
    if (result != VK_SUCCESS) {
        vkDestroyDescriptorSetLayout(driver->getDevice(), descriptorSetLayout, nullptr);
        vkDestroyDescriptorSetLayout(driver->getDevice(), geometryDescriptorSetLayout, nullptr);
        descriptorSetLayout = VK_NULL_HANDLE;
        return false;
    }

    // Allocate descriptor set for Set 1 (geometry data)
    allocInfo.pSetLayouts = &geometryDescriptorSetLayout;
    result = vkAllocateDescriptorSets(driver->getDevice(), &allocInfo, &geometryDescriptorSet);
    if (result != VK_SUCCESS) {
        vkDestroyDescriptorSetLayout(driver->getDevice(), descriptorSetLayout, nullptr);
        vkDestroyDescriptorSetLayout(driver->getDevice(), geometryDescriptorSetLayout, nullptr);
        descriptorSetLayout = VK_NULL_HANDLE;
        geometryDescriptorSetLayout = VK_NULL_HANDLE;
        return false;
    }

    return true;
}

bool VkRaytracingDemo::createTestGeometry() {
    if (!driver) {
        return false;
    }

    // Get raytracing manager
    VkRaytracingManager* rtManager = driver->getRaytracingManager();
    if (!rtManager) {
        return false;
    }

    DP(("VkRaytracingDemo: Creating cube and sphere geometry for raytracing"));

    // Create a simple cube geometry
    struct VertexAttribute {
        float position[3];  // xyz position
        float normal[3];    // xyz normal
        float uv[2];        // uv coordinates
        float color[4];     // rgba color
    };

    // Massive cube vertices (8 vertices) - camera is very far away at ~3000 units
    const float cubeSize = 500.0f; // Half-size, so full cube is 1000x1000x1000
    VertexAttribute cubeVertices[8] = {
        // Front face
        { {-cubeSize, -cubeSize,  cubeSize}, {0.0f, 0.0f, 1.0f}, {0.0f, 0.0f}, {0.9f, 0.9f, 0.9f, 1.0f} }, // 0
        { { cubeSize, -cubeSize,  cubeSize}, {0.0f, 0.0f, 1.0f}, {1.0f, 0.0f}, {0.9f, 0.9f, 0.9f, 1.0f} }, // 1
        { { cubeSize,  cubeSize,  cubeSize}, {0.0f, 0.0f, 1.0f}, {1.0f, 1.0f}, {0.9f, 0.9f, 0.9f, 1.0f} }, // 2
        { {-cubeSize,  cubeSize,  cubeSize}, {0.0f, 0.0f, 1.0f}, {0.0f, 1.0f}, {0.9f, 0.9f, 0.9f, 1.0f} }, // 3
        // Back face
        { {-cubeSize, -cubeSize, -cubeSize}, {0.0f, 0.0f, -1.0f}, {1.0f, 0.0f}, {0.9f, 0.9f, 0.9f, 1.0f} }, // 4
        { { cubeSize, -cubeSize, -cubeSize}, {0.0f, 0.0f, -1.0f}, {0.0f, 0.0f}, {0.9f, 0.9f, 0.9f, 1.0f} }, // 5
        { { cubeSize,  cubeSize, -cubeSize}, {0.0f, 0.0f, -1.0f}, {0.0f, 1.0f}, {0.9f, 0.9f, 0.9f, 1.0f} }, // 6
        { {-cubeSize,  cubeSize, -cubeSize}, {0.0f, 0.0f, -1.0f}, {1.0f, 1.0f}, {0.9f, 0.9f, 0.9f, 1.0f} }  // 7
    };

    DP(("VkRaytracingDemo: Creating cube with size %f (full cube: %fx%fx%f)", cubeSize, cubeSize*2, cubeSize*2, cubeSize*2));

    // Cube indices (12 triangles, 36 indices)
    u32 cubeIndices[36] = {
        // Front face
        0, 1, 2,  2, 3, 0,
        // Back face
        4, 7, 6,  6, 5, 4,
        // Left face
        4, 0, 3,  3, 7, 4,
        // Right face
        1, 5, 6,  6, 2, 1,
        // Top face
        3, 2, 6,  6, 7, 3,
        // Bottom face
        4, 5, 1,  1, 0, 4
    };

    // Create vertex buffer (needs storage buffer usage for shader access)
    testVertexBuffer = static_cast<VkHardwareBuffer*>(driver->createHardwareBuffer(
        E_HARDWARE_BUFFER_TYPE::EHBT_STORAGE,  // Use storage buffer type
        E_HARDWARE_BUFFER_ACCESS::EHBA_DEFAULT,
        sizeof(cubeVertices),
        EHBF_VERTEX_ADDITIONAL_BIND, // Additional vertex binding for AS build
        cubeVertices
    ));

    // Create index buffer (needs storage buffer usage for shader access)
    testIndexBuffer = static_cast<VkHardwareBuffer*>(driver->createHardwareBuffer(
        E_HARDWARE_BUFFER_TYPE::EHBT_STORAGE,  // Use storage buffer type
        E_HARDWARE_BUFFER_ACCESS::EHBA_DEFAULT,
        sizeof(cubeIndices),
        EHBF_INDEX_ADDITIONAL_BIND, // Additional index binding for AS build
        cubeIndices
    ));

    if (!testVertexBuffer || !testIndexBuffer) {
        return false;
    }

    // Create material IDs buffer (one material ID per triangle)
    // Cube has 12 triangles (material 0), sphere will have many triangles (material 1)
    u32 totalTriangles = 12 + (sphereIndices.size() / 3); // Cube triangles + sphere triangles
    u32* materialIDs = new u32[totalTriangles];

    // Cube triangles use material 0 (gray mirror)
    for (u32 i = 0; i < 12; i++) {
        materialIDs[i] = 0;
    }

    // Sphere triangles use material 1 (red)
    for (u32 i = 12; i < totalTriangles; i++) {
        materialIDs[i] = 1;
    }
    testMaterialIDsBuffer = static_cast<VkHardwareBuffer*>(driver->createHardwareBuffer(
        E_HARDWARE_BUFFER_TYPE::EHBT_STORAGE,  // Use storage buffer type
        E_HARDWARE_BUFFER_ACCESS::EHBA_DEFAULT,
        totalTriangles * sizeof(u32),
        0, // flags
        materialIDs
    ));

    // Clean up temporary array
    delete[] materialIDs;

    // Create materials buffer (simple material data)
    struct SimpleMaterial {
        float diffuse[4];    // RGBA diffuse color
        float specular[4];   // RGBA specular color
        float emission[4];   // RGBA emission color
        float roughness;     // Roughness value
        float metallic;      // Metallic value
        float padding[2];    // Padding for alignment
    };

    SimpleMaterial materials[2] = {
        // Material 0: Gray mirror-like material for cube
        { {0.9f, 0.9f, 0.9f, 1.0f}, {0.8f, 0.8f, 0.8f, 1.0f}, {0.0f, 0.0f, 0.0f, 1.0f}, 0.1f, 0.8f, {0.0f, 0.0f} },
        // Material 1: Red diffuse material for sphere
        { {0.9f, 0.2f, 0.2f, 1.0f}, {0.1f, 0.1f, 0.1f, 1.0f}, {0.0f, 0.0f, 0.0f, 1.0f}, 0.8f, 0.1f, {0.0f, 0.0f} }
    };

    testMaterialsBuffer = static_cast<VkHardwareBuffer*>(driver->createHardwareBuffer(
        E_HARDWARE_BUFFER_TYPE::EHBT_STORAGE,  // Use storage buffer type
        E_HARDWARE_BUFFER_ACCESS::EHBA_DEFAULT,
        sizeof(materials),
        0, // flags
        materials
    ));

    if (!testMaterialIDsBuffer || !testMaterialsBuffer) {
        DP(("VkRaytracingDemo: Failed to create material buffers"));
        return false;
    }

    DP(("VkRaytracingDemo: Created all buffers - vertex, index, materialIDs, materials"));
    DP(("VkRaytracingDemo: Total triangles: %u (12 cube + %u sphere)", totalTriangles, (sphereIndices.size() / 3)));
    DP(("VkRaytracingDemo: Material 0: Gray mirror (cube), Material 1: Red diffuse (sphere)"));

    // Create BLAS for cube
    testBLAS = rtManager->createBottomLevelAS();
    if (!testBLAS) {
        return false;
    }

    // Build BLAS from the cube mesh
    if (!testBLAS->buildFromMesh(cubeVertices, 8, sizeof(VertexAttribute), cubeIndices, 36, true)) {
        DP(("VkRaytracingDemo: Failed to build BLAS from cube mesh"));
        return false;
    }

    // Get or create top-level AS
    testTLAS = rtManager->getTopLevelAS();
    if (!testTLAS) {
        return false;
    }

    // Add the cube instance to TLAS
    core::matrix4 identity;
    identity.makeIdentity();
    u32 cubeInstanceIndex = testTLAS->addInstance(testBLAS, identity, 0, 0, 0xFF);
    if (cubeInstanceIndex == ~0u) {
        DP(("VkRaytracingDemo: Failed to add cube instance to TLAS"));
        return false;
    }

    // Create a sphere for testing
    DP(("VkRaytracingDemo: Creating sphere geometry"));

    // Generate sphere vertices (simple UV sphere)
    const int sphereSegments = 16;
    const int sphereRings = 8;
    const float sphereRadius = 200.0f; // Smaller sphere so it doesn't completely block cube

    core::array<VertexAttribute> sphereVertices;
    core::array<u32> sphereIndices;

    // Generate sphere vertices
    for (int ring = 0; ring <= sphereRings; ring++) {
        float phi = core::PI * ring / sphereRings;
        float y = cos(phi) * sphereRadius;
        float ringRadius = sin(phi) * sphereRadius;

        for (int seg = 0; seg <= sphereSegments; seg++) {
            float theta = 2.0f * core::PI * seg / sphereSegments;
            float x = cos(theta) * ringRadius;
            float z = sin(theta) * ringRadius;

            VertexAttribute vertex;
            vertex.position[0] = x; // Center sphere horizontally
            vertex.position[1] = y + 1000.0f; // Match camera Y position
            vertex.position[2] = z - 1500.0f; // Put sphere in front of cube (closer to camera)

            // Normal for sphere
            core::vector3df normal(x, y, z);
            normal.normalize();
            vertex.normal[0] = normal.X;
            vertex.normal[1] = normal.Y;
            vertex.normal[2] = normal.Z;

            // UV coordinates
            vertex.uv[0] = (float)seg / sphereSegments;
            vertex.uv[1] = (float)ring / sphereRings;

            // Red color for sphere
            vertex.color[0] = 0.9f;
            vertex.color[1] = 0.2f;
            vertex.color[2] = 0.2f;
            vertex.color[3] = 1.0f;

            sphereVertices.push_back(vertex);
        }
    }

    // Generate sphere indices
    for (int ring = 0; ring < sphereRings; ring++) {
        for (int seg = 0; seg < sphereSegments; seg++) {
            int current = ring * (sphereSegments + 1) + seg;
            int next = current + sphereSegments + 1;

            // First triangle
            sphereIndices.push_back(current);
            sphereIndices.push_back(next);
            sphereIndices.push_back(current + 1);

            // Second triangle
            sphereIndices.push_back(current + 1);
            sphereIndices.push_back(next);
            sphereIndices.push_back(next + 1);
        }
    }

    DP(("VkRaytracingDemo: Generated sphere with %u vertices, %u indices", sphereVertices.size(), sphereIndices.size()));

    // Create sphere BLAS
    VkBottomLevelAS* sphereBLAS = rtManager->createBottomLevelAS();
    if (!sphereBLAS) {
        DP(("VkRaytracingDemo: Failed to create sphere BLAS"));
        return false;
    }

    // Build sphere BLAS
    if (!sphereBLAS->buildFromMesh(sphereVertices.pointer(), sphereVertices.size(), sizeof(VertexAttribute),
                                   sphereIndices.pointer(), sphereIndices.size(), true)) {
        DP(("VkRaytracingDemo: Failed to build sphere BLAS"));
        return false;
    }

    // Add sphere instance to TLAS
    u32 sphereInstanceIndex = testTLAS->addInstance(sphereBLAS, identity, 1, 0, 0xFF);
    if (sphereInstanceIndex == ~0u) {
        DP(("VkRaytracingDemo: Failed to add sphere instance to TLAS"));
        return false;
    }

    // Build the TLAS
    if (!testTLAS->build()) {
        DP(("VkRaytracingDemo: Failed to build TLAS"));
        return false;
    }

    DP(("VkRaytracingDemo: Created cube and sphere geometry for raytracing"));
    DP(("  - Cube BLAS handle: %p", testBLAS->getHandle()));
    DP(("  - Sphere BLAS handle: %p", sphereBLAS->getHandle()));
    DP(("  - TLAS handle: %p", testTLAS->getHandle()));
    DP(("  - TLAS instances: %u", testTLAS->getInstanceCount()));
    DP(("  - Cube BLAS device address: 0x%llx", testBLAS->getDeviceAddress()));
    DP(("  - Sphere BLAS device address: 0x%llx", sphereBLAS->getDeviceAddress()));
    return true;

    return true;
}

bool VkRaytracingDemo::createOutputTexture(u32 width, u32 height) {
    if (!driver) {
        return false;
    }

    // Create a storage image for raytracing output
    // Use the special <STO> tag to create a storage image
    outputTexture = static_cast<VkTexture*>(
        driver->addRenderTargetTexture(core::dimension2d<u32>(width, height), 
                                      "rt_output<STO>", ECF_A8R8G8B8));
    
    return outputTexture != nullptr;
}

bool VkRaytracingDemo::createSkyTexture() {
    if (!driver) {
        return false;
    }

    // Load sky texture directly from rtxON2 sample
    // Using absolute path as requested by user
    const char* skyTexturePath = "D:/OProj/rtxON2/_data/envs/studio_garden_2k.jpg";
    
    skyTexture = static_cast<VkTexture*>(driver->getTexture(skyTexturePath));
    if (!skyTexture) {
        DP(("VkRaytracingDemo: Failed to load sky texture from %s", skyTexturePath));
        
        // Create a simple procedural sky texture as fallback
        const u32 skySize = 256;
        core::array<u32> skyData; skyData.set_used(skySize * skySize);
        
        // Simple gradient from blue (top) to white (bottom)
        for (u32 y = 0; y < skySize; ++y) {
            for (u32 x = 0; x < skySize; ++x) {
                float t = (float)y / (float)(skySize - 1);
                u8 blue = (u8)(255 * (1.0f - t * 0.3f));
                u8 green = (u8)(255 * (1.0f - t * 0.1f));
                u8 red = (u8)(255 * (1.0f - t * 0.05f));
                skyData[y * skySize + x] = (255 << 24) | (red << 16) | (green << 8) | blue;
            }
        }
        
        // Create texture from procedural data
        IImage* skyImage = driver->createImageFromData(ECF_A8R8G8B8, 
            core::dimension2d<u32>(skySize, skySize), skyData.pointer());
        
        if (skyImage) {
            skyTexture = static_cast<VkTexture*>(driver->addTexture("rt_sky_procedural", skyImage));
            
        }
    }
    
    return skyTexture != nullptr;
}

bool VkRaytracingDemo::createCameraBuffer() {
    if (!driver) {
        return false;
    }

    // Create camera uniform buffer
    cameraBuffer = static_cast<VkHardwareBuffer*>(driver->createHardwareBuffer(EHBT_CONSTANTS, EHBA_DEFAULT,
        sizeof(CameraUBO)));
    if (!cameraBuffer) {
        DP(("VkRaytracingDemo: Failed to create camera buffer"));
        return false;
    }

    return true;
}

void VkRaytracingDemo::updateCameraBuffer(const TestRtPm& pm) {
    if (!cameraBuffer) {
        return;
    }

    // Map camera buffer and update data
    CameraUBO* cameraData = static_cast<CameraUBO*>(cameraBuffer->lock());
    if (cameraData) {
        // Convert Irrlicht matrices to GLM matrices
        // Copy row by row from Irrlicht matrix4 to glm::mat4
        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 4; j++) {
                cameraData->viewMatrix[i][j] = pm.viewMatrix(i, j);
                cameraData->projMatrix[i][j] = pm.projMatrix(i, j);
            }
        }

        // Calculate inverse matrices
        core::matrix4 viewInv, projInv;
        pm.viewMatrix.getInverse(viewInv);
        pm.projMatrix.getInverse(projInv);

        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 4; j++) {
                cameraData->viewInverse[i][j] = viewInv(i, j);
                cameraData->projInverse[i][j] = projInv(i, j);
            }
        }

        // Convert Irrlicht vectors to GLM vectors
        cameraData->cameraPos = glm::vec3(pm.cameraPos.X, pm.cameraPos.Y, pm.cameraPos.Z);
        cameraData->cameraDir = glm::vec3(pm.cameraDir.X, pm.cameraDir.Y, pm.cameraDir.Z);
        cameraData->cameraUp = glm::vec3(pm.cameraUp.X, pm.cameraUp.Y, pm.cameraUp.Z);
        cameraData->cameraRight = glm::vec3(pm.cameraRight.X, pm.cameraRight.Y, pm.cameraRight.Z);
        
        // Copy camera parameters
        cameraData->nearPlane = pm.nearPlane;
        cameraData->farPlane = pm.farPlane;
        cameraData->fovY = pm.fovY;
        cameraData->time = pm.time;
        
        // Set padding values to 0
        cameraData->padding1 = 0.0f;
        cameraData->padding2 = 0.0f;
        cameraData->padding3 = 0.0f;
        cameraData->padding4 = 0.0f;
        
        cameraBuffer->unlock();
        
        DP(("VkRaytracingDemo: Camera buffer updated - pos(%f,%f,%f), dir(%f,%f,%f)",
            cameraData->cameraPos.x, cameraData->cameraPos.y, cameraData->cameraPos.z,
            cameraData->cameraDir.x, cameraData->cameraDir.y, cameraData->cameraDir.z));

        // Calculate where camera is looking
        glm::vec3 lookAtPoint = cameraData->cameraPos + cameraData->cameraDir * 15.0f; // 15 units ahead
        DP(("VkRaytracingDemo: Camera looking at point (%f,%f,%f)",
            lookAtPoint.x, lookAtPoint.y, lookAtPoint.z));
    }
}

void VkRaytracingDemo::updateDescriptorSets() {
    if (!driver || descriptorSet == VK_NULL_HANDLE || !outputTexture) {
        return;
    }

    VkWriteDescriptorSet writes[4] = {};  // Now need 4 writes instead of 3
    u32 writeCount = 0;
    
    // Update output image (Binding 0)
    VkDescriptorImageInfo imageInfo = {};
    imageInfo.imageView = outputTexture->getShaderResourceView();
    imageInfo.imageLayout = VK_IMAGE_LAYOUT_GENERAL;
    
    writes[writeCount].sType = VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET;
    writes[writeCount].dstSet = descriptorSet;
    writes[writeCount].dstBinding = RT_BINDING_OUTPUT_IMAGE;
    writes[writeCount].descriptorCount = 1;
    writes[writeCount].descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_IMAGE;
    writes[writeCount].pImageInfo = &imageInfo;
    writeCount++;

    // Update TLAS (Binding 1) - if available
    VkWriteDescriptorSetAccelerationStructureKHR asInfo = {};
    if (testTLAS && testTLAS->isValidAS()) {
        asInfo.sType = VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET_ACCELERATION_STRUCTURE_KHR;
        asInfo.accelerationStructureCount = 1;
        VkAccelerationStructureKHR asHandle = testTLAS->getHandle();
        asInfo.pAccelerationStructures = &asHandle;

        writes[writeCount].sType = VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET;
        writes[writeCount].pNext = &asInfo;
        writes[writeCount].dstSet = descriptorSet;
        writes[writeCount].dstBinding = RT_BINDING_ACCELERATION_STRUCTURE;
        writes[writeCount].descriptorCount = 1;
        writes[writeCount].descriptorType = VK_DESCRIPTOR_TYPE_ACCELERATION_STRUCTURE_KHR;
        writeCount++;

        DP(("VkRaytracingDemo: Binding TLAS to descriptor set - handle: %p", asHandle));
    } else {
        DP(("VkRaytracingDemo: WARNING - No valid TLAS to bind!"));
        if (testTLAS) {
            DP(("  TLAS exists but isValidAS() = false"));
        } else {
            DP(("  testTLAS is null"));
        }
    }

    // Update camera UBO (Binding 2) - if available
    if (cameraBuffer) {
        VkDescriptorBufferInfo cameraBufferInfo = {};
        cameraBufferInfo.buffer = cameraBuffer->getBuffer();
        cameraBufferInfo.offset = 0;
        cameraBufferInfo.range = VK_WHOLE_SIZE;
        
        writes[writeCount].sType = VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET;
        writes[writeCount].dstSet = descriptorSet;
        writes[writeCount].dstBinding = RT_BINDING_CAMERA_UBO;
        writes[writeCount].descriptorCount = 1;
        writes[writeCount].descriptorType = VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER;
        writes[writeCount].pBufferInfo = &cameraBufferInfo;
        writeCount++;
    }

    // Create default sampler for sky texture if needed
    if (m_defaultSampler == VK_NULL_HANDLE) {
        VkSamplerCreateInfo samplerInfo{};
        samplerInfo.sType = VK_STRUCTURE_TYPE_SAMPLER_CREATE_INFO;
        samplerInfo.magFilter = VK_FILTER_LINEAR;
        samplerInfo.minFilter = VK_FILTER_LINEAR;
        samplerInfo.mipmapMode = VK_SAMPLER_MIPMAP_MODE_LINEAR;
        samplerInfo.addressModeU = VK_SAMPLER_ADDRESS_MODE_MIRRORED_REPEAT;
        samplerInfo.addressModeV = VK_SAMPLER_ADDRESS_MODE_MIRRORED_REPEAT;
        samplerInfo.addressModeW = VK_SAMPLER_ADDRESS_MODE_MIRRORED_REPEAT;
        samplerInfo.mipLodBias = 0.0f;
        samplerInfo.anisotropyEnable = VK_FALSE;
        samplerInfo.maxAnisotropy = 1.0f;
        samplerInfo.compareEnable = VK_FALSE;
        samplerInfo.compareOp = VK_COMPARE_OP_ALWAYS;
        samplerInfo.minLod = 0.0f;
        samplerInfo.maxLod = 1.0f;
        samplerInfo.borderColor = VK_BORDER_COLOR_FLOAT_TRANSPARENT_BLACK;
        samplerInfo.unnormalizedCoordinates = VK_FALSE;
        
        VK_CHECK_RESULT(vkCreateSampler(driver->getDevice(), &samplerInfo, nullptr, &m_defaultSampler));
    }
    // Update sky texture (Binding 3) - if available
    VkDescriptorImageInfo skyImageInfo = {};
    if (skyTexture) {
        skyImageInfo.imageView = skyTexture->getShaderResourceView();
        skyImageInfo.imageLayout = VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL;
        skyImageInfo.sampler = m_defaultSampler;  // Use texture's sampler
        
        writes[writeCount].sType = VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET;
        writes[writeCount].dstSet = descriptorSet;
        writes[writeCount].dstBinding = 3;  // Sky texture binding
        writes[writeCount].descriptorCount = 1;
        writes[writeCount].descriptorType = VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER;
        writes[writeCount].pImageInfo = &skyImageInfo;
        writeCount++;
    }

    // Update descriptor sets with the writes we have
    if (writeCount > 0) {
        vkUpdateDescriptorSets(driver->getDevice(), writeCount, writes, 0, nullptr);
    }
    
    // Update Set 1 (geometry data) descriptors
    if (geometryDescriptorSet != VK_NULL_HANDLE && testVertexBuffer && testIndexBuffer &&
        testMaterialIDsBuffer && testMaterialsBuffer) {

        DP(("VkRaytracingDemo: Updating geometry descriptor set with all buffers"));
        
        VkWriteDescriptorSet geometryWrites[4] = {};
        u32 geometryWriteCount = 0;
        
        // Binding 0: Vertex attributes buffer
        VkDescriptorBufferInfo vertexBufferInfo = {};
        vertexBufferInfo.buffer = testVertexBuffer->getBuffer();
        vertexBufferInfo.offset = 0;
        vertexBufferInfo.range = VK_WHOLE_SIZE;
        
        geometryWrites[geometryWriteCount].sType = VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET;
        geometryWrites[geometryWriteCount].dstSet = geometryDescriptorSet;
        geometryWrites[geometryWriteCount].dstBinding = 0;  // RT_BINDING_VERTEX_BUFFER
        geometryWrites[geometryWriteCount].descriptorCount = 1;
        geometryWrites[geometryWriteCount].descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_BUFFER;
        geometryWrites[geometryWriteCount].pBufferInfo = &vertexBufferInfo;
        geometryWriteCount++;
        
        // Binding 1: Faces/index buffer
        VkDescriptorBufferInfo indexBufferInfo = {};
        indexBufferInfo.buffer = testIndexBuffer->getBuffer();
        indexBufferInfo.offset = 0;
        indexBufferInfo.range = VK_WHOLE_SIZE;
        
        geometryWrites[geometryWriteCount].sType = VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET;
        geometryWrites[geometryWriteCount].dstSet = geometryDescriptorSet;
        geometryWrites[geometryWriteCount].dstBinding = 1;  // RT_BINDING_INDEX_BUFFER
        geometryWrites[geometryWriteCount].descriptorCount = 1;
        geometryWrites[geometryWriteCount].descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_BUFFER;
        geometryWrites[geometryWriteCount].pBufferInfo = &indexBufferInfo;
        geometryWriteCount++;
        
        // Binding 2: Material IDs buffer
        VkDescriptorBufferInfo materialIDsBufferInfo = {};
        materialIDsBufferInfo.buffer = testMaterialIDsBuffer->getBuffer();
        materialIDsBufferInfo.offset = 0;
        materialIDsBufferInfo.range = VK_WHOLE_SIZE;

        DP(("VkRaytracingDemo: MaterialIDs buffer handle: %p", materialIDsBufferInfo.buffer));
        
        geometryWrites[geometryWriteCount].sType = VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET;
        geometryWrites[geometryWriteCount].dstSet = geometryDescriptorSet;
        geometryWrites[geometryWriteCount].dstBinding = 2;  // MaterialIDsBuffer binding
        geometryWrites[geometryWriteCount].descriptorCount = 1;
        geometryWrites[geometryWriteCount].descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_BUFFER;
        geometryWrites[geometryWriteCount].pBufferInfo = &materialIDsBufferInfo;
        geometryWriteCount++;
        
        // Binding 3: Materials buffer
        VkDescriptorBufferInfo materialsBufferInfo = {};
        materialsBufferInfo.buffer = testMaterialsBuffer->getBuffer();
        materialsBufferInfo.offset = 0;
        materialsBufferInfo.range = VK_WHOLE_SIZE;
        
        geometryWrites[geometryWriteCount].sType = VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET;
        geometryWrites[geometryWriteCount].dstSet = geometryDescriptorSet;
        geometryWrites[geometryWriteCount].dstBinding = 3;  // RT_BINDING_MATERIAL_BUFFER
        geometryWrites[geometryWriteCount].descriptorCount = 1;
        geometryWrites[geometryWriteCount].descriptorType = VK_DESCRIPTOR_TYPE_STORAGE_BUFFER;
        geometryWrites[geometryWriteCount].pBufferInfo = &materialsBufferInfo;
        geometryWriteCount++;
        
        // Update Set 1 descriptors
        vkUpdateDescriptorSets(driver->getDevice(), geometryWriteCount, geometryWrites, 0, nullptr);

        DP(("VkRaytracingDemo: Updated geometry descriptor set with %u bindings", geometryWriteCount));
    } else {
        DP(("VkRaytracingDemo: Cannot update geometry descriptor set - missing buffers or descriptor set"));
        DP(("  geometryDescriptorSet: %p", geometryDescriptorSet));
        DP(("  testVertexBuffer: %p", testVertexBuffer));
        DP(("  testIndexBuffer: %p", testIndexBuffer));
        DP(("  testMaterialIDsBuffer: %p", testMaterialIDsBuffer));
        DP(("  testMaterialsBuffer: %p", testMaterialsBuffer));
    }
}

bool VkRaytracingDemo::renderFrame(u32 width, u32 height) {
    if (!isInitialized || !driver) {
        return false;
    }
    
    // Ensure output texture matches requested size
    if (!outputTexture || outputTexture->getSize().Width != width || outputTexture->getSize().Height != height) {
        if (outputTexture) {
            ITexture* tex = outputTexture;
            driver->freeTexture(tex);
            outputTexture = nullptr;
        }
        if (!createOutputTexture(width, height)) {
            return false;
        }
        
        // Update descriptor set with new texture
        updateDescriptorSets();
    }
    
    // Use single-time command buffer for raytracing (like acceleration structure building)
    VkCommandBuffer commandBuffer = driver->beginSingleTimeCommands();
    if (commandBuffer == VK_NULL_HANDLE) {
        DP(("VkRaytracingDemo: Failed to begin single-time command buffer"));
        return false;
    }

    // Transition output texture to GENERAL layout for storage image usage
    if (outputTexture) {
        VkImageMemoryBarrier barrier = {};
        barrier.sType = VK_STRUCTURE_TYPE_IMAGE_MEMORY_BARRIER;
        barrier.oldLayout = VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL;  // Current layout
        barrier.newLayout = VK_IMAGE_LAYOUT_GENERAL;  // Required for storage image
        barrier.srcQueueFamilyIndex = VK_QUEUE_FAMILY_IGNORED;
        barrier.dstQueueFamilyIndex = VK_QUEUE_FAMILY_IGNORED;
        barrier.image = outputTexture->getTextureResource();
        barrier.subresourceRange.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
        barrier.subresourceRange.baseMipLevel = 0;
        barrier.subresourceRange.levelCount = 1;
        barrier.subresourceRange.baseArrayLayer = 0;
        barrier.subresourceRange.layerCount = 1;
        barrier.srcAccessMask = VK_ACCESS_SHADER_READ_BIT;
        barrier.dstAccessMask = VK_ACCESS_SHADER_WRITE_BIT;

        vkCmdPipelineBarrier(commandBuffer,
                            VK_PIPELINE_STAGE_FRAGMENT_SHADER_BIT,
                            VK_PIPELINE_STAGE_RAY_TRACING_SHADER_BIT_KHR,
                            0, 0, nullptr, 0, nullptr, 1, &barrier);
    }

    // Update descriptor sets with current resources
    updateDescriptorSets();

    // Bind raytracing pipeline
    if (pipeline && pipeline->isValidPipeline()) {
        pipeline->bind(commandBuffer);

        // Bind descriptor sets
        if (descriptorSet != VK_NULL_HANDLE && geometryDescriptorSet != VK_NULL_HANDLE && pipeline->getPipelineLayout() != VK_NULL_HANDLE) {
            VkDescriptorSet descriptorSets[2] = { descriptorSet, geometryDescriptorSet };
            vkCmdBindDescriptorSets(commandBuffer, VK_PIPELINE_BIND_POINT_RAY_TRACING_KHR,
                                   pipeline->getPipelineLayout(), 0, 2, descriptorSets, 0, nullptr);
        }

        // Dispatch rays
        pipeline->traceRays(commandBuffer, width, height, 1);

        // Transition output texture back to SHADER_READ_ONLY_OPTIMAL for display
        if (outputTexture) {
            VkImageMemoryBarrier barrier = {};
            barrier.sType = VK_STRUCTURE_TYPE_IMAGE_MEMORY_BARRIER;
            barrier.oldLayout = VK_IMAGE_LAYOUT_GENERAL;
            barrier.newLayout = VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL;
            barrier.srcQueueFamilyIndex = VK_QUEUE_FAMILY_IGNORED;
            barrier.dstQueueFamilyIndex = VK_QUEUE_FAMILY_IGNORED;
            barrier.image = outputTexture->getTextureResource();
            barrier.subresourceRange.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
            barrier.subresourceRange.baseMipLevel = 0;
            barrier.subresourceRange.levelCount = 1;
            barrier.subresourceRange.baseArrayLayer = 0;
            barrier.subresourceRange.layerCount = 1;
            barrier.srcAccessMask = VK_ACCESS_SHADER_WRITE_BIT;
            barrier.dstAccessMask = VK_ACCESS_SHADER_READ_BIT;

            vkCmdPipelineBarrier(commandBuffer,
                                VK_PIPELINE_STAGE_RAY_TRACING_SHADER_BIT_KHR,
                                VK_PIPELINE_STAGE_FRAGMENT_SHADER_BIT,
                                0, 0, nullptr, 0, nullptr, 1, &barrier);
        }

        // End and submit the command buffer
        driver->endSingleTimeCommands(commandBuffer);

        DP(("VkRaytracingDemo: Ray tracing dispatched (%dx%d)", width, height));
        return true;
    }
    
    // If we get here, pipeline wasn't valid, so end the command buffer anyway
    driver->endSingleTimeCommands(commandBuffer);
    return false;
}

bool VkRaytracingDemo::testRaytracing(const TestRtPm& pm) {
    if (!isInitialized) {
        DP(("VkRaytracingDemo: Cannot test - not initialized"));
        return false;
    }
    
    // Test with the requested resolution
    const u32 testWidth = pm.width;
	const u32 testHeight = pm.height;
    skyTexture = (VkTexture*)pm.skyTexture;
    
    // Update camera buffer with the provided camera data
    updateCameraBuffer(pm);
    
    // Try to render a test frame
    if (!renderFrame(testWidth, testHeight)) {
        DP(("VkRaytracingDemo: Test frame rendering failed"));
        return false;
    }
    
    DP(("VkRaytracingDemo: Test successful - raytracing pipeline ready"));
    return true;
}

void VkRaytracingDemo::shutdown() {
    if (driver) {
        // Cleanup descriptor sets
        if (descriptorPool != VK_NULL_HANDLE) {
            vkDestroyDescriptorPool(driver->getDevice(), descriptorPool, nullptr);
            descriptorPool = VK_NULL_HANDLE;
        }
        
        if (descriptorSetLayout != VK_NULL_HANDLE) {
            vkDestroyDescriptorSetLayout(driver->getDevice(), descriptorSetLayout, nullptr);
            descriptorSetLayout = VK_NULL_HANDLE;
        }
        
        if (geometryDescriptorSetLayout != VK_NULL_HANDLE) {
            vkDestroyDescriptorSetLayout(driver->getDevice(), geometryDescriptorSetLayout, nullptr);
            geometryDescriptorSetLayout = VK_NULL_HANDLE;
        }
        
        descriptorSet = VK_NULL_HANDLE;
        geometryDescriptorSet = VK_NULL_HANDLE;
    }

    // Cleanup pipeline
    delete pipeline;
    pipeline = nullptr;

 

    // Cleanup geometry buffers
    delete testVertexBuffer;
    testVertexBuffer = nullptr;
    delete testIndexBuffer;
    testIndexBuffer = nullptr;
    delete testMaterialIDsBuffer;
    testMaterialIDsBuffer = nullptr;
    delete testMaterialsBuffer;
    testMaterialsBuffer = nullptr;
    
    // Cleanup camera buffer
    delete cameraBuffer;
    cameraBuffer = nullptr;

    // Note: testBLAS and testTLAS are owned by the raytracing manager
    testBLAS = nullptr;
    testTLAS = nullptr;

    shaders = nullptr;
    isInitialized = false;
}

} // namespace video
} // namespace irr

#endif // VK_ENABLE_RAYTRACING
#endif // _IRR_COMPILE_WITH_VULKAN_ 