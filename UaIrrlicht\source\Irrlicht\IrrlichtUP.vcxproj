﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|ARM">
      <Configuration>Debug</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Profile|ARM">
      <Configuration>Profile</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Profile|Win32">
      <Configuration>Profile</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Profile|x64">
      <Configuration>Profile</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|ARM">
      <Configuration>Release</Configuration>
      <Platform>ARM</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <VCProjectVersion>15.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectName>IrrlichtUP</ProjectName>
    <ProjectGuid>{DEE80C8A-CCE7-4F5E-B67C-E727B11EAED7}</ProjectGuid>
    <RootNamespace>Irrlicht</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|ARM'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Profile|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Profile|ARM'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.30319.1</_ProjectFileVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>$(SolutionDir)Debug\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|Win32'">
    <OutDir>$(SolutionDir)Debug\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>$(SolutionDir)Release\</OutDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM'" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <IntDir>$(SolutionDir)$(Platform)\$(ProjectName)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">
    <IntDir>$(SolutionDir)$(Platform)\$(ProjectName)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <IntDir>$(SolutionDir)$(Platform)\$(ProjectName)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetEnvironment>Win32</TargetEnvironment>
      <TypeLibraryName>.\..\Debug/Irrlicht.tlb</TypeLibraryName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>.;../../include;zlib;../../../CommonStaticLib/src;../../../CommonStaticLib/DirectXMath/Inc;D:\sdk\VulkanSDK\Include;../../external/glm;../../external/gli;../../../SDK/DGEngine;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>IRR_LITE=1;WIN32;_DEBUG;_WINDOWS;_USRDLL;_IRR_STATIC_LIB_;_CRT_SECURE_NO_DEPRECATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ExceptionHandling>Sync</ExceptionHandling>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <WarningLevel>Level3</WarningLevel>
      <EnableEnhancedInstructionSet>StreamingSIMDExtensions2</EnableEnhancedInstructionSet>
      <FloatingPointModel>Fast</FloatingPointModel>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <StructMemberAlignment>16Bytes</StructMemberAlignment>
      <CompileAsWinRT>false</CompileAsWinRT>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <LanguageStandard>stdcpp20</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0c07</Culture>
    </ResourceCompile>
    <Lib>
      <AdditionalDependencies>winmm.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Lib>
    <FxCompile>
      <ShaderType>Effect</ShaderType>
    </FxCompile>
    <FxCompile>
      <ShaderModel>5.0</ShaderModel>
      <EntryPointName />
      <DisableOptimizations>true</DisableOptimizations>
      <EnableDebuggingInformation>true</EnableDebuggingInformation>
      <ObjectFileOutput>$(SolutionDir)Debug\res\%(Filename).cso</ObjectFileOutput>
    </FxCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Profile|Win32'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetEnvironment>Win32</TargetEnvironment>
      <TypeLibraryName>.\..\Debug/Irrlicht.tlb</TypeLibraryName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>.;../../include;zlib;../../../CommonStaticLib/src;../../../CommonStaticLib/DirectXMath/Inc;D:\sdk\VulkanSDK\Include;../../external/glm;../../external/gli;../../../SDK/DGEngine;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>IRR_LITE=1;WIN32;_DEBUG;_WINDOWS;_USRDLL;_IRR_STATIC_LIB_;_CRT_SECURE_NO_DEPRECATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ExceptionHandling>Sync</ExceptionHandling>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <WarningLevel>Level3</WarningLevel>
      <EnableEnhancedInstructionSet>StreamingSIMDExtensions2</EnableEnhancedInstructionSet>
      <FloatingPointModel>Fast</FloatingPointModel>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <StructMemberAlignment>16Bytes</StructMemberAlignment>
      <CompileAsWinRT>false</CompileAsWinRT>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <LanguageStandard>stdcpp20</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0c07</Culture>
    </ResourceCompile>
    <Lib>
      <AdditionalDependencies>winmm.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Lib>
    <FxCompile>
      <ShaderType>Effect</ShaderType>
    </FxCompile>
    <FxCompile>
      <ShaderModel>5.0</ShaderModel>
      <EntryPointName>
      </EntryPointName>
      <DisableOptimizations>true</DisableOptimizations>
      <EnableDebuggingInformation>true</EnableDebuggingInformation>
      <ObjectFileOutput>$(SolutionDir)Debug\res\%(Filename).cso</ObjectFileOutput>
    </FxCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TypeLibraryName>.\..\Debug/Irrlicht.tlb</TypeLibraryName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>.;../../include;zlib;../../../CommonStaticLib/src;../../../CommonStaticLib/DirectXMath/Inc;D:\sdk\VulkanSDK\Include;../../external/glm;../../external/gli;../../external/imgui;../../../CommonStaticLib;../../;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>IRR_LITE=1;WIN32;_DEBUG;_WINDOWS;_USRDLL;_IRR_STATIC_LIB_;_CRT_SECURE_NO_DEPRECATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ExceptionHandling>Sync</ExceptionHandling>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <WarningLevel>Level3</WarningLevel>
      <FloatingPointModel>Fast</FloatingPointModel>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <StructMemberAlignment>16Bytes</StructMemberAlignment>
      <CompileAsWinRT>false</CompileAsWinRT>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <LanguageStandard>stdcpp20</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0c07</Culture>
    </ResourceCompile>
    <Lib />
    <FxCompile />
    <FxCompile>
      <ObjectFileOutput>$(SolutionDir)Debug\res\%(Filename).cso</ObjectFileOutput>
    </FxCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TypeLibraryName>.\..\Debug/Irrlicht.tlb</TypeLibraryName>
    </Midl>
    <ClCompile>
      <Optimization>Full</Optimization>
      <AdditionalIncludeDirectories>.;../../include;zlib;../../../CommonStaticLib/src;../../../CommonStaticLib/DirectXMath/Inc;D:\sdk\VulkanSDK\Include;../../external/glm;../../external/gli;../../external/imgui;../../../CommonStaticLib;../../;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>IRR_LITE=1;WIN32;_DEBUG;_WINDOWS;_USRDLL;_IRR_STATIC_LIB_;_CRT_SECURE_NO_DEPRECATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ExceptionHandling>Sync</ExceptionHandling>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <WarningLevel>Level3</WarningLevel>
      <EnableEnhancedInstructionSet>StreamingSIMDExtensions2</EnableEnhancedInstructionSet>
      <FloatingPointModel>Fast</FloatingPointModel>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <StructMemberAlignment>16Bytes</StructMemberAlignment>
      <CompileAsWinRT>false</CompileAsWinRT>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <LanguageStandard>stdcpp20</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0c07</Culture>
    </ResourceCompile>
    <Lib />
    <FxCompile />
    <FxCompile>
      <ObjectFileOutput>$(SolutionDir)Debug\res\%(Filename).cso</ObjectFileOutput>
    </FxCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|ARM'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TypeLibraryName>.\..\Debug/Irrlicht.tlb</TypeLibraryName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>.;../../include;zlib;../../../CommonStaticLib/src;../../../CommonStaticLib/DirectXMath/Inc;D:\sdk\VulkanSDK\Include;../../external/glm;../../external/gli;../../../SDK/DGEngine;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>IRR_LITE=1;WIN32;_DEBUG;_WINDOWS;_USRDLL;_IRR_STATIC_LIB_;_CRT_SECURE_NO_DEPRECATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ExceptionHandling>Sync</ExceptionHandling>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <WarningLevel>Level3</WarningLevel>
      <EnableEnhancedInstructionSet>StreamingSIMDExtensions2</EnableEnhancedInstructionSet>
      <FloatingPointModel>Fast</FloatingPointModel>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <StructMemberAlignment>16Bytes</StructMemberAlignment>
      <CompileAsWinRT>false</CompileAsWinRT>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <LanguageStandard>stdcpp20</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0c07</Culture>
    </ResourceCompile>
    <Lib>
      <AdditionalDependencies>winmm.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Lib>
    <FxCompile>
      <ShaderType>Effect</ShaderType>
    </FxCompile>
    <FxCompile>
      <ShaderModel>5.0</ShaderModel>
      <EntryPointName>
      </EntryPointName>
      <DisableOptimizations>true</DisableOptimizations>
      <EnableDebuggingInformation>true</EnableDebuggingInformation>
      <ObjectFileOutput>$(SolutionDir)Debug\res\%(Filename).cso</ObjectFileOutput>
    </FxCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Profile|ARM'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TypeLibraryName>.\..\Debug/Irrlicht.tlb</TypeLibraryName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>.;../../include;zlib;../../../CommonStaticLib/src;../../../CommonStaticLib/DirectXMath/Inc;D:\sdk\VulkanSDK\Include;../../external/glm;../../external/gli;../../../SDK/DGEngine;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>IRR_LITE=1;WIN32;_DEBUG;_WINDOWS;_USRDLL;_IRR_STATIC_LIB_;_CRT_SECURE_NO_DEPRECATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ExceptionHandling>Sync</ExceptionHandling>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <WarningLevel>Level3</WarningLevel>
      <EnableEnhancedInstructionSet>StreamingSIMDExtensions2</EnableEnhancedInstructionSet>
      <FloatingPointModel>Fast</FloatingPointModel>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <StructMemberAlignment>16Bytes</StructMemberAlignment>
      <CompileAsWinRT>false</CompileAsWinRT>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <LanguageStandard>stdcpp20</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0c07</Culture>
    </ResourceCompile>
    <Lib>
      <AdditionalDependencies>winmm.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Lib>
    <FxCompile>
      <ShaderType>Effect</ShaderType>
    </FxCompile>
    <FxCompile>
      <ShaderModel>5.0</ShaderModel>
      <EntryPointName>
      </EntryPointName>
      <DisableOptimizations>true</DisableOptimizations>
      <EnableDebuggingInformation>true</EnableDebuggingInformation>
      <ObjectFileOutput>$(SolutionDir)Debug\res\%(Filename).cso</ObjectFileOutput>
    </FxCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Midl>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetEnvironment>Win32</TargetEnvironment>
      <TypeLibraryName>.\..\Release/Irrlicht.tlb</TypeLibraryName>
    </Midl>
    <ClCompile>
      <Optimization>MaxSpeed</Optimization>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <AdditionalIncludeDirectories>.;../../include;zlib;../../../CommonStaticLib/src;../../../CommonStaticLib/DirectXMath/Inc;D:\sdk\VulkanSDK\Include;../../external/glm;../../external/gli;../../../SDK/DGEngine;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>IRR_LITE=1;WINAPI_FAMILY=3;WIN32;NDEBUG;_WINDOWS;_USRDLL;_IRR_STATIC_LIB_;_CRT_SECURE_NO_DEPRECATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <ExceptionHandling>Sync</ExceptionHandling>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <WarningLevel>Level3</WarningLevel>
      <EnableEnhancedInstructionSet>StreamingSIMDExtensions2</EnableEnhancedInstructionSet>
      <FloatingPointModel>Fast</FloatingPointModel>
      <CompileAsWinRT>false</CompileAsWinRT>
      <LanguageStandard>stdcpp20</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0c07</Culture>
    </ResourceCompile>
    <Lib>
      <AdditionalDependencies>winmm.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Lib>
    <FxCompile>
      <ObjectFileOutput>$(SolutionDir)Release\res\%(Filename).cso</ObjectFileOutput>
      <DisableOptimizations>false</DisableOptimizations>
      <EnableDebuggingInformation>false</EnableDebuggingInformation>
      <ShaderType>Effect</ShaderType>
      <ShaderModel>5.0</ShaderModel>
    </FxCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <Optimization>MaxSpeed</Optimization>
      <WholeProgramOptimization>true</WholeProgramOptimization>
      <AdditionalIncludeDirectories>.;../../include;zlib;../../../CommonStaticLib/src;../../../CommonStaticLib/DirectXMath/Inc;D:\sdk\VulkanSDK\Include;../../external/glm;../../external/gli;../../external/imgui;../../../CommonStaticLib;../../;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>IRR_LITE=1;WIN32;NDEBUG;_WINDOWS;_USRDLL;_IRR_STATIC_LIB_;_CRT_SECURE_NO_DEPRECATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <WarningLevel>Level3</WarningLevel>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <SDLCheck>true</SDLCheck>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <ConformanceMode>true</ConformanceMode>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0c07</Culture>
    </ResourceCompile>
    <Lib />
    <FxCompile>
      <ObjectFileOutput>$(SolutionDir)Release\res\%(Filename).cso</ObjectFileOutput>
    </FxCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|ARM'">
    <Midl>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TypeLibraryName>.\..\Release/Irrlicht.tlb</TypeLibraryName>
    </Midl>
    <ClCompile>
      <Optimization>MaxSpeed</Optimization>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <WholeProgramOptimization>false</WholeProgramOptimization>
      <AdditionalIncludeDirectories>.;../../include;zlib;../../../CommonStaticLib/src;../../../CommonStaticLib/DirectXMath/Inc;D:\sdk\VulkanSDK\Include;../../external/glm;../../external/gli;../../../SDK/DGEngine;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>IRR_LITE;WINAPI_FAMILY=3;WIN32;NDEBUG;_WINDOWS;_USRDLL;_IRR_STATIC_LIB_;_CRT_SECURE_NO_DEPRECATE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <StringPooling>true</StringPooling>
      <ExceptionHandling>Sync</ExceptionHandling>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <BufferSecurityCheck>false</BufferSecurityCheck>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <EnableEnhancedInstructionSet>StreamingSIMDExtensions2</EnableEnhancedInstructionSet>
      <FloatingPointModel>Fast</FloatingPointModel>
      <CompileAsWinRT>false</CompileAsWinRT>
      <LanguageStandard>stdcpp20</LanguageStandard>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0c07</Culture>
    </ResourceCompile>
    <Lib>
      <AdditionalDependencies>winmm.lib;%(AdditionalDependencies)</AdditionalDependencies>
    </Lib>
    <FxCompile>
      <ObjectFileOutput>$(SolutionDir)Release\res\%(Filename).cso</ObjectFileOutput>
      <DisableOptimizations>false</DisableOptimizations>
      <EnableDebuggingInformation>false</EnableDebuggingInformation>
      <ShaderType>Effect</ShaderType>
      <ShaderModel>5.0</ShaderModel>
    </FxCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='SDL-Debug|Win32'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetEnvironment>Win32</TargetEnvironment>
      <TypeLibraryName>.\..\Debug/Irrlicht.tlb</TypeLibraryName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>..\..\include;zlib;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_USRDLL;IRRLICHT_EXPORTS;_CRT_SECURE_NO_DEPRECATE;_IRR_USE_SDL_DEVICE_=1;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ExceptionHandling>
      </ExceptionHandling>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <WarningLevel>Level3</WarningLevel>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0c07</Culture>
    </ResourceCompile>
    <ProjectReference>
      <UseLibraryDependencyInputs>true</UseLibraryDependencyInputs>
    </ProjectReference>
    <Link>
      <AdditionalOptions>/MACHINE:I386 %(AdditionalOptions)</AdditionalOptions>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;opengl32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>..\..\bin\Win32-visualstudio\Irrlicht.dll</OutputFile>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <IgnoreSpecificDefaultLibraries>libci.lib;%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <ImportLibrary>..\..\lib\Win32-visualstudio\Irrlicht.lib</ImportLibrary>
      <Version>1.8</Version>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='SDL-Debug|x64'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TypeLibraryName>.\..\Debug/Irrlicht.tlb</TypeLibraryName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>..\..\include;zlib;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_USRDLL;IRRLICHT_EXPORTS;_CRT_SECURE_NO_DEPRECATE;_IRR_USE_SDL_DEVICE_=1;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ExceptionHandling>
      </ExceptionHandling>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <WarningLevel>Level3</WarningLevel>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0c07</Culture>
    </ResourceCompile>
    <ProjectReference>
      <UseLibraryDependencyInputs>true</UseLibraryDependencyInputs>
    </ProjectReference>
    <Link>
      <AdditionalOptions>/MACHINE:I386 %(AdditionalOptions)</AdditionalOptions>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;opengl32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>..\..\bin\Win32-visualstudio\Irrlicht.dll</OutputFile>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <IgnoreSpecificDefaultLibraries>libci.lib;%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <ImportLibrary>..\..\lib\Win32-visualstudio\Irrlicht.lib</ImportLibrary>
      <Version>1.8</Version>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='SDL-Debug|ARM'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TypeLibraryName>.\..\Debug/Irrlicht.tlb</TypeLibraryName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>..\..\include;zlib;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_USRDLL;IRRLICHT_EXPORTS;_CRT_SECURE_NO_DEPRECATE;_IRR_USE_SDL_DEVICE_=1;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ExceptionHandling>
      </ExceptionHandling>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <WarningLevel>Level3</WarningLevel>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0c07</Culture>
    </ResourceCompile>
    <ProjectReference>
      <UseLibraryDependencyInputs>true</UseLibraryDependencyInputs>
    </ProjectReference>
    <Link>
      <AdditionalOptions>/MACHINE:I386 %(AdditionalOptions)</AdditionalOptions>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;opengl32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>..\..\bin\Win32-visualstudio\Irrlicht.dll</OutputFile>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <IgnoreSpecificDefaultLibraries>libci.lib;%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <ImportLibrary>..\..\lib\Win32-visualstudio\Irrlicht.lib</ImportLibrary>
      <Version>1.8</Version>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='SDL-Debug|ARM'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TypeLibraryName>.\..\Debug/Irrlicht.tlb</TypeLibraryName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>..\..\include;zlib;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_USRDLL;IRRLICHT_EXPORTS;_CRT_SECURE_NO_DEPRECATE;_IRR_USE_SDL_DEVICE_=1;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ExceptionHandling>
      </ExceptionHandling>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <WarningLevel>Level3</WarningLevel>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0c07</Culture>
    </ResourceCompile>
    <ProjectReference>
      <UseLibraryDependencyInputs>true</UseLibraryDependencyInputs>
    </ProjectReference>
    <Link>
      <AdditionalOptions>/MACHINE:I386 %(AdditionalOptions)</AdditionalOptions>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;opengl32.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>..\..\bin\Win32-visualstudio\Irrlicht.dll</OutputFile>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <IgnoreSpecificDefaultLibraries>libci.lib;%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <ImportLibrary>..\..\lib\Win32-visualstudio\Irrlicht.lib</ImportLibrary>
      <Version>1.8</Version>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='SDL-Debug|x64'">
    <Midl>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TypeLibraryName>.\..\Debug/Irrlicht.tlb</TypeLibraryName>
    </Midl>
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>..\..\include;zlib;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;WIN64;_DEBUG;_WINDOWS;_USRDLL;IRRLICHT_EXPORTS;_CRT_SECURE_NO_DEPRECATE;_IRR_USE_SDL_DEVICE_=1;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ExceptionHandling>
      </ExceptionHandling>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <DisableLanguageExtensions>false</DisableLanguageExtensions>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <WarningLevel>Level3</WarningLevel>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <Culture>0x0c07</Culture>
    </ResourceCompile>
    <ProjectReference>
      <UseLibraryDependencyInputs>true</UseLibraryDependencyInputs>
    </ProjectReference>
    <Link>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;opengl32.lib;winmm.lib;%(AdditionalDependencies)</AdditionalDependencies>
      <OutputFile>..\..\bin\Win64-visualstudio\Irrlicht.dll</OutputFile>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <IgnoreSpecificDefaultLibraries>libci.lib;%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <DataExecutionPrevention>
      </DataExecutionPrevention>
      <ImportLibrary>..\..\lib\Win64-visualstudio\Irrlicht.lib</ImportLibrary>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="..\..\external\ImGuizmo\ImCurveEdit.h" />
    <ClInclude Include="..\..\external\ImGuizmo\ImGradient.h" />
    <ClInclude Include="..\..\external\ImGuizmo\ImGuizmo.h" />
    <ClInclude Include="..\..\external\ImGuizmo\ImSequencer.h" />
    <ClInclude Include="..\..\external\ImGuizmo\ImZoomSlider.h" />
    <ClInclude Include="..\..\external\imgui\imconfig.h" />
    <ClInclude Include="..\..\external\imgui\imgui.h" />
    <ClInclude Include="..\..\external\imgui\imgui_internal.h" />
    <ClInclude Include="..\..\external\imgui\imstb_rectpack.h" />
    <ClInclude Include="..\..\external\imgui\imstb_textedit.h" />
    <ClInclude Include="..\..\external\imgui\imstb_truetype.h" />
    <ClInclude Include="..\..\include\driverChoice.h" />
    <ClInclude Include="..\..\include\EDriverFeatures.h" />
    <ClInclude Include="..\..\include\IAnimatedMeshMD3.h" />
    <ClInclude Include="..\..\include\IEventReceiver.h" />
    <ClInclude Include="..\..\include\ILogger.h" />
    <ClInclude Include="..\..\include\IOSOperator.h" />
    <ClInclude Include="..\..\include\IRandomizer.h" />
    <ClInclude Include="..\..\include\IReferenceCounted.h" />
    <ClInclude Include="..\..\include\IrrCompileConfig.h" />
    <ClInclude Include="..\..\include\irrlicht.h" />
    <ClInclude Include="..\..\include\IrrlichtDevice.h" />
    <ClInclude Include="..\..\include\irrTypes.h" />
    <ClInclude Include="..\..\include\ISceneNodeAnimatorCameraTouchControl.h" />
    <ClInclude Include="..\..\include\ITimer.h" />
    <ClInclude Include="..\..\include\Keycodes.h" />
    <ClInclude Include="..\..\include\SIrrCreationParameters.h" />
    <ClInclude Include="..\..\include\SKeyMap.h" />
    <ClInclude Include="..\..\include\EDriverTypes.h" />
    <ClInclude Include="..\..\include\IGeometryCreator.h" />
    <ClInclude Include="..\..\include\IGPUProgrammingServices.h" />
    <ClInclude Include="..\..\include\IImage.h" />
    <ClInclude Include="..\..\include\IImageLoader.h" />
    <ClInclude Include="..\..\include\IMaterialRenderer.h" />
    <ClInclude Include="..\..\include\IMaterialRendererServices.h" />
    <ClInclude Include="..\..\include\IShaderConstantSetCallBack.h" />
    <ClInclude Include="..\..\include\ITexture.h" />
    <ClInclude Include="..\..\include\IVideoDriver.h" />
    <ClInclude Include="..\..\include\IVideoModeList.h" />
    <ClInclude Include="..\..\include\S3DVertex.h" />
    <ClInclude Include="..\..\include\SColor.h" />
    <ClInclude Include="..\..\include\SExposedVideoData.h" />
    <ClInclude Include="..\..\include\SLight.h" />
    <ClInclude Include="..\..\include\SMaterial.h" />
    <ClInclude Include="..\..\include\SMaterialLayer.h" />
    <ClInclude Include="..\..\include\aabbox3d.h" />
    <ClInclude Include="..\..\include\coreutil.h" />
    <ClInclude Include="..\..\include\dimension2d.h" />
    <ClInclude Include="..\..\include\heapsort.h" />
    <ClInclude Include="..\..\include\irrAllocator.h" />
    <ClInclude Include="..\..\include\irrArray.h" />
    <ClInclude Include="..\..\include\irrList.h" />
    <ClInclude Include="..\..\include\irrMap.h" />
    <ClInclude Include="..\..\include\irrMath.h" />
    <ClInclude Include="..\..\include\irrString.h" />
    <ClInclude Include="..\..\include\line2d.h" />
    <ClInclude Include="..\..\include\line3d.h" />
    <ClInclude Include="..\..\include\matrix4.h" />
    <ClInclude Include="..\..\include\plane3d.h" />
    <ClInclude Include="..\..\include\position2d.h" />
    <ClInclude Include="..\..\include\quaternion.h" />
    <ClInclude Include="..\..\include\rect.h" />
    <ClInclude Include="..\..\include\triangle3d.h" />
    <ClInclude Include="..\..\include\vector2d.h" />
    <ClInclude Include="..\..\include\vector3d.h" />
    <ClInclude Include="..\..\include\EAttributes.h" />
    <ClInclude Include="..\..\include\IAttributeExchangingObject.h" />
    <ClInclude Include="..\..\include\IAttributes.h" />
    <ClInclude Include="..\..\include\IFileList.h" />
    <ClInclude Include="..\..\include\IFileSystem.h" />
    <ClInclude Include="..\..\include\IReadFile.h" />
    <ClInclude Include="..\..\include\irrXML.h" />
    <ClInclude Include="..\..\include\IWriteFile.h" />
    <ClInclude Include="..\..\include\IXMLReader.h" />
    <ClInclude Include="..\..\include\IXMLWriter.h" />
    <ClInclude Include="..\..\include\path.h" />
    <ClInclude Include="..\..\include\CDynamicMeshBuffer.h" />
    <ClInclude Include="..\..\include\CIndexBuffer.h" />
    <ClInclude Include="..\..\include\CMeshBuffer.h" />
    <ClInclude Include="..\..\include\CVertexBuffer.h" />
    <ClInclude Include="..\..\include\ECullingTypes.h" />
    <ClInclude Include="..\..\include\EDebugSceneTypes.h" />
    <ClInclude Include="..\..\include\EMeshWriterEnums.h" />
    <ClInclude Include="..\..\include\EPrimitiveTypes.h" />
    <ClInclude Include="..\..\include\ESceneNodeAnimatorTypes.h" />
    <ClInclude Include="..\..\include\ESceneNodeTypes.h" />
    <ClInclude Include="..\..\include\IAnimatedMesh.h" />
    <ClInclude Include="..\..\include\IAnimatedMeshMD2.h" />
    <ClInclude Include="..\..\include\IAnimatedMeshSceneNode.h" />
    <ClInclude Include="..\..\include\IBillboardSceneNode.h" />
    <ClInclude Include="..\..\include\ICameraSceneNode.h" />
    <ClInclude Include="..\..\include\IDummyTransformationSceneNode.h" />
    <ClInclude Include="..\..\include\ILightSceneNode.h" />
    <ClInclude Include="..\..\include\IMesh.h" />
    <ClInclude Include="..\..\include\IMeshBuffer.h" />
    <ClInclude Include="..\..\include\IMeshCache.h" />
    <ClInclude Include="..\..\include\IMeshLoader.h" />
    <ClInclude Include="..\..\include\IMeshManipulator.h" />
    <ClInclude Include="..\..\include\IMeshSceneNode.h" />
    <ClInclude Include="..\..\include\IMeshWriter.h" />
    <ClInclude Include="..\..\include\IMetaTriangleSelector.h" />
    <ClInclude Include="..\..\include\IParticleAffector.h" />
    <ClInclude Include="..\..\include\IParticleAnimatedMeshSceneNodeEmitter.h" />
    <ClInclude Include="..\..\include\IParticleAttractionAffector.h" />
    <ClInclude Include="..\..\include\IParticleBoxEmitter.h" />
    <ClInclude Include="..\..\include\IParticleCylinderEmitter.h" />
    <ClInclude Include="..\..\include\IParticleEmitter.h" />
    <ClInclude Include="..\..\include\IParticleFadeOutAffector.h" />
    <ClInclude Include="..\..\include\IParticleGravityAffector.h" />
    <ClInclude Include="..\..\include\IParticleMeshEmitter.h" />
    <ClInclude Include="..\..\include\IParticleRingEmitter.h" />
    <ClInclude Include="..\..\include\IParticleRotationAffector.h" />
    <ClInclude Include="..\..\include\IParticleSphereEmitter.h" />
    <ClInclude Include="..\..\include\IParticleSystemSceneNode.h" />
    <ClInclude Include="..\..\include\IQ3LevelMesh.h" />
    <ClInclude Include="..\..\include\IQ3Shader.h" />
    <ClInclude Include="..\..\include\ISceneCollisionManager.h" />
    <ClInclude Include="..\..\include\ISceneManager.h" />
    <ClInclude Include="..\..\include\ISceneLoader.h" />
    <ClInclude Include="..\..\include\ISceneNode.h" />
    <ClInclude Include="..\..\include\ISceneNodeAnimator.h" />
    <ClInclude Include="..\..\include\ISceneNodeAnimatorCameraFPS.h" />
    <ClInclude Include="..\..\include\ISceneNodeAnimatorCollisionResponse.h" />
    <ClInclude Include="..\..\include\ISceneNodeAnimatorFactory.h" />
    <ClInclude Include="..\..\include\ISceneNodeFactory.h" />
    <ClInclude Include="..\..\include\IShadowVolumeSceneNode.h" />
    <ClInclude Include="..\..\include\ISkinnedMesh.h" />
    <ClInclude Include="..\..\include\ITerrainSceneNode.h" />
    <ClInclude Include="..\..\include\ITextSceneNode.h" />
    <ClInclude Include="..\..\include\ITriangleSelector.h" />
    <ClInclude Include="..\..\include\IVolumeLightSceneNode.h" />
    <ClInclude Include="..\..\include\SAnimatedMesh.h" />
    <ClInclude Include="..\..\include\SceneParameters.h" />
    <ClInclude Include="..\..\include\SMesh.h" />
    <ClInclude Include="..\..\include\SMeshBuffer.h" />
    <ClInclude Include="..\..\include\SMeshBufferLightMap.h" />
    <ClInclude Include="..\..\include\SMeshBufferTangents.h" />
    <ClInclude Include="..\..\include\SParticle.h" />
    <ClInclude Include="..\..\include\SSkinMeshBuffer.h" />
    <ClInclude Include="..\..\include\SViewFrustum.h" />
    <ClInclude Include="..\..\include\EGUIAlignment.h" />
    <ClInclude Include="..\..\include\EGUIElementTypes.h" />
    <ClInclude Include="..\..\include\EMessageBoxFlags.h" />
    <ClInclude Include="..\..\include\ICursorControl.h" />
    <ClInclude Include="..\..\include\IGUIButton.h" />
    <ClInclude Include="..\..\include\IGUICheckbox.h" />
    <ClInclude Include="..\..\include\IGUIComboBox.h" />
    <ClInclude Include="..\..\include\IGUIContextMenu.h" />
    <ClInclude Include="..\..\include\IGUIEditBox.h" />
    <ClInclude Include="..\..\include\IGUIElement.h" />
    <ClInclude Include="..\..\include\IGUIElementFactory.h" />
    <ClInclude Include="..\..\include\IGUIEnvironment.h" />
    <ClInclude Include="..\..\include\IGUIFileOpenDialog.h" />
    <ClInclude Include="..\..\include\IGUIFont.h" />
    <ClInclude Include="..\..\include\IGUIImage.h" />
    <ClInclude Include="..\..\include\IGUIImageList.h" />
    <ClInclude Include="..\..\include\IGUIInOutFader.h" />
    <ClInclude Include="..\..\include\IGUIListBox.h" />
    <ClInclude Include="..\..\include\IGUIMeshViewer.h" />
    <ClInclude Include="..\..\include\IGUIScrollBar.h" />
    <ClInclude Include="..\..\include\IGUISkin.h" />
    <ClInclude Include="..\..\include\IGUISpinBox.h" />
    <ClInclude Include="..\..\include\IGUISpriteBank.h" />
    <ClInclude Include="..\..\include\IGUIStaticText.h" />
    <ClInclude Include="..\..\include\IGUITabControl.h" />
    <ClInclude Include="..\..\include\IGUIToolbar.h" />
    <ClInclude Include="..\..\include\IGUITreeView.h" />
    <ClInclude Include="..\..\include\IGUIWindow.h" />
    <ClInclude Include="BuiltInFont.h" />
    <ClInclude Include="C3DSMeshFileLoader.h" />
    <ClInclude Include="CAnimatedMeshHalfLife.h" />
    <ClInclude Include="CAnimatedMeshMD2.h" />
    <ClInclude Include="CAnimatedMeshMD3.h" />
    <ClInclude Include="CAnimatedMeshSceneNode.h" />
    <ClInclude Include="CAttributeImpl.h" />
    <ClInclude Include="CAttributes.h" />
    <ClInclude Include="CB3DMeshFileLoader.h" />
    <ClInclude Include="CBillboardSceneNode.h" />
    <ClInclude Include="CBoneSceneNode.h" />
    <ClInclude Include="CBSPMeshFileLoader.h" />
    <ClInclude Include="CCameraSceneNode.h" />
    <ClInclude Include="CColladaFileLoader.h" />
    <ClInclude Include="CColladaMeshWriter.h" />
    <ClInclude Include="CCSMLoader.h" />
    <ClInclude Include="CCubeSceneNode.h" />
    <ClInclude Include="CD3D9HardwareBuffer.h" />
    <ClInclude Include="CDefaultGUIElementFactory.h" />
    <ClInclude Include="CDefaultSceneNodeAnimatorFactory.h" />
    <ClInclude Include="CDefaultSceneNodeFactory.h" />
    <ClInclude Include="CDMFLoader.h" />
    <ClInclude Include="CDummyTransformationSceneNode.h" />
    <ClInclude Include="CEmptySceneNode.h" />
    <ClInclude Include="CFileList.h" />
    <ClInclude Include="CFileSystem.h" />
    <ClInclude Include="CGeometryCreator.h" />
    <ClInclude Include="CGUIButton.h" />
    <ClInclude Include="CGUICheckbox.h" />
    <ClInclude Include="CGUIColorSelectDialog.h" />
    <ClInclude Include="CGUIComboBox.h" />
    <ClInclude Include="CGUIContextMenu.h" />
    <ClInclude Include="CGUIEditBox.h" />
    <ClInclude Include="CGUIEnvironment.h" />
    <ClInclude Include="CGUIFileOpenDialog.h" />
    <ClInclude Include="CGUIFont.h" />
    <ClInclude Include="CGUIImage.h" />
    <ClInclude Include="CGUIImageList.h" />
    <ClInclude Include="CGUIInOutFader.h" />
    <ClInclude Include="CGUIListBox.h" />
    <ClInclude Include="CGUIMenu.h" />
    <ClInclude Include="CGUIMeshViewer.h" />
    <ClInclude Include="CGUIMessageBox.h" />
    <ClInclude Include="CGUIModalScreen.h" />
    <ClInclude Include="CGUIScrollBar.h" />
    <ClInclude Include="CGUISkin.h" />
    <ClInclude Include="CGUISpinBox.h" />
    <ClInclude Include="CGUISpriteBank.h" />
    <ClInclude Include="CGUIStaticText.h" />
    <ClInclude Include="CGUITabControl.h" />
    <ClInclude Include="CGUITable.h" />
    <ClInclude Include="CGUIToolBar.h" />
    <ClInclude Include="CGUITreeView.h" />
    <ClInclude Include="CGUIWindow.h" />
    <ClInclude Include="CImageLoaderSTB.h" />
    <ClInclude Include="CImageLoaderWIC.h" />
    <ClInclude Include="CImageWriterSTB.h" />
    <ClInclude Include="CIrrMeshFileLoader.h" />
    <ClInclude Include="CIrrMeshWriter.h" />
    <ClInclude Include="CLightSceneNode.h" />
    <ClInclude Include="CLimitReadFile.h" />
    <ClInclude Include="CLMTSMeshFileLoader.h" />
    <ClInclude Include="CLWOMeshFileLoader.h" />
    <ClInclude Include="CMD2MeshFileLoader.h" />
    <ClInclude Include="CMD3MeshFileLoader.h" />
    <ClInclude Include="CMemoryFile.h" />
    <ClInclude Include="CMeshCache.h" />
    <ClInclude Include="CMeshManipulator.h" />
    <ClInclude Include="CMeshSceneNode.h" />
    <ClInclude Include="CMetaTriangleSelector.h" />
    <ClInclude Include="CMountPointReader.h" />
    <ClInclude Include="CMS3DMeshFileLoader.h" />
    <ClInclude Include="CMY3DHelper.h" />
    <ClInclude Include="CMY3DMeshFileLoader.h" />
    <ClInclude Include="CNPKReader.h" />
    <ClInclude Include="COBJMeshFileLoader.h" />
    <ClInclude Include="COBJMeshWriter.h" />
    <ClInclude Include="COCTLoader.h" />
    <ClInclude Include="COctreeSceneNode.h" />
    <ClInclude Include="COctreeTriangleSelector.h" />
    <ClInclude Include="COgreMeshFileLoader.h" />
    <ClInclude Include="COpenGLCgMaterialRenderer.h" />
    <ClInclude Include="COpenGLDriver.h" />
    <ClInclude Include="COpenGLExtensionHandler.h" />
    <ClInclude Include="COpenGLMaterialRenderer.h" />
    <ClInclude Include="COpenGLNormalMapRenderer.h" />
    <ClInclude Include="COpenGLParallaxMapRenderer.h" />
    <ClInclude Include="COpenGLShaderMaterialRenderer.h" />
    <ClInclude Include="COpenGLSLMaterialRenderer.h" />
    <ClInclude Include="COpenGLTexture.h" />
    <ClInclude Include="CPakReader.h" />
    <ClInclude Include="CParticleAnimatedMeshSceneNodeEmitter.h" />
    <ClInclude Include="CParticleAttractionAffector.h" />
    <ClInclude Include="CParticleBoxEmitter.h" />
    <ClInclude Include="CParticleCylinderEmitter.h" />
    <ClInclude Include="CParticleFadeOutAffector.h" />
    <ClInclude Include="CParticleGravityAffector.h" />
    <ClInclude Include="CParticleMeshEmitter.h" />
    <ClInclude Include="CParticlePointEmitter.h" />
    <ClInclude Include="CParticleRingEmitter.h" />
    <ClInclude Include="CParticleRotationAffector.h" />
    <ClInclude Include="CParticleScaleAffector.h" />
    <ClInclude Include="CParticleSphereEmitter.h" />
    <ClInclude Include="CParticleSystemSceneNode.h" />
    <ClInclude Include="CPLYMeshFileLoader.h" />
    <ClInclude Include="CPLYMeshWriter.h" />
    <ClInclude Include="CQ3LevelMesh.h" />
    <ClInclude Include="CQuake3ShaderSceneNode.h" />
    <ClInclude Include="CReadFile.h" />
    <ClInclude Include="CSceneCollisionManager.h" />
    <ClInclude Include="CSceneLoaderIrr.h" />
    <ClInclude Include="CSceneManager.h" />
    <ClInclude Include="CSceneNodeAnimatorCameraFPS.h" />
    <ClInclude Include="CSceneNodeAnimatorCameraTouchControl.h" />
    <ClInclude Include="CSceneNodeAnimatorCollisionResponse.h" />
    <ClInclude Include="CSceneNodeAnimatorDelete.h" />
    <ClInclude Include="CSceneNodeAnimatorFlyCircle.h" />
    <ClInclude Include="CSceneNodeAnimatorFlyStraight.h" />
    <ClInclude Include="CSceneNodeAnimatorFollowSpline.h" />
    <ClInclude Include="CSceneNodeAnimatorRotation.h" />
    <ClInclude Include="CSceneNodeAnimatorTexture.h" />
    <ClInclude Include="CShadowVolumeSceneNode.h" />
    <ClInclude Include="CSkinnedMesh.h" />
    <ClInclude Include="CSkyBoxSceneNode.h" />
    <ClInclude Include="CSkyDomeSceneNode.h" />
    <ClInclude Include="CSMFMeshFileLoader.h" />
    <ClInclude Include="CSphereSceneNode.h" />
    <ClInclude Include="CSTLMeshFileLoader.h" />
    <ClInclude Include="CSTLMeshWriter.h" />
    <ClInclude Include="CTarReader.h" />
    <ClInclude Include="CTerrainSceneNode.h" />
    <ClInclude Include="CTerrainTriangleSelector.h" />
    <ClInclude Include="CTextSceneNode.h" />
    <ClInclude Include="CTriangleBBSelector.h" />
    <ClInclude Include="CTriangleSelector.h" />
    <ClInclude Include="CVideoModeList.h" />
    <ClInclude Include="CVolumeLightSceneNode.h" />
    <ClInclude Include="CWADReader.h" />
    <ClInclude Include="CWaterSurfaceSceneNode.h" />
    <ClInclude Include="CWriteFile.h" />
    <ClInclude Include="CXMeshFileLoader.h" />
    <ClInclude Include="CXMLReader.h" />
    <ClInclude Include="CXMLReaderImpl.h" />
    <ClInclude Include="CXMLWriter.h" />
    <ClInclude Include="CZipReader.h" />
    <ClInclude Include="dmfsupport.h" />
    <ClInclude Include="IAttribute.h" />
    <ClInclude Include="Octree.h" />
    <ClInclude Include="resource.h" />
    <ClInclude Include="CColorConverter.h" />
    <ClInclude Include="CFPSCounter.h" />
    <ClInclude Include="CImage.h" />
    <ClInclude Include="CNullDriver.h" />
    <ClInclude Include="IImagePresenter.h" />
    <ClInclude Include="CImageWriterBMP.h" />
    <ClInclude Include="CImageWriterJPG.h" />
    <ClInclude Include="CImageWriterPCX.h" />
    <ClInclude Include="CImageWriterPNG.h" />
    <ClInclude Include="CImageWriterPPM.h" />
    <ClInclude Include="CImageWriterPSD.h" />
    <ClInclude Include="CImageWriterTGA.h" />
    <ClInclude Include="CImageLoaderBMP.h" />
    <ClInclude Include="CImageLoaderDDS.h" />
    <ClInclude Include="CImageLoaderJPG.h" />
    <ClInclude Include="CImageLoaderPCX.h" />
    <ClInclude Include="CImageLoaderPNG.h" />
    <ClInclude Include="CImageLoaderPPM.h" />
    <ClInclude Include="CImageLoaderPSD.h" />
    <ClInclude Include="CImageLoaderRGB.h" />
    <ClInclude Include="CImageLoaderTGA.h" />
    <ClInclude Include="CImageLoaderWAL.h" />
    <ClInclude Include="CD3D9Driver.h" />
    <ClInclude Include="CD3D9HLSLMaterialRenderer.h" />
    <ClInclude Include="CD3D9MaterialRenderer.h" />
    <ClInclude Include="CD3D9NormalMapRenderer.h" />
    <ClInclude Include="CD3D9ParallaxMapRenderer.h" />
    <ClInclude Include="CD3D9ShaderMaterialRenderer.h" />
    <ClInclude Include="CD3D9Texture.h" />
    <ClInclude Include="CLogger.h" />
    <ClInclude Include="COSOperator.h" />
    <ClInclude Include="CTimer.h" />
    <ClInclude Include="os.h" />
    <ClInclude Include="CIrrDeviceStub.h" />
    <ClInclude Include="VulkanRenderer\base\threadpool.hpp" />
    <ClInclude Include="VulkanRenderer\base\VulkanBuffer.hpp" />
    <ClInclude Include="VulkanRenderer\base\VulkanDebug.h" />
    <ClInclude Include="VulkanRenderer\base\VulkanDevice.hpp" />
    <ClInclude Include="VulkanRenderer\base\VulkanSwapChain.hpp" />
    <ClInclude Include="VulkanRenderer\base\VulkanTexture.hpp" />
    <ClInclude Include="VulkanRenderer\base\VulkanTools.h" />
    <ClInclude Include="VulkanRenderer\base\VulkanUIOverlay.h" />
    <ClInclude Include="VulkanRenderer\DefineShareWithShader.h" />
    <ClInclude Include="VulkanRenderer\VkDefineShareWithShader.h" />
    <ClInclude Include="VulkanRenderer\VkDriver.h" />
    <ClInclude Include="VulkanRenderer\VkDriverBase.h" />
    <ClInclude Include="VulkanRenderer\VkFixedFunctionMaterialRenderer.h" />
    <ClInclude Include="VulkanRenderer\VkHardwareBuffer.h" />
    <ClInclude Include="VulkanRenderer\VkHeader.h" />
    <ClInclude Include="VulkanRenderer\VkMaterialRenderer.h" />
    <ClInclude Include="VulkanRenderer\VkMr2D.h" />
    <ClInclude Include="VulkanRenderer\VkMrFF_MMD.h" />
    <ClInclude Include="VulkanRenderer\VkMrFF_SSAO.h" />
    <ClInclude Include="VulkanRenderer\VkRaytracing.h" />
    <ClInclude Include="VulkanRenderer\VkAccelerationStructure.h" />
    <ClInclude Include="VulkanRenderer\VkRaytracingPipeline.h" />
    <ClInclude Include="VulkanRenderer\VkRaytracingSceneExample.h" />
    <ClInclude Include="VulkanRenderer\VkRaytracingSceneManager.h" />
    <ClInclude Include="VulkanRenderer\VkRaytracingShaders.h" />
    <ClInclude Include="VulkanRenderer\VkRaytracingDemo.h" />
    <ClInclude Include="VulkanRenderer\shader\RaytracingShared.h" />
    <ClInclude Include="VulkanRenderer\VkShaderMan\VkFxBase.h" />
    <ClInclude Include="VulkanRenderer\VkShaderMan\VkFxConstantBuffer.h" />
    <ClInclude Include="VulkanRenderer\VkShaderMan\VkFxDescriptorSetManager.h" />
    <ClInclude Include="VulkanRenderer\VkShaderMan\VkFxShaderRunner.h" />
    <ClInclude Include="VulkanRenderer\VkTexture.h" />
    <ClInclude Include="VulkanRenderer\VkVertexDeclaration.h" />
    <ClInclude Include="VulkanRenderer\vulkanRenderPass.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\..\external\ImGuizmo\ImCurveEdit.cpp" />
    <ClCompile Include="..\..\external\ImGuizmo\ImGradient.cpp" />
    <ClCompile Include="..\..\external\ImGuizmo\ImGuizmo.cpp" />
    <ClCompile Include="..\..\external\ImGuizmo\ImSequencer.cpp" />
    <ClCompile Include="..\..\external\imgui\backends\imgui_impl_win32.cpp" />
    <ClCompile Include="..\..\external\imgui\imgui.cpp" />
    <ClCompile Include="..\..\external\imgui\imgui_draw.cpp" />
    <ClCompile Include="..\..\external\imgui\imgui_tables.cpp" />
    <ClCompile Include="..\..\external\imgui\imgui_widgets.cpp" />
    <ClCompile Include="..\..\external\imgui\misc\cpp\imgui_stdlib.cpp" />
    <ClCompile Include="..\..\include\ISceneNodeAnimator.cpp" />
    <ClCompile Include="C3DSMeshFileLoader.cpp" />
    <ClCompile Include="CAnimatedMeshHalfLife.cpp" />
    <ClCompile Include="CAnimatedMeshMD2.cpp" />
    <ClCompile Include="CAnimatedMeshMD3.cpp" />
    <ClCompile Include="CAnimatedMeshSceneNode.cpp" />
    <ClCompile Include="CAttributes.cpp" />
    <ClCompile Include="CB3DMeshFileLoader.cpp" />
    <ClCompile Include="CBillboardSceneNode.cpp" />
    <ClCompile Include="CBoneSceneNode.cpp" />
    <ClCompile Include="CBSPMeshFileLoader.cpp" />
    <ClCompile Include="CCameraSceneNode.cpp" />
    <ClCompile Include="CColladaFileLoader.cpp" />
    <ClCompile Include="CColladaMeshWriter.cpp" />
    <ClCompile Include="CCSMLoader.cpp" />
    <ClCompile Include="CCubeSceneNode.cpp" />
    <ClCompile Include="CD3D9HardwareBuffer.cpp" />
    <ClCompile Include="CDefaultGUIElementFactory.cpp" />
    <ClCompile Include="CDefaultSceneNodeAnimatorFactory.cpp" />
    <ClCompile Include="CDefaultSceneNodeFactory.cpp" />
    <ClCompile Include="CDMFLoader.cpp" />
    <ClCompile Include="CDummyTransformationSceneNode.cpp" />
    <ClCompile Include="CEmptySceneNode.cpp" />
    <ClCompile Include="CFileList.cpp" />
    <ClCompile Include="CFileSystem.cpp" />
    <ClCompile Include="CGeometryCreator.cpp" />
    <ClCompile Include="CGUIButton.cpp" />
    <ClCompile Include="CGUICheckbox.cpp" />
    <ClCompile Include="CGUIColorSelectDialog.cpp" />
    <ClCompile Include="CGUIComboBox.cpp" />
    <ClCompile Include="CGUIContextMenu.cpp" />
    <ClCompile Include="CGUIEditBox.cpp" />
    <ClCompile Include="CGUIEnvironment.cpp" />
    <ClCompile Include="CGUIFileOpenDialog.cpp" />
    <ClCompile Include="CGUIFont.cpp" />
    <ClCompile Include="CGUIImage.cpp" />
    <ClCompile Include="CGUIImageList.cpp" />
    <ClCompile Include="CGUIInOutFader.cpp" />
    <ClCompile Include="CGUIListBox.cpp" />
    <ClCompile Include="CGUIMenu.cpp" />
    <ClCompile Include="CGUIMeshViewer.cpp" />
    <ClCompile Include="CGUIMessageBox.cpp" />
    <ClCompile Include="CGUIModalScreen.cpp" />
    <ClCompile Include="CGUIScrollBar.cpp" />
    <ClCompile Include="CGUISkin.cpp" />
    <ClCompile Include="CGUISpinBox.cpp" />
    <ClCompile Include="CGUISpriteBank.cpp" />
    <ClCompile Include="CGUIStaticText.cpp" />
    <ClCompile Include="CGUITabControl.cpp" />
    <ClCompile Include="CGUITable.cpp" />
    <ClCompile Include="CGUIToolBar.cpp" />
    <ClCompile Include="CGUITreeView.cpp" />
    <ClCompile Include="CGUIWindow.cpp" />
    <ClCompile Include="CImageLoaderSTB.cpp" />
    <ClCompile Include="CImageLoaderWIC.cpp" />
    <ClCompile Include="CImageWriterSTB.cpp" />
    <ClCompile Include="CIrrDeviceWin32.cpp" />
    <ClCompile Include="CIrrMeshFileLoader.cpp" />
    <ClCompile Include="CIrrMeshWriter.cpp" />
    <ClCompile Include="CLightSceneNode.cpp" />
    <ClCompile Include="CLimitReadFile.cpp" />
    <ClCompile Include="CLMTSMeshFileLoader.cpp" />
    <ClCompile Include="CLWOMeshFileLoader.cpp" />
    <ClCompile Include="CMD2MeshFileLoader.cpp" />
    <ClCompile Include="CMD3MeshFileLoader.cpp" />
    <ClCompile Include="CMemoryFile.cpp" />
    <ClCompile Include="CMeshCache.cpp" />
    <ClCompile Include="CMeshManipulator.cpp" />
    <ClCompile Include="CMeshSceneNode.cpp" />
    <ClCompile Include="CMetaTriangleSelector.cpp" />
    <ClCompile Include="CMountPointReader.cpp" />
    <ClCompile Include="CMS3DMeshFileLoader.cpp" />
    <ClCompile Include="CMY3DMeshFileLoader.cpp" />
    <ClCompile Include="CNPKReader.cpp" />
    <ClCompile Include="COBJMeshFileLoader.cpp" />
    <ClCompile Include="COBJMeshWriter.cpp" />
    <ClCompile Include="COCTLoader.cpp" />
    <ClCompile Include="COctreeSceneNode.cpp" />
    <ClCompile Include="COctreeTriangleSelector.cpp" />
    <ClCompile Include="COgreMeshFileLoader.cpp" />
    <ClCompile Include="COpenGLCgMaterialRenderer.cpp" />
    <ClCompile Include="COpenGLDriver.cpp" />
    <ClCompile Include="COpenGLExtensionHandler.cpp" />
    <ClCompile Include="COpenGLNormalMapRenderer.cpp" />
    <ClCompile Include="COpenGLParallaxMapRenderer.cpp" />
    <ClCompile Include="COpenGLShaderMaterialRenderer.cpp" />
    <ClCompile Include="COpenGLSLMaterialRenderer.cpp" />
    <ClCompile Include="COpenGLTexture.cpp" />
    <ClCompile Include="CPakReader.cpp" />
    <ClCompile Include="CParticleAnimatedMeshSceneNodeEmitter.cpp" />
    <ClCompile Include="CParticleAttractionAffector.cpp" />
    <ClCompile Include="CParticleBoxEmitter.cpp" />
    <ClCompile Include="CParticleCylinderEmitter.cpp" />
    <ClCompile Include="CParticleFadeOutAffector.cpp" />
    <ClCompile Include="CParticleGravityAffector.cpp" />
    <ClCompile Include="CParticleMeshEmitter.cpp" />
    <ClCompile Include="CParticlePointEmitter.cpp" />
    <ClCompile Include="CParticleRingEmitter.cpp" />
    <ClCompile Include="CParticleRotationAffector.cpp" />
    <ClCompile Include="CParticleScaleAffector.cpp" />
    <ClCompile Include="CParticleSphereEmitter.cpp" />
    <ClCompile Include="CParticleSystemSceneNode.cpp" />
    <ClCompile Include="CPLYMeshFileLoader.cpp" />
    <ClCompile Include="CPLYMeshWriter.cpp" />
    <ClCompile Include="CQ3LevelMesh.cpp" />
    <ClCompile Include="CQuake3ShaderSceneNode.cpp" />
    <ClCompile Include="CReadFile.cpp" />
    <ClCompile Include="CSceneCollisionManager.cpp" />
    <ClCompile Include="CSceneLoaderIrr.cpp" />
    <ClCompile Include="CSceneManager.cpp" />
    <ClCompile Include="CSceneNodeAnimatorCameraFPS.cpp" />
    <ClCompile Include="CSceneNodeAnimatorCameraTouchControl.cpp" />
    <ClCompile Include="CSceneNodeAnimatorCollisionResponse.cpp" />
    <ClCompile Include="CSceneNodeAnimatorDelete.cpp" />
    <ClCompile Include="CSceneNodeAnimatorFlyCircle.cpp" />
    <ClCompile Include="CSceneNodeAnimatorFlyStraight.cpp" />
    <ClCompile Include="CSceneNodeAnimatorFollowSpline.cpp" />
    <ClCompile Include="CSceneNodeAnimatorFollowSplineRotation.cpp" />
    <ClCompile Include="CSceneNodeAnimatorRotation.cpp" />
    <ClCompile Include="CSceneNodeAnimatorTexture.cpp" />
    <ClCompile Include="CShadowVolumeSceneNode.cpp" />
    <ClCompile Include="CSkinnedMesh.cpp" />
    <ClCompile Include="CSkyBoxSceneNode.cpp" />
    <ClCompile Include="CSkyDomeSceneNode.cpp" />
    <ClCompile Include="CSMFMeshFileLoader.cpp" />
    <ClCompile Include="CSphereSceneNode.cpp" />
    <ClCompile Include="CSTLMeshFileLoader.cpp" />
    <ClCompile Include="CSTLMeshWriter.cpp" />
    <ClCompile Include="CTarReader.cpp" />
    <ClCompile Include="CTerrainSceneNode.cpp" />
    <ClCompile Include="CTerrainTriangleSelector.cpp" />
    <ClCompile Include="CTextSceneNode.cpp" />
    <ClCompile Include="CTriangleBBSelector.cpp" />
    <ClCompile Include="CTriangleSelector.cpp" />
    <ClCompile Include="CVideoModeList.cpp" />
    <ClCompile Include="CColorConverter.cpp" />
    <ClCompile Include="CFPSCounter.cpp" />
    <ClCompile Include="CImage.cpp" />
    <ClCompile Include="CNullDriver.cpp" />
    <ClCompile Include="CImageWriterBMP.cpp" />
    <ClCompile Include="CImageWriterJPG.cpp" />
    <ClCompile Include="CImageWriterPCX.cpp" />
    <ClCompile Include="CImageWriterPNG.cpp" />
    <ClCompile Include="CImageWriterPPM.cpp" />
    <ClCompile Include="CImageWriterPSD.cpp" />
    <ClCompile Include="CImageWriterTGA.cpp" />
    <ClCompile Include="CImageLoaderBMP.cpp" />
    <ClCompile Include="CImageLoaderDDS.cpp" />
    <ClCompile Include="CImageLoaderJPG.cpp" />
    <ClCompile Include="CImageLoaderPCX.cpp" />
    <ClCompile Include="CImageLoaderPNG.cpp" />
    <ClCompile Include="CImageLoaderPPM.cpp" />
    <ClCompile Include="CImageLoaderPSD.cpp" />
    <ClCompile Include="CImageLoaderRGB.cpp" />
    <ClCompile Include="CImageLoaderTGA.cpp" />
    <ClCompile Include="CImageLoaderWAL.cpp" />
    <ClCompile Include="CD3D9Driver.cpp" />
    <ClCompile Include="CD3D9HLSLMaterialRenderer.cpp" />
    <ClCompile Include="CD3D9NormalMapRenderer.cpp" />
    <ClCompile Include="CD3D9ParallaxMapRenderer.cpp" />
    <ClCompile Include="CD3D9ShaderMaterialRenderer.cpp" />
    <ClCompile Include="CD3D9Texture.cpp" />
    <ClCompile Include="CLogger.cpp" />
    <ClCompile Include="COSOperator.cpp" />
    <ClCompile Include="CVolumeLightSceneNode.cpp" />
    <ClCompile Include="CWADReader.cpp" />
    <ClCompile Include="CWaterSurfaceSceneNode.cpp" />
    <ClCompile Include="CWriteFile.cpp" />
    <ClCompile Include="CXMeshFileLoader.cpp" />
    <ClCompile Include="CXMLReader.cpp" />
    <ClCompile Include="CXMLWriter.cpp" />
    <ClCompile Include="CZipReader.cpp" />
    <ClCompile Include="Irrlicht.cpp" />
    <ClCompile Include="irrXML.cpp" />
    <ClCompile Include="ISceneNode.cpp" />
    <ClCompile Include="leakHunter.cpp" />
    <ClCompile Include="os.cpp" />
    <ClCompile Include="CIrrDeviceStub.cpp" />
    <ClCompile Include="VulkanRenderer\base\VulkanBuffer.cpp" />
    <ClCompile Include="VulkanRenderer\base\VulkanDebug.cpp" />
    <ClCompile Include="VulkanRenderer\base\VulkanTools.cpp" />
    <ClCompile Include="VulkanRenderer\base\VulkanUIOverlay.cpp" />
    <ClCompile Include="VulkanRenderer\VkDriver.cpp" />
    <ClCompile Include="VulkanRenderer\VkDriverBase.cpp" />
    <ClCompile Include="VulkanRenderer\VkDriverRaytracing.cpp" />
    <ClCompile Include="VulkanRenderer\VkAccelerationStructure.cpp" />
    <ClCompile Include="VulkanRenderer\VkRaytracing.cpp" />
    <ClCompile Include="VulkanRenderer\VkRaytracingPipeline.cpp" />
    <ClCompile Include="VulkanRenderer\VkRaytracingSceneExample.cpp" />
    <ClCompile Include="VulkanRenderer\VkRaytracingSceneManager.cpp" />
    <ClCompile Include="VulkanRenderer\VkRaytracingShaders.cpp" />
    <ClCompile Include="VulkanRenderer\VkRaytracingDemo.cpp" />
    <ClCompile Include="VulkanRenderer\VkFixedFunctionMaterialRenderer.cpp" />
    <ClCompile Include="VulkanRenderer\VkHardwareBuffer.cpp" />
    <ClCompile Include="VulkanRenderer\VkMaterialRenderer.cpp" />
    <ClCompile Include="VulkanRenderer\VkMr2D.cpp" />
    <ClCompile Include="VulkanRenderer\VkMrFF_MMD.cpp" />
    <ClCompile Include="VulkanRenderer\VkMrFF_SSAO.cpp" />
    <ClCompile Include="VulkanRenderer\VkShaderMan\VkFxBase.cpp" />
    <ClCompile Include="VulkanRenderer\VkShaderMan\VkFxDescriptorSetManager.cpp" />
    <ClCompile Include="VulkanRenderer\VkShaderMan\VkFxShaderRunner.cpp" />
    <ClCompile Include="VulkanRenderer\VkTexture.cpp" />
    <ClCompile Include="VulkanRenderer\VkVertexDeclaration.cpp" />
    <ClCompile Include="VulkanRenderer\vulkanRenderPass.cpp" />
    <ClCompile Include="zlib\adler32.c" />
    <ClCompile Include="zlib\compress.c" />
    <ClCompile Include="zlib\crc32.c" />
    <ClCompile Include="zlib\deflate.c" />
    <ClCompile Include="zlib\gzclose.c" />
    <ClCompile Include="zlib\gzlib.c" />
    <ClCompile Include="zlib\gzread.c" />
    <ClCompile Include="zlib\gzwrite.c" />
    <ClCompile Include="zlib\infback.c" />
    <ClCompile Include="zlib\inffast.c" />
    <ClCompile Include="zlib\inflate.c" />
    <ClCompile Include="zlib\inftrees.c" />
    <ClCompile Include="zlib\trees.c" />
    <ClCompile Include="zlib\uncompr.c" />
    <ClCompile Include="zlib\zutil.c" />
  </ItemGroup>
  <ItemGroup>
    <None Include="cpp.hint" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>