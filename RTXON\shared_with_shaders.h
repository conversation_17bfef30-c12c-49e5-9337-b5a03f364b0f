#ifndef SHARED_WITH_SHADERS_H
#define SHARED_WITH_SHADERS_H

/**
 * 着色器共享头文件 - 定义所有着色器共用的常量、结构和函数
 * 注意: 在C++和GLSL中均可使用
 */

#ifdef __cplusplus
// 包含向量和矩阵类型(与GLSL命名相同)
#include "framework/common.h"
#endif // __cplusplus

// 着色器绑定表索引定义
#define SWS_PRIMARY_HIT_SHADERS_IDX     0  // 主射线命中着色器索引
#define SWS_PRIMARY_MISS_SHADERS_IDX    0  // 主射线未命中着色器索引
#define SWS_SHADOW_HIT_SHADERS_IDX      1  // 阴影射线命中着色器索引
#define SWS_SHADOW_MISS_SHADERS_IDX     1  // 阴影射线未命中着色器索引

// 资源绑定位置定义
#define SWS_SCENE_AS_SET                0  // 场景加速结构描述符集
#define SWS_SCENE_AS_BINDING            0  // 场景加速结构绑定位置
#define SWS_RESULT_IMAGE_SET            0  // 结果图像描述符集
#define SWS_RESULT_IMAGE_BINDING        1  // 结果图像绑定位置
#define SWS_CAMDATA_SET                 0  // 相机数据描述符集
#define SWS_CAMDATA_BINDING             2  // 相机数据绑定位置

// 材质和几何数据描述符集
#define SWS_MATIDS_SET                  1  // 材质ID缓冲区描述符集
#define SWS_ATTRIBS_SET                 2  // 顶点属性缓冲区描述符集
#define SWS_FACES_SET                   3  // 面索引缓冲区描述符集
#define SWS_TEXTURES_SET                4  // 纹理数组描述符集
#define SWS_ENVS_SET                    5  // 环境贴图描述符集

#define SWS_NUM_SETS                    6  // 描述符集总数

// 跨着色器位置定义
#define SWS_LOC_PRIMARY_RAY             0  // 主射线payload位置
#define SWS_LOC_HIT_ATTRIBS             1  // 命中属性位置
#define SWS_LOC_SHADOW_RAY              2  // 阴影射线payload位置

#define SWS_MAX_RECURSION               10  // 最大递归深度

// 对象ID定义
#define OBJECT_ID_BUNNY                 0.0f  // 兔子模型ID
#define OBJECT_ID_PLANE                 1.0f  // 平面模型ID
#define OBJECT_ID_TEAPOT                2.0f  // 茶壶模型ID


/**
 * 主射线payload结构
 * - colorAndDist.rgb: 命中颜色
 * - colorAndDist.a: 命中距离(未命中时为-1)
 * - normalAndObjId.xyz: 命中点法线
 * - normalAndObjId.w: 物体ID
 */
struct RayPayload {
    vec4 colorAndDist;
    vec4 normalAndObjId;
};

/**
 * 阴影射线payload结构
 * - distance: 命中距离(未命中时为-1)
 */
struct ShadowRayPayload {
    float distance;
};

/**
 * 顶点属性结构(std430布局)
 * - normal: 顶点法线(xyz) + 填充(w)
 * - uv: 顶点纹理坐标(xy) + 填充(zw)
 */
struct VertexAttribute {
    vec4 normal;
    vec4 uv;
};

/**
 * 统一参数缓冲区(std140布局)
 * 包含相机和光照参数
 */
struct UniformParams {
    // 光照参数
    vec4 sunPosAndAmbient;  // xyz: 太阳位置, w: 环境光强度

    // 相机参数
    vec4 camPos;         // 相机位置(xyz)
    vec4 camDir;         // 相机前方向(xyz)
    vec4 camUp;          // 相机上方向(xyz) 
    vec4 camSide;        // 相机右方向(xyz)
    vec4 camNearFarFov;  // x: 近平面, y: 远平面, z: FOV(弧度)
};


/**
 * 着色器辅助函数
 */

/**
 * 2D向量重心坐标插值
 * @param a,b,c 三角形三个顶点的属性值
 * @param barycentrics 重心坐标(alpha, beta, gamma)
 * @return 插值结果
 */
vec2 BaryLerp(vec2 a, vec2 b, vec2 c, vec3 barycentrics) {
    return a * barycentrics.x + b * barycentrics.y + c * barycentrics.z;
}

/**
 * 3D向量重心坐标插值 
 * @param a,b,c 三角形三个顶点的属性值
 * @param barycentrics 重心坐标(alpha, beta, gamma)
 * @return 插值结果
 */
vec3 BaryLerp(vec3 a, vec3 b, vec3 c, vec3 barycentrics) {
    return a * barycentrics.x + b * barycentrics.y + c * barycentrics.z;
}

/**
 * 线性空间转sRGB空间(单通道)
 * @param channel 线性空间颜色值[0,1]
 * @return sRGB空间颜色值[0,1]
 */
float LinearToSrgb(float channel) {
    if (channel <= 0.0031308f) {
        return 12.92f * channel;  // 线性部分
    } else {
        return 1.055f * pow(channel, 1.0f / 2.4f) - 0.055f;  // 伽马校正部分
    }
}

/**
 * 线性空间转sRGB空间(三通道)
 * @param linear 线性空间RGB颜色值[0,1]
 * @return sRGB空间RGB颜色值[0,1]
 */
vec3 LinearToSrgb(vec3 linear) {
    return vec3(LinearToSrgb(linear.r), LinearToSrgb(linear.g), LinearToSrgb(linear.b));
}

#endif // SHARED_WITH_SHADERS_H
