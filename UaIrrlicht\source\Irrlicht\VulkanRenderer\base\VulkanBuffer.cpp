#include "VulkanBuffer.hpp"
#include "VulkanDevice.hpp"
#include "VulkanDebug.h"
#include <mutex> 
using namespace vks;

VkResult vks::Buffer::createBuffer(VulkanDevice *device, VkBufferUsageFlags usageFlags,
 VkMemoryPropertyFlags memoryPropertyFlags, VkDeviceSize size, const void * data)
{
	if (hBuffer)
	{
		//destroy();
		//resize(size);
		//return	VK_SUCCESS;
		assert(hDevice);
		AddLostBuffers(hDevice, hBuffer, hMemory);
		hBuffer = VK_NULL_HANDLE;
		hMemory = VK_NULL_HANDLE;
		//descriptor = nullptr;
	}
	hDevice = device->logicalDevice;

	// Create the buffer handle
	VkBufferCreateInfo bufferCreateInfo = vks::initializers::bufferCreateInfo(usageFlags, size);


	VK_CHECK_RESULT(vkCreateBuffer(hDevice, &bufferCreateInfo, nullptr, &hBuffer));  //DEBUG SEE +3 line
	DP(("createBuffer VkBuffer 0x%p size=%u",hBuffer,uint32_t(size)));

	//DEBUG_UNFREED
	if (((uint64_t)hBuffer & 0xFFF) == 0x24d)
	{
 		//assert(0);
	}
 

	// Create the memory backing up the buffer handle
	VkMemoryRequirements memReqs;
	VkMemoryAllocateInfo memAlloc = vks::initializers::memoryAllocateInfo();
	vkGetBufferMemoryRequirements(hDevice, hBuffer, &memReqs);
	memAlloc.allocationSize = memReqs.size;
	// Find a memory type index that fits the properties of the buffer
	memAlloc.memoryTypeIndex = device->getMemoryType(memReqs.memoryTypeBits, memoryPropertyFlags);
	
	// If buffer uses device address, we need to set the memory allocate flag
	VkMemoryAllocateFlagsInfo allocFlagsInfo = {};
	if (usageFlags & VK_BUFFER_USAGE_SHADER_DEVICE_ADDRESS_BIT) {
		allocFlagsInfo.sType = VK_STRUCTURE_TYPE_MEMORY_ALLOCATE_FLAGS_INFO;
		allocFlagsInfo.flags = VK_MEMORY_ALLOCATE_DEVICE_ADDRESS_BIT;
		memAlloc.pNext = &allocFlagsInfo;
	}
	
	VK_CHECK_RESULT(vkAllocateMemory(hDevice, &memAlloc, nullptr, &hMemory));
	mMemFlag = device->memoryProperties.memoryTypes[memAlloc.memoryTypeIndex].propertyFlags;
	//DP(("req size=%d MEM size=%d, typeBits=%X typeIdx=%d MemFlag=%X",size, memReqs.size, memReqs.memoryTypeBits,memAlloc.memoryTypeIndex, mMemFlag));
	alignment = memReqs.alignment;
	mSize = memAlloc.allocationSize;
	mUsageFlags = usageFlags;
	mMemoryPropertyFlags = memoryPropertyFlags;

	// If a pointer to the buffer data has been passed, map the buffer and copy over the data
	if (data != nullptr)
	{
		VK_CHECK_RESULT(map());
		memcpy(mapped, data, size);
		if ((memoryPropertyFlags & VK_MEMORY_PROPERTY_HOST_COHERENT_BIT) == 0)
			flush();

		unmap();
	}

	// Initialize a default descriptor that covers the whole buffer size
	setupDescriptor();
#if DBG_VK_NAMES
	vks::debugmarker::setDeviceMemoryName(hDevice, hMemory, "vk:Buffer hMemory");
	vks::debugmarker::setBufferName(hDevice, hBuffer, "vk:Buffer ");
#endif
	// Attach the memory to the buffer object
	return bind();
}

void vks::Buffer::resize(VkDeviceSize newsize)
{
	if (newsize < mSize)
		throw;
	if (hMemory)
	{
		vkFreeMemory(hDevice, hMemory, nullptr);
		hMemory = VK_NULL_HANDLE;
	}
}

bool vks::Buffer::swapMemory(vks::Buffer& buf)
{
	std::swap(hMemory,buf.hMemory);
	bind();//not support
	throw;
	return mSize==buf.mSize;
}

/**
* Release all Vulkan resources held by this buffer
*/

void vks::Buffer::freeBufferAndMemory()
{
	if (hBuffer)
	{
		vkDestroyBuffer(hDevice, hBuffer, nullptr);
		hBuffer = VK_NULL_HANDLE;
	}
	if (hMemory)
	{
		vkFreeMemory(hDevice, hMemory, nullptr);
		hMemory = VK_NULL_HANDLE;
	}
	//if (descriptor) delete descriptor; descriptor = nullptr;
}


static std::vector<vks::Buffer::LostBuffer> LostBuffers;
static UP_LOCK_DECLVAR(mtLB);
void vks::Buffer::AddLostBuffers(VkDevice dev, VkBuffer buf, VkDeviceMemory mem)
{
	UP_LOCK_GUARD(mtLB);
	LostBuffers.push_back({dev, buf,mem });
}
void vks::Buffer::FreeLostBuffers()
{
	UP_LOCK_GUARD(mtLB);
#ifdef _DEBUG
	if (LostBuffers.size())
	{
		DP(("Many LostBuffers = %u", LostBuffers.size()));
	}
#endif
	for (auto &lb : LostBuffers)
	{
		if (lb.hBuffer)
		{
			vkDestroyBuffer(lb.hDevice, lb.hBuffer, nullptr);
		}
		if (lb.hMemory)
		{
			vkFreeMemory(lb.hDevice, lb.hMemory, nullptr);
		}
		//delete lb.descriptor;
	}
	LostBuffers.clear();
}
