#pragma once

#include "IrrCompileConfig.h"

namespace irr {
namespace video {

 

#if VK_ENABLE_RAYTRACING

//! Raytracing rendering modes
enum E_RAYTRACING_MODE {
    //! Raytracing disabled, use rasterization only
    ERM_RT_DISABLED = 0,
    
    //! Hybrid mode: RT reflections + raster base
    ERM_RT_REFLECTIONS_ONLY,
    
    //! Hybrid mode: RT shadows + raster base  
    ERM_RT_SHADOWS_ONLY,
    
    //! Global illumination with RT
    ERM_RT_GLOBAL_ILLUMINATION,
    
    //! Full path tracing mode
    ERM_RT_FULL_PATHTRACING,
    
    //! Count of raytracing modes
    ERM_RT_COUNT
};

//! Raytracing quality settings
enum E_RAYTRACING_QUALITY {
    //! Low quality - fewer bounces, lower resolution
    ERQ_RT_LOW = 0,
    
    //! Medium quality - balanced performance/quality
    ERQ_RT_MEDIUM,
    
    //! High quality - more bounces, higher resolution
    ERQ_RT_HIGH,
    
    //! Ultra quality - maximum bounces and resolution
    ERQ_RT_ULTRA,
    
    //! Count of quality levels
    ERQ_RT_COUNT
};

//! Raytracing denoising modes
enum E_RAYTRACING_DENOISER {
    //! No denoising
    ERD_RT_NONE = 0,
    
    //! Simple temporal denoising
    ERD_RT_TEMPORAL,
    
    //! Spatial denoising
    ERD_RT_SPATIAL,
    
    //! Advanced temporal + spatial denoising
    ERD_RT_ADVANCED,
    
    //! Count of denoiser types
    ERD_RT_COUNT
};

#endif // VK_ENABLE_RAYTRACING

} // namespace video
} // namespace irr 