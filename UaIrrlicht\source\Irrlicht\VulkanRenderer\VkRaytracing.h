#pragma once

#include "IrrCompileConfig.h"
#ifdef _IRR_COMPILE_WITH_VULKAN_

#include "VkHeader.h"
#if VK_ENABLE_RAYTRACING

#include "ERaytracingTypes.h"
#include "VkAccelerationStructure.h"

namespace irr {
namespace video {

// Forward declarations
class VkDriver;

//! Raytracing resource manager
//! This manages acceleration structures and raytracing pipelines
class VkRaytracingManager {
private:
    VkDriver* driver;
    core::array<VkBottomLevelAS*> bottomLevelAS;
    VkTopLevelAS* topLevelAS;
    bool isInitialized;

public:
    VkRaytracingManager(VkDriver* driver);
    ~VkRaytracingManager();

    //! Initialize the raytracing manager
    bool initialize();

    //! Shutdown and cleanup
    void shutdown();

    //! Create a bottom-level acceleration structure
    VkBottomLevelAS* createBottomLevelAS();

    //! Get the top-level acceleration structure (created automatically)
    VkTopLevelAS* getTopLevelAS() { return topLevelAS; }

    //! Update all acceleration structures that need rebuilding
    void updateAccelerationStructures();

    //! Check if raytracing is ready for use
    bool isReady() const;
};

// Placeholder for raytracing functionality
// This will be expanded in future phases
struct VkRaytracingPlaceholder {
    // Reserved for future raytracing implementation
    bool initialized = false;
};

} // namespace video
} // namespace irr

#endif // VK_ENABLE_RAYTRACING
#endif // _IRR_COMPILE_WITH_VULKAN_ 