#pragma once



#include "IrrCompileConfig.h"
#include "irrTypes.h"

#include "../Helpers/UPCOMMON.h"
#include "VulkanRenderer/shader/GsParticleShared.h"

#define VKR_DEBUG true


#define VK_ENABLE_NON_SOLID_FILL_MODE 1 //TODO

#define TODO_MULTISAMPLE 0
#define TODO_STREAM_OUT 0
#define IRR_HAS_OIT 1
#define MAX_MMD_COUNT 2560
#define DP_TEX_CREATE	0

#ifdef _WIN32
#define IRR_SC_BUFFER_DXGI_FORMAT  VK_FORMAT_B8G8R8A8_UNORM
#define IRR_SC_BUFFER_DXGI_FORMAT_SRGB  VK_FORMAT_B8G8R8A8_SRGB
#define FW_VTX_MAT_MAX  (1024*100)
#else
#define IRR_SC_BUFFER_DXGI_FORMAT  VK_FORMAT_R8G8B8A8_UNORM
#define IRR_SC_BUFFER_DXGI_FORMAT_SRGB VK_FORMAT_R8G8B8A8_SRGB
#define FW_VTX_MAT_MAX  (256)
#endif

//from vulkan ex
#ifdef _WIN32
//#pragma comment(linker, "/subsystem:windows")
//#include <windows.h>
//#include <fcntl.h>
//#include <io.h>
//#include <ShellScalingAPI.h>


#elif defined(VK_USE_PLATFORM_ANDROID_KHR)
#include <android/native_activity.h>
#include <android/asset_manager.h>
//#include <android_native_app_glue.h>
#include <sys/system_properties.h>
#include "VulkanAndroid.h"
#endif

#ifdef _IRR_WINDOWS_



#define WIN32_LEAN_AND_MEAN             // Exclude rarely-used stuff from Windows headers
// Windows Header Files
#define NOMINMAX
#include <windows.h>
#ifndef PLATFORM_WIN32
#   define PLATFORM_WIN32 1
#endif

#else

typedef unsigned long       DWORD;
typedef int                 BOOL;
typedef unsigned char       BYTE;
typedef unsigned short      WORD;
typedef float               FLOAT;
typedef FLOAT               *PFLOAT;
typedef BOOL            *PBOOL;
typedef BOOL             *LPBOOL;
typedef BYTE            *PBYTE;
typedef BYTE             *LPBYTE;
typedef int             *PINT;
typedef int              *LPINT;
typedef WORD            *PWORD;
typedef WORD             *LPWORD;
typedef long             *LPLONG;
typedef DWORD           *PDWORD;
typedef DWORD            *LPDWORD;
typedef void             *LPVOID;
typedef const void       *LPCVOID;

typedef int                 INT;
typedef unsigned int        UINT;
typedef unsigned int        *PUINT;
typedef void			*HWND;
#endif

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <assert.h>
#include <fstream>
#include <vector>
#include <exception>

#include "VkExTypes.h"

#define GLM_FORCE_RADIANS
#define GLM_FORCE_DEPTH_ZERO_TO_ONE
#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>

namespace irr {
	typedef glm::mat4 float4x4;
	typedef glm::mat3 float3x3;
	typedef glm::mat2 float2x2;

	typedef glm::vec2 float2;
	typedef glm::vec3 float3;
	typedef glm::vec4 float4;

	typedef glm::ivec2 int2;
	typedef glm::ivec3 int3;
	typedef glm::ivec4 int4;

	typedef glm::uvec2 uint2;
	typedef glm::uvec3 uint3;
	typedef glm::uvec4 uint4;
}
#define  DRV_TransposeMatrix(_X_) glm::transpose(_X_) //  _X_ // 
#define DRIVER_HAS_UI_INPUT 1

#define VK_DRIVER_USE_IRR_PIPELINE_STATUS 0
#define VK_USE_EXT_DEVICE_VAR  throw xxx
#define VK_USE_2D_FX_RENDERER 1





#define ALIGN16_MALLOC(t) ((t*)_aligned_malloc(sizeof(t),16))
#define ALIGN16_FREE(t) (_aligned_free(t))


#include "DefineShareWithShader.h"



#include "base/VulkanDebug.h"
#include "base/VulkanTools.h"

#define VK_USE_DYNAMIC_STATE_EXT VK_VERSION_1_3


namespace irr {
	namespace video {
		uint32_t getVkFormatBitsPerPixel(VkFormat format);
		uint32_t getVkFormatNumberOfComponents(VkFormat format);


		struct VkCmdBuf {
			VkCommandBuffer cmdBuf = VK_NULL_HANDLE;
			VkCommandPool cmdPool = VK_NULL_HANDLE;
		};
	}
}




