#include "VkRaytracing.h"
#include "VkDriver.h"
#include "VkHardwareBuffer.h"
#include "os.h"

#ifdef _IRR_COMPILE_WITH_VULKAN_
#if VK_ENABLE_RAYTRACING

namespace irr {
namespace video {

// =============================================================================
// VkRaytracingManager Implementation
// =============================================================================

VkRaytracingManager::VkRaytracingManager(VkDriver* driver)
    : driver(driver), topLevelAS(nullptr), isInitialized(false) {
}

VkRaytracingManager::~VkRaytracingManager() {
    shutdown();
}

bool VkRaytracingManager::initialize() {
    if (isInitialized || !driver) {
        return false;
    }

    // Create top-level acceleration structure
    topLevelAS = new VkTopLevelAS(driver);
    if (!topLevelAS || !topLevelAS->initialize()) {
        os::Printer::log("VkRaytracingManager: Failed to create top-level acceleration structure", ELL_ERROR);
        delete topLevelAS;
        topLevelAS = nullptr;
        return false;
    }

    isInitialized = true;
    os::Printer::log("VkRaytracingManager: Initialized successfully", ELL_INFORMATION);
    return true;
}

void VkRaytracingManager::shutdown() {
    if (!isInitialized) {
        return;
    }

    // Cleanup bottom-level acceleration structures
    for (u32 i = 0; i < bottomLevelAS.size(); ++i) {
        delete bottomLevelAS[i];
    }
    bottomLevelAS.clear();

    // Cleanup top-level acceleration structure
    delete topLevelAS;
    topLevelAS = nullptr;

    isInitialized = false;
    os::Printer::log("VkRaytracingManager: Shutdown completed", ELL_INFORMATION);
}

VkBottomLevelAS* VkRaytracingManager::createBottomLevelAS() {
    if (!isInitialized || !driver) {
        return nullptr;
    }

    VkBottomLevelAS* blas = new VkBottomLevelAS(driver);
    if (!blas || !blas->initialize()) {
        os::Printer::log("VkRaytracingManager: Failed to create bottom-level acceleration structure", ELL_ERROR);
        delete blas;
        return nullptr;
    }

    bottomLevelAS.push_back(blas);
    return blas;
}

void VkRaytracingManager::updateAccelerationStructures() {
    if (!isInitialized) {
        return;
    }

    // Update bottom-level acceleration structures that need rebuilding
    for (u32 i = 0; i < bottomLevelAS.size(); ++i) {
        if (bottomLevelAS[i] && bottomLevelAS[i]->needsRebuild()) {
            bottomLevelAS[i]->build();
        }
    }

    // Update top-level acceleration structure if needed
    if (topLevelAS && topLevelAS->needsRebuild()) {
        topLevelAS->build();
    }
}

bool VkRaytracingManager::isReady() const {
    return isInitialized && topLevelAS && topLevelAS->isValidAS();
}

} // namespace video
} // namespace irr

#endif // VK_ENABLE_RAYTRACING
#endif // _IRR_COMPILE_WITH_VULKAN_ 