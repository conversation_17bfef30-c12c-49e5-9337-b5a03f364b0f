# Phase 5: Pipeline Integration and Dispatch - Implementation Guide

## **Critical Missing Components (Based on Context7 Analysis)**

### **1. Ray Tracing Pipeline Creation (HIGHEST PRIORITY)**

According to Context7, we need to implement `vkCreateRayTracingPipelinesKHR` wrapper with proper shader group configuration.

#### **Required Components:**
```cpp
// VkRaytracingPipeline.h - Add missing methods
class VkRaytracingPipeline {
private:
    VkPipeline rtPipeline;
    VkPipelineLayout pipelineLayout;
    
public:
    // MISSING: Core pipeline creation
    bool createPipeline(const VkRaytracingShaders* shaders);
    
    // MISSING: Shader group configuration  
    bool configureShaderGroups();
    
    // MISSING: Pipeline layout creation
    bool createPipelineLayout();
};
```

#### **Shader Group Types (Context7 Pattern):**
```cpp
// Required shader group configuration
VkRayTracingShaderGroupCreateInfoKHR groups[] = {
    // Ray generation group
    {
        .sType = VK_STRUCTURE_TYPE_RAY_TRACING_SHADER_GROUP_CREATE_INFO_KHR,
        .type = VK_RAY_TRACING_SHADER_GROUP_TYPE_GENERAL_KHR,
        .generalShader = 0, // Ray generation shader index
        .closestHitShader = VK_SHADER_UNUSED_KHR,
        .anyHitShader = VK_SHADER_UNUSED_KHR,
        .intersectionShader = VK_SHADER_UNUSED_KHR
    },
    // Miss group (primary)
    {
        .sType = VK_STRUCTURE_TYPE_RAY_TRACING_SHADER_GROUP_CREATE_INFO_KHR,
        .type = VK_RAY_TRACING_SHADER_GROUP_TYPE_GENERAL_KHR,
        .generalShader = 1, // Primary miss shader index
        // ... other fields VK_SHADER_UNUSED_KHR
    },
    // Miss group (shadow)
    {
        .sType = VK_STRUCTURE_TYPE_RAY_TRACING_SHADER_GROUP_CREATE_INFO_KHR,
        .type = VK_RAY_TRACING_SHADER_GROUP_TYPE_GENERAL_KHR,
        .generalShader = 2, // Shadow miss shader index
        // ... other fields VK_SHADER_UNUSED_KHR
    },
    // Hit group (primary)
    {
        .sType = VK_STRUCTURE_TYPE_RAY_TRACING_SHADER_GROUP_CREATE_INFO_KHR,
        .type = VK_RAY_TRACING_SHADER_GROUP_TYPE_TRIANGLES_HIT_GROUP_KHR,
        .generalShader = VK_SHADER_UNUSED_KHR,
        .closestHitShader = 3, // Primary closest hit shader index
        .anyHitShader = VK_SHADER_UNUSED_KHR,
        .intersectionShader = VK_SHADER_UNUSED_KHR
    },
    // Hit group (shadow)
    {
        .sType = VK_STRUCTURE_TYPE_RAY_TRACING_SHADER_GROUP_CREATE_INFO_KHR,
        .type = VK_RAY_TRACING_SHADER_GROUP_TYPE_TRIANGLES_HIT_GROUP_KHR,
        .generalShader = VK_SHADER_UNUSED_KHR,
        .closestHitShader = 4, // Shadow closest hit shader index
        .anyHitShader = VK_SHADER_UNUSED_KHR,
        .intersectionShader = VK_SHADER_UNUSED_KHR
    }
};
```

### **2. Shader Binding Table (SBT) Management (CRITICAL)**

Context7 emphasizes proper `VkStridedDeviceAddressRegionKHR` structure setup:

#### **Required SBT Structure:**
```cpp
class VkShaderBindingTable {
private:
    // Context7 pattern: Device address regions
    VkStridedDeviceAddressRegionKHR raygenRegion;
    VkStridedDeviceAddressRegionKHR missRegion;
    VkStridedDeviceAddressRegionKHR hitRegion;
    VkStridedDeviceAddressRegionKHR callableRegion;
    
    // Memory management
    VkHardwareBuffer* sbtBuffer;
    VkDeviceSize sbtSize;
    
public:
    // MISSING: Core SBT creation
    bool createSBT(VkDevice device, VkPipeline rtPipeline);
    
    // MISSING: Shader group handle management
    bool allocateShaderGroupHandles();
    
    // MISSING: Memory layout calculation
    VkDeviceSize calculateSBTSize();
    
    // MISSING: Device address setup
    bool setupDeviceAddressRegions();
};
```

#### **SBT Memory Layout (Context7 Pattern):**
```cpp
// Shader binding table layout calculation
struct SBTLayout {
    VkDeviceSize raygenOffset = 0;
    VkDeviceSize raygenSize;
    VkDeviceSize raygenStride;
    
    VkDeviceSize missOffset;
    VkDeviceSize missSize;
    VkDeviceSize missStride;
    
    VkDeviceSize hitOffset;
    VkDeviceSize hitSize;
    VkDeviceSize hitStride;
    
    VkDeviceSize totalSize;
};
```

### **3. Ray Tracing Dispatch (CRITICAL)**

Context7 shows `vkCmdTraceRaysKHR` as the key dispatch command:

#### **Required Dispatch Implementation:**
```cpp
// VkDriver.h - Add missing dispatch method
class VkDriver {
public:
    // MISSING: Core ray tracing dispatch
    bool dispatchRayTracing(
        u32 width, u32 height, u32 depth,
        const VkStridedDeviceAddressRegionKHR* raygenSBT,
        const VkStridedDeviceAddressRegionKHR* missSBT,
        const VkStridedDeviceAddressRegionKHR* hitSBT,
        const VkStridedDeviceAddressRegionKHR* callableSBT
    );
    
    // MISSING: Command buffer recording
    bool recordRayTracingCommands(VkCommandBuffer cmd);
    
    // MISSING: Resource binding before dispatch
    bool bindRayTracingResources();
};
```

### **4. Descriptor Set Integration (HIGH PRIORITY)**

Context7 emphasizes proper descriptor layout for RT resources:

#### **Required Descriptor Layouts:**
```cpp
// RT-specific descriptor set layouts
enum ERTDescriptorSet {
    RT_SET_GLOBAL = 0,    // Output image, acceleration structure, camera
    RT_SET_GEOMETRY = 1,  // Vertex/index buffers, materials
    RT_SET_TEXTURES = 2   // Texture arrays, samplers
};

// RT_SET_GLOBAL layout (set = 0)
VkDescriptorSetLayoutBinding globalBindings[] = {
    { 0, VK_DESCRIPTOR_TYPE_STORAGE_IMAGE, 1, VK_SHADER_STAGE_RAYGEN_BIT_KHR },          // Output image
    { 1, VK_DESCRIPTOR_TYPE_ACCELERATION_STRUCTURE_KHR, 1, VK_SHADER_STAGE_RAYGEN_BIT_KHR | VK_SHADER_STAGE_CLOSEST_HIT_BIT_KHR }, // TLAS
    { 2, VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER, 1, VK_SHADER_STAGE_RAYGEN_BIT_KHR },         // Camera UBO
    { 3, VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER, 1, VK_SHADER_STAGE_MISS_BIT_KHR },          // Sky UBO
    { 4, VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER, 1, VK_SHADER_STAGE_CLOSEST_HIT_BIT_KHR }    // Light UBO
};

// RT_SET_GEOMETRY layout (set = 1)
VkDescriptorSetLayoutBinding geometryBindings[] = {
    { 0, VK_DESCRIPTOR_TYPE_STORAGE_BUFFER, 1, VK_SHADER_STAGE_CLOSEST_HIT_BIT_KHR },    // Vertex attributes
    { 1, VK_DESCRIPTOR_TYPE_STORAGE_BUFFER, 1, VK_SHADER_STAGE_CLOSEST_HIT_BIT_KHR },    // Face indices
    { 2, VK_DESCRIPTOR_TYPE_STORAGE_BUFFER, 1, VK_SHADER_STAGE_CLOSEST_HIT_BIT_KHR },    // Material IDs
    { 3, VK_DESCRIPTOR_TYPE_STORAGE_BUFFER, 1, VK_SHADER_STAGE_CLOSEST_HIT_BIT_KHR }     // Materials
};
```

## **Implementation Priority Order (Context7 Guided)**

### **Step 1: Pipeline Creation (Week 1)**
1. Implement `VkRaytracingPipeline::createPipeline()`
2. Add proper shader group configuration
3. Handle pipeline layout creation
4. Test with our compiled shaders

### **Step 2: SBT Management (Week 1-2)**
1. Implement `VkShaderBindingTable::createSBT()`
2. Add shader group handle retrieval
3. Implement memory layout calculation
4. Set up device address regions

### **Step 3: Descriptor Integration (Week 2)**
1. Create RT-specific descriptor layouts
2. Implement descriptor set allocation
3. Add resource binding for RT dispatch
4. Test resource access from shaders

### **Step 4: Dispatch Implementation (Week 2-3)**
1. Implement `vkCmdTraceRaysKHR` wrapper
2. Add command buffer recording
3. Integrate with existing rendering pipeline
4. Create simple output image display

### **Step 5: Integration Testing (Week 3)**
1. Create minimal raytracing test
2. Verify shader execution
3. Debug pipeline and SBT issues
4. Validate output image results

## **Context7 Best Practices to Follow**

### **Memory Management:**
- Use `VkDeviceAddress` for SBT regions
- Implement proper buffer alignment
- Handle scratch buffer allocation for AS builds

### **Pipeline Optimization:**
- Use `VK_PIPELINE_CREATE_RAY_TRACING_SKIP_TRIANGLES_BIT_KHR` when appropriate
- Implement pipeline libraries for modular shaders
- Optimize stack size calculation

### **Error Handling:**
- Graceful fallback when RT hardware unavailable
- Proper validation layer integration
- Debug-friendly error messages

### **Performance:**
- Minimize descriptor set updates
- Batch ray tracing dispatches
- Use appropriate ray flags for optimization

## **Testing Strategy**

### **Minimal Test Case:**
1. Create single triangle in BLAS
2. Simple camera setup
3. Output to screen texture
4. Verify ray/triangle intersection

### **Validation Points:**
- Pipeline creation succeeds
- SBT allocation and setup works
- Descriptor sets bind correctly
- Ray dispatch executes without errors
- Output image shows expected results

This implementation plan follows Context7 Vulkan documentation patterns and ensures we build the critical raytracing dispatch functionality correctly. 