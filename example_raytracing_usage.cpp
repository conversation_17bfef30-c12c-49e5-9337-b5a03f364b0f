// Example: How to add cube mirror and sphere scene nodes for raytracing
// This shows the complete integration with your existing application

#include "irrlicht.h"
#include "VkRaytracingSceneExample.h"

using namespace irr;
using namespace core;
using namespace scene;
using namespace video;

// Example function showing how to integrate raytracing scene nodes
// Call this from your main application after creating the device and scene manager
void setupRaytracingSceneExample(IrrlichtDevice* device) {
    if (!device) {
        return;
    }
    
    IVideoDriver* driver = device->getVideoDriver();
    ISceneManager* smgr = device->getSceneManager();
    
    // Check if we're using Vulkan driver
    if (driver->getDriverType() != EDT_VULKAN) {
        printf("Raytracing requires Vulkan driver\n");
        return;
    }
    
    // Cast to VkDriver to access raytracing features
    VkDriver* vkDriver = static_cast<VkDriver*>(driver);
    
    // Check if raytracing is supported
    if (!vkDriver->isRaytracingSupported()) {
        printf("Raytracing is not supported on this device\n");
        return;
    }
    
    printf("Setting up raytracing scene with cube mirror and spheres...\n");
    
    // Use the example function to set up the scene
    exampleRaytracingSceneUsage(vkDriver, smgr);
    
    printf("Raytracing scene setup completed!\n");
}

// Alternative: Manual setup if you want more control
void manualRaytracingSetup(IrrlichtDevice* device) {
    if (!device) {
        return;
    }
    
    IVideoDriver* driver = device->getVideoDriver();
    ISceneManager* smgr = device->getSceneManager();
    VkDriver* vkDriver = static_cast<VkDriver*>(driver);
    
    if (!vkDriver->isRaytracingSupported()) {
        return;
    }
    
    // Step 1: Create camera
    ICameraSceneNode* camera = smgr->addCameraSceneNode(
        0,                              // parent
        vector3df(0, 10, -20),         // position
        vector3df(0, 0, 0)             // look at
    );
    
    // Step 2: Create cube mirror using standard SceneManager method
    IMeshSceneNode* cubeMirror = smgr->addCubeSceneNode(
        8.0f,                          // size
        0,                             // parent
        -1,                            // id
        vector3df(-5, 0, 0),          // position
        vector3df(0, 45, 0),          // rotation (45 degree Y rotation)
        vector3df(1, 1, 1)            // scale
    );
    
    if (cubeMirror) {
        // Set material properties for mirror effect
        cubeMirror->setMaterialFlag(EMF_LIGHTING, false);
        cubeMirror->setMaterialType(EMT_SOLID);
        printf("Created cube mirror scene node\n");
    }
    
    // Step 3: Create reflective sphere using standard SceneManager method
    IMeshSceneNode* sphere = smgr->addSphereSceneNode(
        4.0f,                          // radius
        16,                            // poly count
        0,                             // parent
        -1,                            // id
        vector3df(5, 0, 0),           // position
        vector3df(0, 0, 0),           // rotation
        vector3df(1, 1, 1)            // scale
    );
    
    if (sphere) {
        // Set material properties
        sphere->setMaterialFlag(EMF_LIGHTING, false);
        sphere->setMaterialType(EMT_SOLID);
        sphere->getMaterial(0).DiffuseColor = SColor(255, 200, 50, 50); // Red color
        printf("Created sphere scene node\n");
    }
    
    // Step 4: Set up raytracing (this would integrate with your raytracing system)
    // For now, just test the basic raytracing
    VkRaytracingDemo::TestRtPm params;
    params.width = 800;
    params.height = 600;
    params.skyTexture = nullptr;
    params.cameraPos = camera->getAbsolutePosition();
    params.cameraUp = camera->getUpVector();
    params.cameraDir = (camera->getTarget() - camera->getAbsolutePosition()).normalize();
    params.cameraRight = params.cameraDir.crossProduct(params.cameraUp).normalize();
    params.fovY = camera->getFOV();
    params.nearPlane = camera->getNearValue();
    params.farPlane = camera->getFarValue();
    params.time = 0.0f;
    
    // Test raytracing
    VkRaytracingDemo* rtDemo = vkDriver->getRaytracingDemo();
    if (rtDemo && rtDemo->testRaytracing(params)) {
        printf("Raytracing test successful!\n");
        
        // Get the output texture for display
        VkTexture* outputTexture = rtDemo->getOutputTexture();
        if (outputTexture) {
            printf("Raytracing output texture is ready\n");
            // You can now display this texture in your application
        }
    }
}

// Example of how to integrate this into your main loop
void exampleMainLoop(IrrlichtDevice* device) {
    IVideoDriver* driver = device->getVideoDriver();
    ISceneManager* smgr = device->getSceneManager();
    
    // Set up the raytracing scene once
    setupRaytracingSceneExample(device);
    
    // Main rendering loop
    while (device->run()) {
        driver->beginScene(true, true, SColor(255, 100, 101, 140));
        
        // Draw the scene normally (this will render the scene nodes)
        smgr->drawAll();
        
        // Optional: You can also render the raytracing output as an overlay
        // This would require additional code to display the raytracing texture
        
        driver->endScene();
    }
}

// Complete example main function
int main() {
    // Create device with Vulkan driver
    SIrrlichtCreationParameters params;
    params.DriverType = EDT_VULKAN;
    params.WindowSize = dimension2d<u32>(800, 600);
    params.Bits = 32;
    params.Fullscreen = false;
    params.Stencilbuffer = false;
    params.Vsync = false;
    
    IrrlichtDevice* device = createDeviceEx(params);
    if (!device) {
        printf("Failed to create Irrlicht device with Vulkan driver\n");
        return -1;
    }
    
    printf("Irrlicht device created successfully\n");
    
    // Run the example
    exampleMainLoop(device);
    
    device->drop();
    return 0;
}

/*
USAGE INSTRUCTIONS:

1. Include this file in your project
2. Make sure VkRaytracingSceneExample.h is included
3. Call setupRaytracingSceneExample(device) after creating your Irrlicht device
4. The function will:
   - Create a camera positioned to view the scene
   - Create a cube mirror using smgr->addCubeSceneNode()
   - Create reflective spheres using smgr->addSphereSceneNode()
   - Set up raytracing for these objects
   - Test the raytracing system

The result will be a raytraced scene with:
- A mirror cube that reflects the environment
- Reflective spheres with different colors
- Proper lighting and sky environment

You can then access the raytraced output texture and display it in your application.
*/
