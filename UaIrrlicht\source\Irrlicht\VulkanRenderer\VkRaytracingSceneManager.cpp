#include "VkRaytracingSceneManager.h"

#ifdef _IRR_COMPILE_WITH_VULKAN_
#if VK_ENABLE_RAYTRACING

#include "VkDriver.h"
#include "VkRaytracing.h"
#include "VkAccelerationStructure.h"
#include "ISceneManager.h"
#include "IMeshSceneNode.h"
#include "IMesh.h"
#include "IMeshBuffer.h"
#include "S3DVertex.h"
#include "os.h"

namespace irr {
namespace video {

VkRaytracingSceneManager::VkRaytracingSceneManager(VkDriver* driver, scene::ISceneManager* sceneManager)
    : driver(driver)
    , rt<PERSON>anager(nullptr)
    , sceneManager(sceneManager)
    , needsTLASRebuild(false)
{
}

VkRaytracingSceneManager::~VkRaytracingSceneManager() {
    shutdown();
}

bool VkRaytracingSceneManager::initialize() {
    if (!driver || !sceneManager) {
        return false;
    }
    
    rtManager = driver->getRaytracingManager();
    if (!rtManager) {
        os::Printer::log("VkRaytracingSceneManager: No raytracing manager available", ELL_ERROR);
        return false;
    }
    
    return true;
}

void VkRaytracingSceneManager::shutdown() {
    // Clean up all raytracing objects
    for (u32 i = 0; i < rtObjects.size(); ++i) {
        if (rtObjects[i].blas) {
            delete rtObjects[i].blas;
        }
    }
    rtObjects.clear();
    needsTLASRebuild = false;
}

u32 VkRaytracingSceneManager::addSceneNode(scene::IMeshSceneNode* node, const SRaytracingMaterial2& material) {
    if (!node || !rtManager) {
        return ~0u;
    }
    
    // Extract mesh data from scene node
    core::array<u8> vertexData;
    core::array<u32> indexData;
    u32 vertexCount, indexCount;
    
    if (!extractMeshData(node, vertexData, indexData, vertexCount, indexCount)) {
        os::Printer::log("VkRaytracingSceneManager: Failed to extract mesh data", ELL_ERROR);
        return ~0u;
    }
    
    // Create BLAS for this object
    VkBottomLevelAS* blas = createBLAS(vertexData, indexData, vertexCount, indexCount);
    if (!blas) {
        os::Printer::log("VkRaytracingSceneManager: Failed to create BLAS", ELL_ERROR);
        return ~0u;
    }
    
    // Create raytracing object
    SRaytracingObject rtObject;
    rtObject.sceneNode = node;
    rtObject.blas = blas;
    rtObject.material = material;
    rtObject.needsUpdate = true;
    
    u32 objectIndex = rtObjects.size();
    rtObjects.push_back(rtObject);
    
    needsTLASRebuild = true;
    
    os::Printer::log("VkRaytracingSceneManager: Added scene node to raytracing", ELL_INFORMATION);
    return objectIndex;
}

void VkRaytracingSceneManager::removeSceneNode(u32 objectIndex) {
    if (objectIndex >= rtObjects.size()) {
        return;
    }
    
    // Clean up BLAS
    if (rtObjects[objectIndex].blas) {
        delete rtObjects[objectIndex].blas;
    }
    
    // Remove from array
    rtObjects.erase(objectIndex);
    needsTLASRebuild = true;
}

void VkRaytracingSceneManager::updateObjects() {
    if (!rtManager) {
        return;
    }
    
    bool hasUpdates = false;
    
    // Check for transform updates
    for (u32 i = 0; i < rtObjects.size(); ++i) {
        SRaytracingObject& obj = rtObjects[i];
        if (obj.sceneNode && obj.needsUpdate) {
            hasUpdates = true;
            obj.needsUpdate = false;
        }
    }
    
    // Rebuild TLAS if needed
    if (needsTLASRebuild || hasUpdates) {
        rebuildTLAS();
        needsTLASRebuild = false;
    }
}

scene::IMeshSceneNode* VkRaytracingSceneManager::createCubeMirror(const core::vector3df& position,
                                                                 const core::vector3df& rotation,
                                                                 const core::vector3df& scale,
                                                                 f32 size) {
    if (!sceneManager) {
        return nullptr;
    }
    
    // Create cube scene node
    scene::IMeshSceneNode* cube = sceneManager->addCubeSceneNode(size);
    if (!cube) {
        return nullptr;
    }
    
    // Set transform
    cube->setPosition(position);
    cube->setRotation(rotation);
    cube->setScale(scale);
    
    // Set up mirror material properties
    SRaytracingMaterial2 mirrorMaterial;
    mirrorMaterial.diffuseColor = core::vector3df(0.9f, 0.9f, 0.9f);
    mirrorMaterial.roughness = 0.0f;
    mirrorMaterial.metallic = 1.0f;
    mirrorMaterial.reflectivity = 0.95f;
    mirrorMaterial.materialType = 1; // Mirror type
    
    // Add to raytracing
    u32 objectIndex = addSceneNode(cube, mirrorMaterial);
    if (objectIndex == ~0u) {
        cube->remove();
        return nullptr;
    }
    
    os::Printer::log("VkRaytracingSceneManager: Created cube mirror", ELL_INFORMATION);
    return cube;
}

scene::IMeshSceneNode* VkRaytracingSceneManager::createReflectiveSphere(const core::vector3df& position,
                                                                       const core::vector3df& rotation,
                                                                       const core::vector3df& scale,
                                                                       f32 radius,
                                                                       s32 polyCount,
                                                                       const core::vector3df& color) {
    if (!sceneManager) {
        return nullptr;
    }
    
    // Create sphere scene node
    scene::IMeshSceneNode* sphere = sceneManager->addSphereSceneNode(radius, polyCount);
    if (!sphere) {
        return nullptr;
    }
    
    // Set transform
    sphere->setPosition(position);
    sphere->setRotation(rotation);
    sphere->setScale(scale);
    
    // Set up sphere material properties
    SRaytracingMaterial2 sphereMaterial;
    sphereMaterial.diffuseColor = color;
    sphereMaterial.roughness = 0.2f;
    sphereMaterial.metallic = 0.3f;
    sphereMaterial.reflectivity = 0.4f;
    sphereMaterial.materialType = 0; // Diffuse type with some reflection
    
    // Add to raytracing
    u32 objectIndex = addSceneNode(sphere, sphereMaterial);
    if (objectIndex == ~0u) {
        sphere->remove();
        return nullptr;
    }
    
    os::Printer::log("VkRaytracingSceneManager: Created reflective sphere", ELL_INFORMATION);
    return sphere;
}

const SRaytracingObject* VkRaytracingSceneManager::getObject(u32 index) const {
    if (index >= rtObjects.size()) {
        return nullptr;
    }
    return &rtObjects[index];
}

bool VkRaytracingSceneManager::extractMeshData(scene::IMeshSceneNode* node,
                                              core::array<u8>& vertexData,
                                              core::array<u32>& indexData,
                                              u32& vertexCount, u32& indexCount) {
    if (!node || !node->getMesh()) {
        return false;
    }

    scene::IMesh* mesh = node->getMesh();
    if (mesh->getMeshBufferCount() == 0) {
        return false;
    }

    // For simplicity, use only the first mesh buffer
    scene::IMeshBuffer* buffer = mesh->getMeshBuffer(0);
    if (!buffer) {
        return false;
    }

    vertexCount = buffer->getVertexCount();
    indexCount = buffer->getIndexCount();

    if (vertexCount == 0 || indexCount == 0) {
        return false;
    }

    // Raytracing vertex format: position(3), normal(3), uv(2), color(4)
    const u32 rtVertexSize = sizeof(f32) * 12; // 12 floats per vertex
    vertexData.set_used(vertexCount * rtVertexSize);
    indexData.set_used(indexCount);

    // Get world transform
    core::matrix4 worldTransform = node->getAbsoluteTransformation();

    // Extract and convert vertices
    f32* vertexPtr = reinterpret_cast<f32*>(vertexData.pointer());
    for (u32 i = 0; i < vertexCount; ++i) {
        const video::S3DVertex& vertex = static_cast<const video::S3DVertex*>(buffer->getVertices())[i];

        // Transform position to world space
        core::vector3df worldPos;
        worldTransform.transformVect(worldPos, vertex.Pos);

        // Transform normal to world space
        core::vector3df worldNormal;
        worldTransform.rotateVect(worldNormal, vertex.Normal);
        worldNormal.normalize();

        // Position (3 floats)
        *vertexPtr++ = worldPos.X;
        *vertexPtr++ = worldPos.Y;
        *vertexPtr++ = worldPos.Z;

        // Normal (3 floats)
        *vertexPtr++ = worldNormal.X;
        *vertexPtr++ = worldNormal.Y;
        *vertexPtr++ = worldNormal.Z;

        // UV (2 floats)
        *vertexPtr++ = vertex.TCoords.X;
        *vertexPtr++ = vertex.TCoords.Y;

        // Color (4 floats)
        *vertexPtr++ = vertex.Color.getRed() / 255.0f;
        *vertexPtr++ = vertex.Color.getGreen() / 255.0f;
        *vertexPtr++ = vertex.Color.getBlue() / 255.0f;
        *vertexPtr++ = vertex.Color.getAlpha() / 255.0f;
    }

    // Extract indices
    const u16* indices16 = buffer->getIndices();
    for (u32 i = 0; i < indexCount; ++i) {
        indexData[i] = static_cast<u32>(indices16[i]);
    }

    return true;
}

VkBottomLevelAS* VkRaytracingSceneManager::createBLAS(const core::array<u8>& vertexData,
                                                     const core::array<u32>& indexData,
                                                     u32 vertexCount, u32 indexCount) {
    if (!rtManager) {
        return nullptr;
    }

    VkBottomLevelAS* blas = rtManager->createBottomLevelAS();
    if (!blas) {
        return nullptr;
    }

    const u32 vertexStride = sizeof(f32) * 12; // 12 floats per vertex

    if (!blas->buildFromMesh(vertexData.const_pointer(), vertexCount, vertexStride,
                            indexData.const_pointer(), indexCount, true)) {
        delete blas;
        return nullptr;
    }

    return blas;
}

void VkRaytracingSceneManager::rebuildTLAS() {
    if (!rtManager) {
        return;
    }

    VkTopLevelAS* tlas = rtManager->getTopLevelAS();
    if (!tlas) {
        return;
    }

    // Clear existing instances
    tlas->clear();

    // Add all objects to TLAS
    for (u32 i = 0; i < rtObjects.size(); ++i) {
        SRaytracingObject& obj = rtObjects[i];
        if (obj.blas && obj.sceneNode) {
            // Get current transform (identity since we already applied world transform to vertices)
            core::matrix4 identity;
            identity.makeIdentity();

            // Add instance to TLAS
            obj.tlasInstanceIndex = tlas->addInstance(obj.blas, identity, i, 0, 0xFF);
        }
    }

    // Build TLAS
    tlas->build();
}

} // namespace video
} // namespace irr

#endif // VK_ENABLE_RAYTRACING
#endif // _IRR_COMPILE_WITH_VULKAN_
