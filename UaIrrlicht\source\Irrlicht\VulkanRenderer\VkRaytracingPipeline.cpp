#include "VkRaytracingPipeline.h"

#ifdef _IRR_COMPILE_WITH_VULKAN_
#if VK_ENABLE_RAYTRACING

#include "VkDriver.h"
#include "VkHardwareBuffer.h"

namespace irr {
namespace video {

// =============================================================================
// VkShaderBindingTable Implementation
// =============================================================================

VkShaderBindingTable::VkShaderBindingTable(VkDriver* driver)
    : driver(driver), buffer(nullptr), handleSize(0), handleCount(0), 
      stride(0), isValid(false) {
    
    // Initialize region to zero
    region = {};
}

VkShaderBindingTable::~VkShaderBindingTable() {
    destroy();
}

bool VkShaderBindingTable::initialize(const void* handles, u32 handleCount, u32 handleSize) {
    if (!driver || !driver->isRaytracingSupported() || !handles || handleCount == 0 || handleSize == 0) {
        return false;
    }

    this->handleCount = handleCount;
    this->handleSize = handleSize;
    
    // Calculate stride (must be aligned)
    const VkPhysicalDeviceRayTracingPipelinePropertiesKHR& rtProps = driver->getRaytracingProperties();
    stride = ((handleSize + rtProps.shaderGroupHandleAlignment - 1) / rtProps.shaderGroupHandleAlignment) * rtProps.shaderGroupHandleAlignment;
    
    // Calculate total buffer size
    VkDeviceSize bufferSize = stride * handleCount;
    
    // Create buffer for SBT
    buffer = static_cast<VkHardwareBuffer*>(driver->createHardwareBuffer(
        EHBT_STORAGE,
        EHBA_DEFAULT,  // Host visible for SBT data upload
        bufferSize,
        EHBF_SHADER_BINDING_TABLE | EHBF_DEVICE_ADDRESS,
        nullptr
    ));
    
    if (!buffer) {
        return false;
    }

    // Upload shader handles
    void* mappedData = buffer->lock();
    if (!mappedData) {
        delete buffer;
        buffer = nullptr;
        return false;
    }

    // Copy handles with proper stride
    u8* dst = static_cast<u8*>(mappedData);
    const u8* src = static_cast<const u8*>(handles);
    
    for (u32 i = 0; i < handleCount; ++i) {
        memcpy(dst + i * stride, src + i * handleSize, handleSize);
    }
    
    buffer->unlock();

    // Setup region
    region.deviceAddress = buffer->getDeviceAddress();
    region.stride = stride;
    region.size = bufferSize;

    isValid = true;
    return true;
}

void VkShaderBindingTable::destroy() {
    if (buffer) {
        delete buffer;
        buffer = nullptr;
    }
    
    region = {};
    handleSize = 0;
    handleCount = 0;
    stride = 0;
    isValid = false;
}

// =============================================================================
// VkRaytracingPipeline Implementation
// =============================================================================

VkRaytracingPipeline::VkRaytracingPipeline(VkDriver* driver)
    : driver(driver), pipeline(VK_NULL_HANDLE), pipelineLayout(VK_NULL_HANDLE),
      raygenSBT(nullptr), missSBT(nullptr), hitSBT(nullptr), callableSBT(nullptr),
      shaderHandleSize(0), isValid(false) {
}

VkRaytracingPipeline::~VkRaytracingPipeline() {
    destroy();
}

bool VkRaytracingPipeline::addShader(const SRaytracingShaderInfo& shaderInfo) {
    if (!driver || !driver->isRaytracingSupported() || !shaderInfo.shaderCode || shaderInfo.shaderSize == 0) {
        return false;
    }

    // Create shader module
    VkShaderModule shaderModule = createShaderModule(shaderInfo.shaderCode, shaderInfo.shaderSize);
    if (shaderModule == VK_NULL_HANDLE) {
        return false;
    }

    shaderModules.push_back(shaderModule);

    // Create shader stage info
    VkPipelineShaderStageCreateInfo stageInfo = {};
    stageInfo.sType = VK_STRUCTURE_TYPE_PIPELINE_SHADER_STAGE_CREATE_INFO;
    stageInfo.module = shaderModule;
    stageInfo.pName = shaderInfo.entryPoint;

    // Map shader type to Vulkan stage
    switch (shaderInfo.type) {
        case ERST_RAY_GENERATION:
            stageInfo.stage = VK_SHADER_STAGE_RAYGEN_BIT_KHR;
            break;
        case ERST_MISS:
            stageInfo.stage = VK_SHADER_STAGE_MISS_BIT_KHR;
            break;
        case ERST_CLOSEST_HIT:
            stageInfo.stage = VK_SHADER_STAGE_CLOSEST_HIT_BIT_KHR;
            break;
        case ERST_ANY_HIT:
            stageInfo.stage = VK_SHADER_STAGE_ANY_HIT_BIT_KHR;
            break;
        case ERST_INTERSECTION:
            stageInfo.stage = VK_SHADER_STAGE_INTERSECTION_BIT_KHR;
            break;
        case ERST_CALLABLE:
            stageInfo.stage = VK_SHADER_STAGE_CALLABLE_BIT_KHR;
            break;
        default:
            vkDestroyShaderModule(driver->getDevice(), shaderModule, nullptr);
            shaderModules.erase(shaderModules.size() - 1);
            return false;
    }

    shaderStages.push_back(stageInfo);

    // Create shader group
    VkRayTracingShaderGroupCreateInfoKHR groupInfo = {};
    groupInfo.sType = VK_STRUCTURE_TYPE_RAY_TRACING_SHADER_GROUP_CREATE_INFO_KHR;
    groupInfo.type = getGroupType(shaderInfo.type);
    groupInfo.generalShader = VK_SHADER_UNUSED_KHR;
    groupInfo.closestHitShader = VK_SHADER_UNUSED_KHR;
    groupInfo.anyHitShader = VK_SHADER_UNUSED_KHR;
    groupInfo.intersectionShader = VK_SHADER_UNUSED_KHR;

    u32 stageIndex = shaderStages.size() - 1;

    switch (shaderInfo.type) {
        case ERST_RAY_GENERATION:
        case ERST_MISS:
        case ERST_CALLABLE:
            groupInfo.generalShader = stageIndex;
            break;
        case ERST_CLOSEST_HIT:
            groupInfo.closestHitShader = stageIndex;
            break;
        case ERST_ANY_HIT:
            groupInfo.anyHitShader = stageIndex;
            break;
        case ERST_INTERSECTION:
            groupInfo.intersectionShader = stageIndex;
            break;
    }

    shaderGroups.push_back(groupInfo);
    return true;
}

void VkRaytracingPipeline::setDescriptorSetLayouts(const VkDescriptorSetLayout* layouts, u32 layoutCount) {
    descriptorSetLayouts.clear();
    if (layouts && layoutCount > 0) {
        descriptorSetLayouts.set_used(layoutCount);
        for (u32 i = 0; i < layoutCount; ++i) {
            descriptorSetLayouts[i] = layouts[i];
        }
    }
}

bool VkRaytracingPipeline::build() {
    if (!driver || !driver->isRaytracingSupported() || shaderStages.size() == 0) {
        return false;
    }

    // Get raytracing properties
    const VkPhysicalDeviceRayTracingPipelinePropertiesKHR& rtProps = driver->getRaytracingProperties();
    shaderHandleSize = rtProps.shaderGroupHandleSize;

    // Create pipeline layout with descriptor set layouts
    VkPipelineLayoutCreateInfo layoutInfo = {};
    layoutInfo.sType = VK_STRUCTURE_TYPE_PIPELINE_LAYOUT_CREATE_INFO;
    layoutInfo.setLayoutCount = descriptorSetLayouts.size();
    layoutInfo.pSetLayouts = descriptorSetLayouts.size() > 0 ? descriptorSetLayouts.pointer() : nullptr;
    layoutInfo.pushConstantRangeCount = 0;  // No push constants for now

    VkResult result = vkCreatePipelineLayout(driver->getDevice(), &layoutInfo, nullptr, &pipelineLayout);
    if (result != VK_SUCCESS) {
        return false;
    }

    // Create raytracing pipeline
    VkRayTracingPipelineCreateInfoKHR pipelineInfo = {};
    pipelineInfo.sType = VK_STRUCTURE_TYPE_RAY_TRACING_PIPELINE_CREATE_INFO_KHR;
    pipelineInfo.stageCount = shaderStages.size();
    pipelineInfo.pStages = shaderStages.pointer();
    pipelineInfo.groupCount = shaderGroups.size();
    pipelineInfo.pGroups = shaderGroups.pointer();
    pipelineInfo.maxPipelineRayRecursionDepth = 1;  // Simple recursion for now
    pipelineInfo.layout = pipelineLayout;

    result = driver->vkCreateRayTracingPipelinesKHR(
        driver->getDevice(), VK_NULL_HANDLE, VK_NULL_HANDLE, 1, &pipelineInfo, nullptr, &pipeline);

    if (result != VK_SUCCESS) {
        vkDestroyPipelineLayout(driver->getDevice(), pipelineLayout, nullptr);
        pipelineLayout = VK_NULL_HANDLE;
        return false;
    }

    // Setup shader binding tables
    if (!setupShaderBindingTables()) {
        destroy();
        return false;
    }

    isValid = true;
    return true;
}

void VkRaytracingPipeline::bind(VkCommandBuffer commandBuffer) {
    if (!isValid || !driver->isRaytracingSupported()) {
        return;
    }

    vkCmdBindPipeline(commandBuffer, VK_PIPELINE_BIND_POINT_RAY_TRACING_KHR, pipeline);
}

void VkRaytracingPipeline::traceRays(VkCommandBuffer commandBuffer, u32 width, u32 height, u32 depth) {
    if (!isValid || !driver->isRaytracingSupported()) {
        return;
    }

    // Get SBT regions (empty regions for unused stages)
    VkStridedDeviceAddressRegionKHR raygenRegion = raygenSBT ? raygenSBT->getRegion() : VkStridedDeviceAddressRegionKHR{};
    VkStridedDeviceAddressRegionKHR missRegion = missSBT ? missSBT->getRegion() : VkStridedDeviceAddressRegionKHR{};
    VkStridedDeviceAddressRegionKHR hitRegion = hitSBT ? hitSBT->getRegion() : VkStridedDeviceAddressRegionKHR{};
    VkStridedDeviceAddressRegionKHR callableRegion = callableSBT ? callableSBT->getRegion() : VkStridedDeviceAddressRegionKHR{};

    driver->vkCmdTraceRaysKHR(commandBuffer, &raygenRegion, &missRegion, &hitRegion, &callableRegion, width, height, depth);
}

void VkRaytracingPipeline::destroy() {
    // Destroy SBTs
    delete raygenSBT; raygenSBT = nullptr;
    delete missSBT; missSBT = nullptr;
    delete hitSBT; hitSBT = nullptr;
    delete callableSBT; callableSBT = nullptr;

    // Destroy pipeline
    if (pipeline != VK_NULL_HANDLE && driver) {
        vkDestroyPipeline(driver->getDevice(), pipeline, nullptr);
        pipeline = VK_NULL_HANDLE;
    }

    // Destroy pipeline layout
    if (pipelineLayout != VK_NULL_HANDLE && driver) {
        vkDestroyPipelineLayout(driver->getDevice(), pipelineLayout, nullptr);
        pipelineLayout = VK_NULL_HANDLE;
    }

    // Destroy shader modules
    if (driver) {
        for (u32 i = 0; i < shaderModules.size(); ++i) {
            vkDestroyShaderModule(driver->getDevice(), shaderModules[i], nullptr);
        }
    }
    shaderModules.clear();

    shaderStages.clear();
    shaderGroups.clear();
    shaderHandleSize = 0;
    isValid = false;
}

VkShaderModule VkRaytracingPipeline::createShaderModule(const void* code, u32 size) {
    if (!driver || !code || size == 0) {
        return VK_NULL_HANDLE;
    }

    VkShaderModuleCreateInfo createInfo = {};
    createInfo.sType = VK_STRUCTURE_TYPE_SHADER_MODULE_CREATE_INFO;
    createInfo.codeSize = size;
    createInfo.pCode = static_cast<const u32*>(code);

    VkShaderModule shaderModule;
    VkResult result = vkCreateShaderModule(driver->getDevice(), &createInfo, nullptr, &shaderModule);

    return (result == VK_SUCCESS) ? shaderModule : VK_NULL_HANDLE;
}

bool VkRaytracingPipeline::setupShaderBindingTables() {
    if (!driver || !driver->isRaytracingSupported() || pipeline == VK_NULL_HANDLE) {
        return false;
    }

    // Get shader group handles
    u32 groupCount = shaderGroups.size();
    u32 handleDataSize = groupCount * shaderHandleSize;
    core::array<u8> handleData(handleDataSize);

    VkResult result = driver->vkGetRayTracingShaderGroupHandlesKHR(
        driver->getDevice(), pipeline, 0, groupCount, handleDataSize, handleData.pointer());

    if (result != VK_SUCCESS) {
        return false;
    }

    // Separate handles by shader type based on group indices
    core::array<const u8*> raygenHandles;
    core::array<const u8*> missHandles;
    core::array<const u8*> hitHandles;
    core::array<const u8*> callableHandles;

    // Categorize handles based on shader stages (assumes group index matches stage order)
    for (u32 i = 0; i < shaderStages.size(); ++i) {
        const u8* handle = handleData.pointer() + (i * shaderHandleSize);
        
        switch (shaderStages[i].stage) {
            case VK_SHADER_STAGE_RAYGEN_BIT_KHR:
                raygenHandles.push_back(handle);
                break;
            case VK_SHADER_STAGE_MISS_BIT_KHR:
                missHandles.push_back(handle);
                break;
            case VK_SHADER_STAGE_CLOSEST_HIT_BIT_KHR:
            case VK_SHADER_STAGE_ANY_HIT_BIT_KHR:
            case VK_SHADER_STAGE_INTERSECTION_BIT_KHR:
                hitHandles.push_back(handle);
                break;
            case VK_SHADER_STAGE_CALLABLE_BIT_KHR:
                callableHandles.push_back(handle);
                break;
        }
    }

    // Create raygen SBT
    if (raygenHandles.size() > 0) {
        raygenSBT = new VkShaderBindingTable(driver);
        if (!raygenSBT->initialize(raygenHandles[0], 1, shaderHandleSize)) {
            delete raygenSBT;
            raygenSBT = nullptr;
            return false;
        }
    }

    // Create miss SBT
    if (missHandles.size() > 0) {
        missSBT = new VkShaderBindingTable(driver);
        if (!missSBT->initialize(missHandles[0], missHandles.size(), shaderHandleSize)) {
            delete missSBT;
            missSBT = nullptr;
            // Don't fail - miss shaders are optional
        }
    }

    // Create hit SBT
    if (hitHandles.size() > 0) {
        hitSBT = new VkShaderBindingTable(driver);
        if (!hitSBT->initialize(hitHandles[0], hitHandles.size(), shaderHandleSize)) {
            delete hitSBT;
            hitSBT = nullptr;
            // Don't fail - hit shaders are optional for basic setup
        }
    }

    // Create callable SBT
    if (callableHandles.size() > 0) {
        callableSBT = new VkShaderBindingTable(driver);
        if (!callableSBT->initialize(callableHandles[0], callableHandles.size(), shaderHandleSize)) {
            delete callableSBT;
            callableSBT = nullptr;
            // Don't fail - callable shaders are optional
        }
    }

    return true;
}

VkRayTracingShaderGroupTypeKHR VkRaytracingPipeline::getGroupType(E_RAYTRACING_SHADER_TYPE shaderType) {
    switch (shaderType) {
        case ERST_RAY_GENERATION:
        case ERST_MISS:
        case ERST_CALLABLE:
            return VK_RAY_TRACING_SHADER_GROUP_TYPE_GENERAL_KHR;
        case ERST_CLOSEST_HIT:
        case ERST_ANY_HIT:
        case ERST_INTERSECTION:
            return VK_RAY_TRACING_SHADER_GROUP_TYPE_TRIANGLES_HIT_GROUP_KHR;
        default:
            return VK_RAY_TRACING_SHADER_GROUP_TYPE_GENERAL_KHR;
    }
}

} // namespace video
} // namespace irr

#endif // VK_ENABLE_RAYTRACING
#endif // _IRR_COMPILE_WITH_VULKAN_ 