/*
* Vulkan device class
*
* Encapsulates a physical Vulkan device and it's logical representation
*
* Copyright (C) 2016-2017 by <PERSON><PERSON><PERSON> - www.saschawillems.de
*
* This code is licensed under the MIT license (MIT) (http://opensource.org/licenses/MIT)
*/

#pragma once

#include <exception>
#include <assert.h>
#include <algorithm>
#include <mutex>
#include <thread>
#include "VulkanTools.h"
#include "VulkanBuffer.hpp"
#include "Helpers/UpUtils.h"


namespace vks
{	
	class VulkanDevice
	{

	public:
		/** @brief Physical device representation */
		VkPhysicalDevice physicalDevice;
		/** @brief Logical device representation (application's view of the device) */
		VkDevice logicalDevice{};
		bool ownLD = false;
		/** @brief Properties of the physical device including limits that the application can check against */
		VkPhysicalDeviceProperties properties;
		
		/** @brief Features of the physical device that an application can use to check if a feature is supported */
		VkPhysicalDeviceFeatures features;
		/** @brief Features that have been enabled for use on the physical device */
		VkPhysicalDeviceFeatures enabledFeatures;
		/** @brief Memory types and heaps of the physical device */
		VkPhysicalDeviceMemoryProperties memoryProperties;
		/** @brief Queue family properties of the physical device */
		std::vector<VkQueueFamilyProperties> queueFamilyProperties;
		/** @brief List of extensions supported by the device */
		std::vector<std::string> supportedExtensions;

		std::thread::id rendererTid;
		//virtual std::thread::id getRendererTid() { return rendererTid; }
		/** @brief Default command pool for the graphics queue family index */
		VkCommandPool commandPool = VK_NULL_HANDLE, commandPoolT1 = VK_NULL_HANDLE;

		VkCommandPool _commandPoolTransfer = VK_NULL_HANDLE;//  use graphic
		bool isMultiQueue=false;

#if VKDRIVER_USE_SUBGROUP
		VkPhysicalDeviceSubgroupProperties subgroupProperties;
		VkPhysicalDeviceProperties2 physicalDeviceProperties2;
#endif
		VkCommandPool thCmdPool() //thread's command pool
		{

			if ( std::this_thread::get_id() == rendererTid )
				return commandPool;
			else
			{
				//DP(("commandPool NOT RENDER THREAD								!!!!!!"));
				return commandPoolT1;
			}

		}

		/** @brief Set to true when the debug marker extension is detected */
		bool enableDebugMarkers = false;
		bool enableDynamicExt = false;
		std::mutex mtxPool;
		/** @brief Contains queue family indices */
		struct
		{
			uint32_t graphics;
			uint32_t compute;
			uint32_t transfer;
		} queueFamilyIndices{};

		/**  @brief Typecast to VkDevice */
		operator VkDevice() { return logicalDevice; };

		/**
		* Default constructor
		*
		* @param physicalDevice Physical device that is to be used
		*/
		VulkanDevice(VkPhysicalDevice physicalDevice)
		{
			rendererTid = std::this_thread::get_id();

			assert(physicalDevice);
			this->physicalDevice = physicalDevice;

			// Store Properties features, limits and properties of the physical device for later use
			// Device properties also contain limits and sparse properties
			vkGetPhysicalDeviceProperties(physicalDevice, &properties);
			// Features should be checked by the examples before using them
			vkGetPhysicalDeviceFeatures(physicalDevice, &features);
			// Memory properties are used regularly for creating all kinds of buffers
			vkGetPhysicalDeviceMemoryProperties(physicalDevice, &memoryProperties);
#ifdef _DEBUG
			for (uint32_t i = 0; i < memoryProperties.memoryTypeCount;i++)
			{
				auto mt = memoryProperties.memoryTypes[i];
				DP(("MemType%3d: %X  heap=%d",i,mt.propertyFlags,mt.heapIndex));
			}
			for (uint32_t i = 0; i < memoryProperties.memoryHeapCount; i++)
			{
				auto hp = memoryProperties.memoryHeaps[i];
				DP(("MemHeap%3d: %llu  flag=%d", i, hp.size, hp.flags));
			}
#endif
			// Queue family properties, used for setting up requested queues upon device creation
			uint32_t queueFamilyCount;
			vkGetPhysicalDeviceQueueFamilyProperties(physicalDevice, &queueFamilyCount, nullptr);
			assert(queueFamilyCount > 0);
			queueFamilyProperties.resize(queueFamilyCount);
			vkGetPhysicalDeviceQueueFamilyProperties(physicalDevice, &queueFamilyCount, queueFamilyProperties.data());
#ifdef _DEBUG
			for (uint32_t i = 0; i < queueFamilyCount; i++)
			{
				const auto &qfp = queueFamilyProperties[i];
				DP(("QueueFamily%d flag %X, count %d, tvb %d, itg %d,%d,%d",i, qfp.queueFlags,qfp.queueCount,qfp.timestampValidBits,qfp.minImageTransferGranularity.width, qfp.minImageTransferGranularity.height, qfp.minImageTransferGranularity.depth));
			}
#endif
			DP(("init step 1 "));
			// Get list of supported extensions
			uint32_t extCount = 0;
			vkEnumerateDeviceExtensionProperties(physicalDevice, nullptr, &extCount, nullptr); 
			if (extCount > 0)
			{
				std::vector<VkExtensionProperties> extensions(extCount);
				if (vkEnumerateDeviceExtensionProperties(physicalDevice, nullptr, &extCount, &extensions.front()) == VK_SUCCESS)
				{
					for (auto ext : extensions)
					{
						supportedExtensions.push_back(ext.extensionName);
						DP((ext.extensionName));
					}
				}
			}
			DP(("init step 2 "));

#if VKDRIVER_USE_SUBGROUP		
			subgroupProperties.sType = VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_SUBGROUP_PROPERTIES;
			subgroupProperties.pNext = NULL;
			physicalDeviceProperties2.sType = VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_PROPERTIES_2;
			physicalDeviceProperties2.pNext = &subgroupProperties;
			vkGetPhysicalDeviceProperties2(physicalDevice, &physicalDeviceProperties2);
			assert(subgroupProperties.supportedStages & VK_SHADER_STAGE_COMPUTE_BIT);
#endif
		}

		/** 
		* Default destructor
		*
		* @note Frees the logical device
		*/
		~VulkanDevice()
		{
			if (_commandPoolTransfer!= commandPool)
			{
				vkDestroyCommandPool(logicalDevice, _commandPoolTransfer, nullptr);
			}
			if (commandPool)				vkDestroyCommandPool(logicalDevice, commandPool, nullptr);
			if (commandPoolT1)				vkDestroyCommandPool(logicalDevice, commandPoolT1, nullptr);		

			if (logicalDevice && ownLD)
			{
				vkDestroyDevice(logicalDevice, nullptr);
			}
		}

		/**
		* Get the index of a memory type that has all the requested property bits set
		*
		* @param typeBits Bitmask with bits set for each memory type supported by the resource to request for (from VkMemoryRequirements)
		* @param properties Bitmask of properties for the memory type to request
		* @param (Optional) memTypeFound Pointer to a bool that is set to true if a matching memory type has been found
		* 
		* @return Index of the requested memory type
		*
		* @throw Throws an exception if memTypeFound is null and no memory type could be found that supports the requested properties
		*/
		uint32_t getMemoryType(uint32_t typeBits, VkMemoryPropertyFlags properties_, VkBool32 *memTypeFound = nullptr)
		{
			for (uint32_t i = 0; i < memoryProperties.memoryTypeCount; i++)
			{
				if ((typeBits & 1) == 1)
				{
					if ((memoryProperties.memoryTypes[i].propertyFlags & properties_) == properties_)
					{
						if (memTypeFound)
						{
							*memTypeFound = true;
						}
						return i;
					}
				}
				typeBits >>= 1;
			}

#if defined(__ANDROID__)
			//todo : Exceptions are disabled by default on Android (need to add LOCAL_CPP_FEATURES += exceptions to Android.mk), so for now just return zero
			if (memTypeFound)
			{
				*memTypeFound = false;
			}
			return 0;
#else
			if (memTypeFound)
			{
				*memTypeFound = false;
				return 0;
			}
			else
			{
				throw std::runtime_error("Could not find a matching memory type");
			}
#endif
		}

		/**
		* Get the index of a queue family that supports the requested queue flags
		*
		* @param queueFlags Queue flags to find a queue family index for
		*
		* @return Index of the queue family index that matches the flags
		*
		* @throw Throws an exception if no queue family index could be found that supports the requested flags
		*/
		uint32_t getQueueFamilyIndex(VkQueueFlagBits queueFlags)
		{
#if VKDRIVER_USE_DEDICATED_COMPUTE_QUEUE// ck:todo dedicated
			// Dedicated queue for compute
			// Try to find a queue family index that supports compute but not graphics
			if (queueFlags & VK_QUEUE_COMPUTE_BIT)
			{
				for (uint32_t i = 0; i < static_cast<uint32_t>(queueFamilyProperties.size()); i++)
				{
					if ((queueFamilyProperties[i].queueFlags & queueFlags) && ((queueFamilyProperties[i].queueFlags & VK_QUEUE_GRAPHICS_BIT) == 0))
					{
						return i;
						break;
					}
				}
			}
#endif

#if VKDRIVER_USE_DEDICATED_TRANSFER_QUEUE
			// Dedicated queue for transfer
			// Try to find a queue family index that supports transfer but not graphics and compute
			if (queueFlags & VK_QUEUE_TRANSFER_BIT)
			{
				for (uint32_t i = 0; i < static_cast<uint32_t>(queueFamilyProperties.size()); i++)
				{
					if ((queueFamilyProperties[i].queueFlags & queueFlags) && ((queueFamilyProperties[i].queueFlags & VK_QUEUE_GRAPHICS_BIT) == 0) && ((queueFamilyProperties[i].queueFlags & VK_QUEUE_COMPUTE_BIT) == 0))
					{
						return i;
						break;
					}
				}
			}
#endif
			// For other queue types or if no separate compute queue is present, return the first one to support the requested flags
			for (uint32_t i = 0; i < static_cast<uint32_t>(queueFamilyProperties.size()); i++)
			{
				if (queueFamilyProperties[i].queueFlags & queueFlags)
				{
					return i;
					break;
				}
			}

#if defined(__ANDROID__)
			//todo : Exceptions are disabled by default on Android (need to add LOCAL_CPP_FEATURES += exceptions to Android.mk), so for now just return zero
			return 0;
#else
			throw std::runtime_error("Could not find a matching queue family index");
#endif
		}

		/**
		* Create the logical device based on the assigned physical device, also gets default queue family indices
		*
		* @param enabledFeatures Can be used to enable certain features upon device creation
		* @param useSwapChain Set to false for headless rendering to omit the swapchain device extensions
		* @param requestedQueueTypes Bit flags specifying the queue types to be requested from the device  
		*
		* @return VkResult of the device creation call
		*/
		VkResult createLogicalDevice(
			VkPhysicalDeviceFeatures enabledFeatures_,
			std::vector<const char*> enabledExtensions,
			bool useSwapChain = true,
			bool skipCreateDevice = false,
			bool multiQueue = false,
			VkQueueFlags requestedQueueTypes = VK_QUEUE_GRAPHICS_BIT | VK_QUEUE_COMPUTE_BIT

		)
		{
			isMultiQueue = multiQueue;
			// Desired queues need to be requested upon logical device creation
			// Due to differing queue family configurations of Vulkan implementations this can be a bit tricky, especially if the application
			// requests different queue types

			std::vector<VkDeviceQueueCreateInfo> queueCreateInfos{};

			// Get queue family indices for the requested queue family types
			// Note that the indices may overlap depending on the implementation

			const float defaultQueuePriority[2] = { 1.0f,0.5f };
			float graphicQueP[4] = { 0.99f,0.5f,0.98f,0.5f };
			// Graphics queue
			if (requestedQueueTypes & VK_QUEUE_GRAPHICS_BIT)
			{
				queueFamilyIndices.graphics = getQueueFamilyIndex(VK_QUEUE_GRAPHICS_BIT);
				VkDeviceQueueCreateInfo queueInfo{};
				queueInfo.sType = VK_STRUCTURE_TYPE_DEVICE_QUEUE_CREATE_INFO;
				queueInfo.queueFamilyIndex = queueFamilyIndices.graphics;
				if (isMultiQueue) {
					//throw "to chechk";//my intel card
					if (queueFamilyProperties[0].queueCount > 1)
						queueInfo.queueCount = 4;
						queueInfo.pQueuePriorities = graphicQueP;
				}
				else {
					queueInfo.queueCount = 2;  //one for offline Driver
					queueInfo.pQueuePriorities = defaultQueuePriority;
				}

				queueCreateInfos.push_back(queueInfo);
			}
			else
			{
				queueFamilyIndices.graphics = 0;
			}

			// Dedicated compute queue
			if (requestedQueueTypes & VK_QUEUE_COMPUTE_BIT)
			{
				queueFamilyIndices.compute = getQueueFamilyIndex(VK_QUEUE_COMPUTE_BIT);
				if (queueFamilyIndices.compute != queueFamilyIndices.graphics)
				{
					// If compute family index differs, we need an additional queue create info for the compute queue
					VkDeviceQueueCreateInfo queueInfo{};
					queueInfo.sType = VK_STRUCTURE_TYPE_DEVICE_QUEUE_CREATE_INFO;
					queueInfo.queueFamilyIndex = queueFamilyIndices.compute;
					queueInfo.queueCount = 1;
					queueInfo.pQueuePriorities = defaultQueuePriority;
					queueCreateInfos.push_back(queueInfo);
				}
			}
			else
			{
				// Else we use the same queue
				queueFamilyIndices.compute = queueFamilyIndices.graphics;
			}


			if (queueFamilyIndices.compute != queueFamilyIndices.graphics)
				throw "queue graphics has no compute";//ck: currently should same

			// Dedicated transfer queue
			if (requestedQueueTypes & VK_QUEUE_TRANSFER_BIT)
			{
				queueFamilyIndices.transfer = getQueueFamilyIndex(VK_QUEUE_TRANSFER_BIT);
				if ((queueFamilyIndices.transfer != queueFamilyIndices.graphics) && (queueFamilyIndices.transfer != queueFamilyIndices.compute))
				{
					// If compute family index differs, we need an additional queue create info for the compute queue
					VkDeviceQueueCreateInfo queueInfo{};
					queueInfo.sType = VK_STRUCTURE_TYPE_DEVICE_QUEUE_CREATE_INFO;
					queueInfo.queueFamilyIndex = queueFamilyIndices.transfer;
					queueInfo.queueCount = 1;
					queueInfo.pQueuePriorities = defaultQueuePriority;
					queueCreateInfos.push_back(queueInfo);
				}
			}
			else
			{
				// Else we use the same queue
				queueFamilyIndices.transfer = queueFamilyIndices.graphics;
			}

			VkResult result = VK_SUCCESS;
			if (!skipCreateDevice)
			{
					//commandPoolTransfer = createCommandPool(queueFamilyIndices.transfer);


				// Create the logical device representation
				std::vector<const char*> deviceExtensions(enabledExtensions);
				if (useSwapChain)
				{
					// If the device will be used for presenting to a display via a swapchain we need to request the swapchain extension
					deviceExtensions.push_back(VK_KHR_SWAPCHAIN_EXTENSION_NAME);
				}				VkDeviceCreateInfo deviceCreateInfo = {};
				deviceCreateInfo.sType = VK_STRUCTURE_TYPE_DEVICE_CREATE_INFO;
				deviceCreateInfo.queueCreateInfoCount = static_cast<uint32_t>(queueCreateInfos.size());;
				deviceCreateInfo.pQueueCreateInfos = queueCreateInfos.data();
				
#if VK_ENABLE_RAYTRACING
				// Check if raytracing extensions are requested
				bool needRaytracing = false;
				for (const auto& ext : deviceExtensions) {
					if (strcmp(ext, VK_KHR_RAY_TRACING_PIPELINE_EXTENSION_NAME) == 0) {
						needRaytracing = true;
						break;
					}
				}
				
				VkPhysicalDeviceRayTracingPipelineFeaturesKHR rtPipelineFeatures{};
				VkPhysicalDeviceAccelerationStructureFeaturesKHR accelStructFeatures{};
				VkPhysicalDeviceBufferDeviceAddressFeaturesKHR bufferDeviceAddressFeatures{};
				VkPhysicalDeviceFeatures2 features2{};
				
				if (needRaytracing) {
					// Follow rtxON2 sample EXACTLY - only use 3 feature structures, NO ray query
					bufferDeviceAddressFeatures.sType = VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_BUFFER_DEVICE_ADDRESS_FEATURES_KHR;
					bufferDeviceAddressFeatures.bufferDeviceAddress = VK_TRUE;
					
					rtPipelineFeatures.sType = VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_RAY_TRACING_PIPELINE_FEATURES_KHR;
					rtPipelineFeatures.pNext = &bufferDeviceAddressFeatures;
					rtPipelineFeatures.rayTracingPipeline = VK_TRUE;
					
					accelStructFeatures.sType = VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_ACCELERATION_STRUCTURE_FEATURES_KHR;
					accelStructFeatures.pNext = &rtPipelineFeatures;
					accelStructFeatures.accelerationStructure = VK_TRUE;
					
					features2.sType = VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_FEATURES_2;
					features2.pNext = &accelStructFeatures;
					features2.features = enabledFeatures_;
					
					// Query all available features FIRST (like rtxON2)
					vkGetPhysicalDeviceFeatures2(physicalDevice, &features2);
					
					deviceCreateInfo.pNext = &features2;
					deviceCreateInfo.pEnabledFeatures = nullptr;  // Use features2 instead
					printf("VulkanDevice: Enabling raytracing features following rtxON2 pattern\n");
				} else {
					deviceCreateInfo.pEnabledFeatures = &enabledFeatures_;
				}
#else
				deviceCreateInfo.pEnabledFeatures = &enabledFeatures_;
#endif				// Check if fragment shader interlock extension is requested
				bool needFragmentShaderInterlock = false;
				for (const auto& ext : deviceExtensions) {
					if (strcmp(ext, "VK_EXT_fragment_shader_interlock") == 0) {
						needFragmentShaderInterlock = true;
						break;
					}
				}
				
				VkPhysicalDeviceFragmentShaderInterlockFeaturesEXT fragmentShaderInterlockFeatures{
					VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_FRAGMENT_SHADER_INTERLOCK_FEATURES_EXT,  // sType
					nullptr,                                                                   // pNext
					VK_TRUE,                                                                   // fragmentShaderSampleInterlock
					VK_TRUE,                                                                   // fragmentShaderPixelInterlock
					VK_FALSE  // fragmentShaderShadingRateInterlock (we don't need this)
				};
				
				if (needFragmentShaderInterlock) {
#if VK_ENABLE_RAYTRACING
					if (needRaytracing) {
						// Chain fragment shader interlock at the beginning of the chain
						fragmentShaderInterlockFeatures.pNext = &accelStructFeatures;
						features2.pNext = &fragmentShaderInterlockFeatures;
						// Re-query features with the updated chain
						vkGetPhysicalDeviceFeatures2(physicalDevice, &features2);
					} else {
						deviceCreateInfo.pNext = &fragmentShaderInterlockFeatures;
					}
#else
					deviceCreateInfo.pNext = &fragmentShaderInterlockFeatures;
#endif
					printf("VulkanDevice: Enabling fragment shader interlock features\n");
				}

				// Enable the debug marker extension if it is present (likely meaning a debugging tool is present)
				if (extensionSupported(VK_EXT_DEBUG_MARKER_EXTENSION_NAME)
					&& extensionSupported(VK_EXT_DEBUG_REPORT_EXTENSION_NAME)
					)
				{
					deviceExtensions.push_back(VK_EXT_DEBUG_REPORT_EXTENSION_NAME);
					deviceExtensions.push_back(VK_EXT_DEBUG_MARKER_EXTENSION_NAME);

					enableDebugMarkers = true;
				}

				if (extensionSupported(VK_EXT_EXTENDED_DYNAMIC_STATE_EXTENSION_NAME))
				{
					deviceExtensions.push_back(VK_EXT_EXTENDED_DYNAMIC_STATE_EXTENSION_NAME);
					enableDynamicExt = true;
				}

#ifndef _DEBUG 
				//renderdoc not support 
				if (extensionSupported(VK_EXT_SWAPCHAIN_COLOR_SPACE_EXTENSION_NAME)) 
				{
					deviceExtensions.push_back("VK_NV_linear_color_attachment");
				 
				}
#endif
				if (deviceExtensions.size() > 0)
				{
					deviceCreateInfo.enabledExtensionCount = (uint32_t)deviceExtensions.size();
					deviceCreateInfo.ppEnabledExtensionNames = deviceExtensions.data();
				}

				CPU_COUNT_B(vkCreateDevice);
				if (!logicalDevice)
					result = vkCreateDevice(physicalDevice, &deviceCreateInfo, nullptr, &logicalDevice);
				CPU_COUNT_E(vkCreateDevice);
				if (result!=VK_SUCCESS)
				vks::tools::exitFatal("Could not create Vulkan device",result);
			}

			DP(("init step 3 "));
			if (result == VK_SUCCESS)
			{

				// Create a default command pool for graphics command buffers
				commandPool = createCommandPool(queueFamilyIndices.graphics);
				//if (multiQueue)
				commandPoolT1 = createCommandPool(queueFamilyIndices.graphics);
				//else				commandPoolT1 = VK_NULL_HANDLE;
 
				if (requestedQueueTypes & VK_QUEUE_TRANSFER_BIT)
				{
					assert(0);//need more work , resources sharingMode -> VK_SHARING_MODE_CONCURRENT ...
					_commandPoolTransfer = createCommandPool(queueFamilyIndices.transfer);
				}
				else
					_commandPoolTransfer = commandPool;

			}

			this->enabledFeatures = enabledFeatures_;

			return result;
		}

		/**
		* Create a buffer on the device
		*
		* @param usageFlags Usage flag bitmask for the buffer (i.e. index, vertex, uniform buffer)
		* @param memoryPropertyFlags Memory properties for this buffer (i.e. device local, host visible, coherent)
		* @param size Size of the buffer in byes
		* @param buffer Pointer to the buffer handle acquired by the function
		* @param memory Pointer to the memory handle acquired by the function
		* @param data Pointer to the data that should be copied to the buffer after creation (optional, if not set, no data is copied over)
		*
		* @return VK_SUCCESS if buffer handle and memory have been created and (optionally passed) data has been copied
		*/
		VkResult createBuffer(VkBufferUsageFlags usageFlags, VkMemoryPropertyFlags memoryPropertyFlags, VkDeviceSize size, VkBuffer *buffer, VkDeviceMemory *memory, void *data = nullptr)
		{
			// Create the buffer handle
			VkBufferCreateInfo bufferCreateInfo = vks::initializers::bufferCreateInfo(usageFlags, size);
			bufferCreateInfo.sharingMode = VK_SHARING_MODE_EXCLUSIVE;
			VK_CHECK_RESULT(vkCreateBuffer(logicalDevice, &bufferCreateInfo, nullptr, buffer));
			DP(("createBuffer %p", buffer));
#if DBG_VK_NAMES
			vks::debugmarker::setBufferName(logicalDevice, *buffer, "vk:Buffer vktexture.Lock");
#endif
			// Create the memory backing up the buffer handle
			VkMemoryRequirements memReqs;
			VkMemoryAllocateInfo memAlloc = vks::initializers::memoryAllocateInfo();
			vkGetBufferMemoryRequirements(logicalDevice, *buffer, &memReqs);
			memAlloc.allocationSize = memReqs.size;
			// Find a memory type index that fits the properties of the buffer
			memAlloc.memoryTypeIndex = getMemoryType(memReqs.memoryTypeBits, memoryPropertyFlags);
			VK_CHECK_RESULT(vkAllocateMemory(logicalDevice, &memAlloc, nullptr, memory));
			
			// If a pointer to the buffer data has been passed, map the buffer and copy over the data
			if (data != nullptr)
			{
				void *mapped;
				VK_CHECK_RESULT(vkMapMemory(logicalDevice, *memory, 0, size, 0, &mapped));
				memcpy(mapped, data, size);
				// If host coherency hasn't been requested, do a manual flush to make writes visible
				if ((memoryPropertyFlags & VK_MEMORY_PROPERTY_HOST_COHERENT_BIT) == 0)
				{
					VkMappedMemoryRange mappedRange = vks::initializers::mappedMemoryRange();
					mappedRange.memory = *memory;
					mappedRange.offset = 0;
					mappedRange.size = size;
					vkFlushMappedMemoryRanges(logicalDevice, 1, &mappedRange);
				}
				vkUnmapMemory(logicalDevice, *memory);
			}

			// Attach the memory to the buffer object
			VK_CHECK_RESULT(vkBindBufferMemory(logicalDevice, *buffer, *memory, 0));

			return VK_SUCCESS;
		}

		/**
		* Create a buffer on the device
		*
		* @param usageFlags Usage flag bitmask for the buffer (i.e. index, vertex, uniform buffer)
		* @param memoryPropertyFlags Memory properties for this buffer (i.e. device local, host visible, coherent)
		* @param buffer Pointer to a vk::Vulkan buffer object
		* @param size Size of the buffer in byes
		* @param data Pointer to the data that should be copied to the buffer after creation (optional, if not set, no data is copied over)
		*
		* @return VK_SUCCESS if buffer handle and memory have been created and (optionally passed) data has been copied
		*/
		VkResult createBuffer(VkBufferUsageFlags usageFlags, VkMemoryPropertyFlags memoryPropertyFlags, vks::Buffer *buffer, VkDeviceSize size, const void *data = nullptr)
		{
			return buffer->createBuffer(this, usageFlags, memoryPropertyFlags, size, data);
		
		}

		/**
		* Copy buffer data from src to dst using VkCmdCopyBuffer
		* 
		* @param src Pointer to the source buffer to copy from
		* @param dst Pointer to the destination buffer to copy tp
		* @param queue Pointer
		* @param copyRegion (Optional) Pointer to a copy region, if NULL, the whole buffer is copied
		*
		* @note Source and destionation pointers must have the approriate transfer usage flags set (TRANSFER_SRC / TRANSFER_DST)
		*/
		void copyBuffer(vks::Buffer *src, vks::Buffer *dst, VkQueue queue, VkBufferCopy *copyRegion = nullptr)
		{
			//assert(dst->size <= src->size);
			assert(src->hBuffer);
			VkCommandPool pool;
			VkCommandBuffer copyCmd = createCommandBuffer(VK_COMMAND_BUFFER_LEVEL_PRIMARY, pool, true);
			VkBufferCopy bufferCopy{};
			if (copyRegion == nullptr)
			{
				bufferCopy.size = std::min(dst->mSize,src->mSize);
			}
			else
			{
				bufferCopy = *copyRegion;
			}

			vkCmdCopyBuffer(copyCmd, src->hBuffer, dst->hBuffer, 1, &bufferCopy);

			flushCommandBuffer(copyCmd, pool, queue);
		}

		/** 
		* Create a command pool for allocation command buffers from
		* 
		* @param queueFamilyIndex Family index of the queue to create the command pool for
		* @param createFlags (Optional) Command pool creation flags (Defaults to VK_COMMAND_POOL_CREATE_RESET_COMMAND_BUFFER_BIT)
		*
		* @note Command buffers allocated from the created pool can only be submitted to a queue with the same family index
		*
		* @return A handle to the created command buffer
		*/
		VkCommandPool createCommandPool(uint32_t queueFamilyIndex, VkCommandPoolCreateFlags createFlags = VK_COMMAND_POOL_CREATE_RESET_COMMAND_BUFFER_BIT)
		{
			VkCommandPoolCreateInfo cmdPoolInfo = {};
			cmdPoolInfo.sType = VK_STRUCTURE_TYPE_COMMAND_POOL_CREATE_INFO;
			cmdPoolInfo.queueFamilyIndex = queueFamilyIndex;
			cmdPoolInfo.flags = createFlags;
			VkCommandPool cmdPool;
			VK_CHECK_RESULT(vkCreateCommandPool(logicalDevice, &cmdPoolInfo, nullptr, &cmdPool));
			return cmdPool;
		}

		/**
		* Allocate a command buffer from the command pool
		*
		* @param level Level of the new command buffer (primary or secondary)
		* @param (Optional) begin If true, recording on the new command buffer will be started (vkBeginCommandBuffer) (Defaults to false)
		*
		* @return A handle to the allocated command buffer
		*/
		VkCommandBuffer createCommandBuffer(VkCommandBufferLevel level,VkCommandPool& fromCmbPool, bool begin = false)
		{
			fromCmbPool = thCmdPool();
			if (fromCmbPool!= commandPool)			mtxPool.lock();
			
			VkCommandBufferAllocateInfo cmdBufAllocateInfo = vks::initializers::commandBufferAllocateInfo(
				fromCmbPool, level, 1);


			VkCommandBuffer cmdBuffer;
			VK_CHECK_RESULT(vkAllocateCommandBuffers(logicalDevice, &cmdBufAllocateInfo, &cmdBuffer));
			if (fromCmbPool != commandPool)			mtxPool.unlock();
			// If requested, also start recording for the new command buffer
			if (begin)
			{
				VkCommandBufferBeginInfo cmdBufInfo = vks::initializers::commandBufferBeginInfo();
				VK_CHECK_RESULT(vkBeginCommandBuffer(cmdBuffer, &cmdBufInfo));
			}

			return cmdBuffer;
		}
		void freeCommandBuffer(VkCommandPool pool, VkCommandBuffer commandBuffer)
		{
			vkFreeCommandBuffers(logicalDevice, pool, 1, &commandBuffer);
		}
		VkFence createFence(bool signaled)
		{
			VkFenceCreateInfo fenceInfo = vks::initializers::fenceCreateInfo(signaled? VK_FENCE_CREATE_SIGNALED_BIT:VK_FLAGS_NONE);
			VkFence fence;
			VK_CHECK_RESULT(vkCreateFence(logicalDevice, &fenceInfo, nullptr, &fence));
			return fence;
		}
		void freeFence(VkFence fence)
		{
			vkDestroyFence(logicalDevice, fence, nullptr);
		}
		/**
		* Finish command buffer recording and submit it to a queue
		*
		* @param commandBuffer Command buffer to flush
		* @param queue Queue to submit the command buffer to 
		* @param free (Optional) Free the command buffer once it has been submitted (Defaults to true)
		*
		* @note The queue that the command buffer is submitted to must be from the same family index as the pool it was allocated from
		* @note Uses a fence to ensure command buffer has finished executing
		*/
		void flushCommandBuffer(VkCommandBuffer commandBuffer,VkCommandPool pool, VkQueue queue, bool free = true)
		{
			if (commandBuffer == VK_NULL_HANDLE)
			{
				return;
			}
			
			mtxPool.lock();
			//DP(("FLUSH+"));
			VK_CHECK_RESULT(vkEndCommandBuffer(commandBuffer));

			VkSubmitInfo submitInfo = vks::initializers::submitInfo();
			submitInfo.commandBufferCount = 1;
			submitInfo.pCommandBuffers = &commandBuffer;

			// Create fence to ensure that the command buffer has finished executing
			VkFenceCreateInfo fenceInfo = vks::initializers::fenceCreateInfo(VK_FLAGS_NONE);
			VkFence fence;
			VK_CHECK_RESULT(vkCreateFence(logicalDevice, &fenceInfo, nullptr, &fence));
			
			// Submit to the queue
			VK_CHECK_RESULT(vkQueueSubmit(queue, 1, &submitInfo, fence));
			// Wait for the fence to signal that command buffer has finished executing

			//CPU_COUNT_B(cbfence);
			VK_CHECK_RESULT(vkWaitForFences(logicalDevice, 1, &fence, VK_TRUE, DEFAULT_FENCE_TIMEOUT));
			//CPU_COUNT_E(cbfence);
			vkDestroyFence(logicalDevice, fence, nullptr);
			//DP(("FLUSH-"));
			mtxPool.unlock();
			if (free)
			{
				vkFreeCommandBuffers(logicalDevice, pool, 1, &commandBuffer);
			}

		}

		/**
		* Check if an extension is supported by the (physical device)
		*
		* @param extension Name of the extension to check
		*
		* @return True if the extension is supported (present in the list read at device creation time)
		*/
		bool extensionSupported(std::string extension)
		{
			return (std::find(supportedExtensions.begin(), supportedExtensions.end(), extension) != supportedExtensions.end());
		}

	};
}
