#include "UaIrrlicht/include/irrlicht.h"
#include "UaIrrlicht/include/ERaytracingTypes.h"
#include <iostream>

using namespace irr;
using namespace core;
using namespace scene;
using namespace video;
using namespace io;

int main() {
    // Create device parameters
    SIrrlichtCreationParameters params;
    params.DriverType = EDT_VULKAN;
    params.WindowSize = dimension2d<u32>(800, 600);
    params.Bits = 32;
    params.Fullscreen = false;
    params.Vsync = false;
    
    // Create device
    IrrlichtDevice* device = createDeviceEx(params);
    if (!device) {
        std::cout << "Failed to create Irrlicht device!" << std::endl;
        return -1;
    }
    
    IVideoDriver* driver = device->getVideoDriver();
    ISceneManager* smgr = device->getSceneManager();
    
    std::cout << "Irrlicht device created successfully!" << std::endl;
    std::cout << "Driver: " << driver->getName() << std::endl;
    
    // Check if we have a VkDriver (Vulkan driver)
    if (driver->getDriverType() == EDT_VULKAN) {
        std::cout << "Vulkan driver detected!" << std::endl;
        
        // Cast to VkDriver to access raytracing features
        // Note: This requires proper headers and linking
        #if VK_ENABLE_RAYTRACING
        // Check if raytracing is supported
        auto* vkDriver = static_cast<video::VkDriver*>(driver);
        if (vkDriver->isRaytracingSupported()) {
            std::cout << "Raytracing is SUPPORTED!" << std::endl;
            
            // Get the raytracing demo
            auto* rtDemo = vkDriver->getRaytracingDemo();
            if (rtDemo) {
                std::cout << "Raytracing demo is available!" << std::endl;
                
                // Test raytracing
                if (rtDemo->testRaytracing()) {
                    std::cout << "Raytracing test PASSED!" << std::endl;
                } else {
                    std::cout << "Raytracing test FAILED!" << std::endl;
                }
                
                // Try to enable raytracing mode
                vkDriver->setRayTracingMode(ERM_RT_FULL_PATHTRACING);
                std::cout << "Raytracing mode set to: " << (int)vkDriver->getRayTracingMode() << std::endl;
                
                // Try to render a frame with raytracing
                if (rtDemo->renderFrame(512, 512)) {
                    std::cout << "Raytracing frame rendered successfully!" << std::endl;
                } else {
                    std::cout << "Failed to render raytracing frame!" << std::endl;
                }
            } else {
                std::cout << "Raytracing demo is not available!" << std::endl;
            }
        } else {
            std::cout << "Raytracing is NOT supported!" << std::endl;
        }
        #else
        std::cout << "Raytracing is disabled at compile time!" << std::endl;
        #endif
    } else {
        std::cout << "Not using Vulkan driver!" << std::endl;
    }
    
    // Add a simple scene
    smgr->addCameraSceneNode(0, vector3df(0, 30, -40), vector3df(0, 5, 0));
    
    // Add a cube
    ISceneNode* cube = smgr->addCubeSceneNode(10.0f);
    if (cube) {
        cube->setPosition(vector3df(0, 0, 30));
        cube->setMaterialTexture(0, driver->getTexture("UaIrrlicht/media/wall.bmp"));
        cube->setMaterialFlag(EMF_LIGHTING, false);
    }
    
    // Render a few frames to test
    int frameCount = 0;
    while (device->run() && frameCount < 10) {
        if (device->isWindowActive()) {
            driver->beginScene(true, true, SColor(255, 100, 101, 140));
            smgr->drawAll();
            driver->endScene();
            frameCount++;
        }
    }
    
    std::cout << "Rendered " << frameCount << " frames successfully!" << std::endl;
    
    device->drop();
    return 0;
} 