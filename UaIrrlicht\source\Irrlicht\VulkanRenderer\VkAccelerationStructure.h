#pragma once

#include "IrrCompileConfig.h"
#ifdef _IRR_COMPILE_WITH_VULKAN_

#if VK_ENABLE_RAYTRACING
#include "irrlicht.h"
#include "VkHeader.h"

#include "ERaytracingTypes.h"

namespace irr {
namespace video {

// Forward declarations
class VkDriver;
class VkHardwareBuffer;

//! Basic acceleration structure wrapper
//! This provides a safe interface for acceleration structure management
//! when raytracing is enabled, and gracefully does nothing when disabled
class VkAccelerationStructureWrapper {
protected:
    VkDriver* driver;
    VkAccelerationStructureKHR handle;
    VkHardwareBuffer* buffer;
    VkDeviceAddress deviceAddress;
    bool isValid;
    
public:
    VkAccelerationStructureWrapper(VkDriver* driver);
    virtual ~VkAccelerationStructureWrapper();
    
    //! Check if acceleration structure is valid
    bool isValidAS() const { return isValid && handle != VK_NULL_HANDLE; }
    
    //! Get the Vulkan handle (for advanced use)
    VkAccelerationStructureKHR getHandle() const { return handle; }
    
    //! Get device address (for shader binding)
    VkDeviceAddress getDeviceAddress() const { return deviceAddress; }
    
    //! Destroy the acceleration structure
    virtual void destroy();
    
protected:
    //! Initialize the wrapper (called by derived classes)
    bool initialize(VkAccelerationStructureTypeKHR type, VkDeviceSize size);
};

//! Simple bottom-level acceleration structure for individual meshes
//! This is a minimal implementation that can be extended later
class VkBottomLevelAS : public VkAccelerationStructureWrapper {
private:
    bool m_needsRebuild;
    
public:
    VkBottomLevelAS(VkDriver* driver);
    virtual ~VkBottomLevelAS();
    
    //! Initialize the BLAS
    bool initialize();
    
    //! Build from a simple triangle mesh (minimal implementation)
    bool buildFromMesh(const void* vertices, u32 vertexCount, u32 vertexStride,
                      const void* indices, u32 indexCount, bool isOpaque = true);
    
    //! Build the BLAS
    bool build();
    
    //! Mark for rebuild (when geometry changes)
    void markForRebuild() { m_needsRebuild = true; }
    
    //! Check if needs rebuild
    bool needsRebuild() const;
    bool needsUpdate() const { return m_needsRebuild; }
};

//! Simple top-level acceleration structure for scene instances
//! This manages a collection of bottom-level acceleration structures
class VkTopLevelAS : public VkAccelerationStructureWrapper {
private:
    struct Instance {
        VkBottomLevelAS* blas;
        core::matrix4 transform;
        u32 instanceID;
        u32 hitGroupIndex;
        u8 mask;
    };
    
    core::array<Instance> instances;
    bool m_needsRebuild;
    
public:
    VkTopLevelAS(VkDriver* driver);
    virtual ~VkTopLevelAS();
    
    //! Initialize the TLAS
    bool initialize();
    
    //! Add an instance of a bottom-level AS
    u32 addInstance(VkBottomLevelAS* blas, const core::matrix4& transform, 
                   u32 instanceID = 0, u32 hitGroupIndex = 0, u8 mask = 0xFF);
    
    //! Update instance transform
    bool updateInstanceTransform(u32 instanceIndex, const core::matrix4& transform);
    
    //! Remove an instance
    void removeInstance(u32 instanceIndex);
    
    //! Build the top-level structure
    bool build();
    
    //! Clear all instances
    void clear();
    
    //! Get instance count
    u32 getInstanceCount() const { return instances.size(); }
    
    //! Check if needs rebuild
    bool needsRebuild() const;
    bool needsUpdate() const { return m_needsRebuild; }
};

} // namespace video
} // namespace irr

#endif // VK_ENABLE_RAYTRACING
#endif // _IRR_COMPILE_WITH_VULKAN_ 