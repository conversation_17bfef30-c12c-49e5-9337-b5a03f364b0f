#include "appGlobal.h"
#include "MrVkFluid.h"
#include "../../../UaIrrlicht/source/Irrlicht/VulkanRenderer/VkDriver.h"
#include "../../../UaIrrlicht/include/IFileSystem.h"
#include "VulkanRenderer/VkVertexDeclaration.h"
#include "VulkanRenderer/vulkanRenderPass.h"
#include "VulkanRenderer/VkShaderMan/VkFxBase.h"
#include "VulkanRenderer/VkShaderMan/VkFxDescriptorSetManager.h"
#include "VulkanRenderer/VkTexture.h"

// Compiled shader headers - using actual VkFluid shaders
#include "VulkanRenderer/Shader/Compiled/SnTemplate/VkFluid/VkFluidShaderToy_vert_SpirV.h"
#include "VulkanRenderer/Shader/Compiled/SnTemplate/VkFluid/VkFluidShaderToy_frag_SpirV.h"
#include "VulkanRenderer/Shader/Compiled/SnTemplate/VkFluid/VkFluidDensity_comp_SpirV.h"
#include "VulkanRenderer/Shader/Compiled/SnTemplate/VkFluid/VkFluidCovariance_comp_SpirV.h"
#include "VulkanRenderer/Shader/Compiled/SnTemplate/VkFluid/VkFluid_comp_SpirV.h"
#include "VulkanRenderer/Shader/Compiled/SnTemplate/VkFluid/VkFluidParticleToGrid_comp_SpirV.h"

#define GRID_TEXTURE_BINDING 1
#define DENSITY_TEXTURE_BINDING 3
#define COVARIANCE_TEXTURE_BINDING 5
#define GRID_TEXTURE_SECONDARY_BINDING 6

#define DPINFO(_x_) //DP(_x_)
using namespace irr;
using namespace irr::video;
using namespace VkFxUtil;

// Fluid shader bytecode declarations - using actual VkFluid shaders
static const ShaderBytecode Sbc_VkFluid__VkFluidShaderToy_vert = CE_DECL_SHADERCODE(SpirV_snTemplate__VkFluid__VkFluidShaderToy_vert);
static const ShaderBytecode Sbc_VkFluid__VkFluidShaderToy_frag = CE_DECL_SHADERCODE(SpirV_snTemplate__VkFluid__VkFluidShaderToy_frag);
static const ShaderBytecode Sbc_VkFluid__VkFluidDensity_comp = CE_DECL_SHADERCODE(SpirV_snTemplate__VkFluid__VkFluidDensity_comp);
static const ShaderBytecode Sbc_VkFluid__VkFluidCovariance_comp = CE_DECL_SHADERCODE(SpirV_snTemplate__VkFluid__VkFluidCovariance_comp);
static const ShaderBytecode Sbc_VkFluid__VkFluid_comp = CE_DECL_SHADERCODE(SpirV_snTemplate__VkFluid__VkFluid_comp);
static const ShaderBytecode Sbc_VkFluid__VkFluidParticleToGrid_comp = CE_DECL_SHADERCODE(SpirV_snTemplate__VkFluid__VkFluidParticleToGrid_comp);

namespace irr
{
namespace video
{

E_MATERIAL_TYPE MrVkFluid::s_materialType = EMT_TRANSPARENT_ALPHA_CHANNEL;

// FIXED ISSUES:
// 1. Graphics pipeline creation was commented out - now enabled
// 2. Shader bytecode references - now using actual VkFluid compiled shaders
// 3. OnRender method didn't check for valid pipeline - now checks VK_NULL_HANDLE
// 4. Vertex input state setup to match full-screen quad rendering
// 5. Found and linked actual VkFluid shaders in VkFluid directory
// 6. Added missing compute descriptor binding 3 for storage image (density texture)
// 7. Fixed storage image descriptor validation by deferring binding until actual textures are set
// 8. Added texture validation checks in compute dispatch methods to prevent unbound descriptor errors

MrVkFluid::MrVkFluid(IVideoDriver* driver, io::IFileSystem* fileSystem)
    : VkMaterialRenderer(driver)
    , m_fileSystem(fileSystem)
    , m_descriptorSet(VK_NULL_HANDLE)
    , m_particleToGridComputeDescriptorSet(VK_NULL_HANDLE)
    , m_densityComputeDescriptorSet(VK_NULL_HANDLE)
    , m_covarianceComputeDescriptorSet(VK_NULL_HANDLE)
    , m_mainComputeDescriptorSet(VK_NULL_HANDLE)
    , m_descriptorSetLayout(VK_NULL_HANDLE)
    , m_computeDescriptorSetLayout(VK_NULL_HANDLE)
    , m_particleToGridDescriptorSetLayout(VK_NULL_HANDLE)
    , m_descriptorPool(VK_NULL_HANDLE)
    , m_defaultSampler(VK_NULL_HANDLE)
    , m_pipelineLayout(VK_NULL_HANDLE)
    , m_computePipelineLayout(VK_NULL_HANDLE)
    , m_particleToGridPipelineLayout(VK_NULL_HANDLE)
    , m_graphicsPipeline(VK_NULL_HANDLE)
    , m_particleToGridComputePipeline(VK_NULL_HANDLE)
    , m_densityComputePipeline(VK_NULL_HANDLE)
    , m_covarianceComputePipeline(VK_NULL_HANDLE)
    , m_mainComputePipeline(VK_NULL_HANDLE)
    , m_particleToGridComputeCommandBuffer(VK_NULL_HANDLE) 
    , m_covarianceComputeCommandBuffer(VK_NULL_HANDLE)
    , m_mainComputeCommandBuffer(VK_NULL_HANDLE)
    , m_computeFence(VK_NULL_HANDLE)
    , m_time(0.0f)
    , m_deltaTime(0.0f)
    , m_needsUpdate(true)
    , m_renderPipeline(FRP_RAYMARCHING)
    , m_particleBuffer(nullptr)
    , m_covarianceBuffer(nullptr)
    , m_gridTexture(nullptr)
    , m_gridTextureSecondary(nullptr)
    , m_densityTexture(nullptr)
#if COVARIANCE_COMPUTATION
    , m_covarianceTexture(nullptr)
#endif
    , m_depthTexture(nullptr)
{
    if (m_fileSystem)
        m_fileSystem->grab();

    // Initialize matrices
    m_worldMatrix.makeIdentity();
    m_viewMatrix.makeIdentity();
    m_projectionMatrix.makeIdentity();

    // Initialize uniform data
    memset(&m_uniforms, 0, sizeof(m_uniforms));
    memset(&m_computeParams, 0, sizeof(m_computeParams));
    memset(&cb, 0, sizeof(cb));

    // Set default volume parameters (scaled to match original scene coordinates)

    float sizeR = 20.f * MUL_FLUID_SCALE(1.f);  // Match SnVkFluid bounds
    m_uniforms.volumeMin = core::vector3df(-sizeR, 0, -sizeR);  // (-2000, 0, -2000)
    m_uniforms.volumeMax = core::vector3df(sizeR, 50.0f * MUL_FLUID_SCALE(1.f), sizeR);  // (2000, 3000, 2000)
    m_uniforms.volumeSize = m_uniforms.volumeMax - m_uniforms.volumeMin;
    m_uniforms.rayStepSize = 0.5f * MUL_FLUID_SCALE(1.f);
    m_uniforms.densityThreshold = 0.1f;
    m_uniforms.maxRaymarchSteps = 128;
    m_uniforms.renderDiffuse = 1;
    m_uniforms.numParticles = 0;


    initializeShaders();
    createUniformBuffers();e
    createDefaultSampler();
    createComputeCommandBuffers();
    setupDescriptorSets();
    recreatePipelines();
}

MrVkFluid::~MrVkFluid()
{
    // Clean up Vulkan resources
    VkDevice device = Driver->Device;

    if (m_graphicsPipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_graphicsPipeline, nullptr);
    }

    if (m_particleToGridComputePipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_particleToGridComputePipeline, nullptr);
    }

    if (m_densityComputePipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_densityComputePipeline, nullptr);
    }

    if (m_covarianceComputePipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_covarianceComputePipeline, nullptr);
    }

    if (m_mainComputePipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_mainComputePipeline, nullptr);
    }

    if (m_pipelineLayout != VK_NULL_HANDLE) {
        vkDestroyPipelineLayout(device, m_pipelineLayout, nullptr);
    }

    if (m_computePipelineLayout != VK_NULL_HANDLE) {
        vkDestroyPipelineLayout(device, m_computePipelineLayout, nullptr);
    }

    if (m_particleToGridPipelineLayout != VK_NULL_HANDLE) {
        vkDestroyPipelineLayout(device, m_particleToGridPipelineLayout, nullptr);
    }

    if (m_descriptorSetLayout != VK_NULL_HANDLE) {
        vkDestroyDescriptorSetLayout(device, m_descriptorSetLayout, nullptr);
    }

    if (m_computeDescriptorSetLayout != VK_NULL_HANDLE) {
        vkDestroyDescriptorSetLayout(device, m_computeDescriptorSetLayout, nullptr);
    }

    if (m_particleToGridDescriptorSetLayout != VK_NULL_HANDLE) {
        vkDestroyDescriptorSetLayout(device, m_particleToGridDescriptorSetLayout, nullptr);
    }

    if (m_descriptorPool != VK_NULL_HANDLE) {
        vkDestroyDescriptorPool(device, m_descriptorPool, nullptr);
    }

    if (m_defaultSampler != VK_NULL_HANDLE) {
        vkDestroySampler(device, m_defaultSampler, nullptr);
    }

    if (m_computeFence != VK_NULL_HANDLE) {
        vkDestroyFence(device, m_computeFence, nullptr);
    }

    // Free compute command buffers
    if (m_particleToGridComputeCommandBuffer != VK_NULL_HANDLE ||
        m_particleToGridComputeCommandBuffer != VK_NULL_HANDLE ||
        m_covarianceComputeCommandBuffer != VK_NULL_HANDLE ||
        m_mainComputeCommandBuffer != VK_NULL_HANDLE) {
        VkDriver* vkDriver = static_cast<VkDriver*>(Driver);
        VkCommandBuffer cmdBuffers[] = {
            m_particleToGridComputeCommandBuffer,
            m_particleToGridComputeCommandBuffer,
            m_covarianceComputeCommandBuffer,
            m_mainComputeCommandBuffer
        };
        vkFreeCommandBuffers(device, vkDriver->getComputeCommandPool(), 4, cmdBuffers);
    }

    if (m_fileSystem)
        m_fileSystem->drop();
}

bool MrVkFluid::OnRender(IMaterialRendererServices* service, E_VERTEX_TYPE vtxtype, int paraId)
{
    if (m_needsUpdate) {
        updateUniformBuffers();
        m_needsUpdate = false;
    }

    // Check if pipeline is valid before using it
    if (m_graphicsPipeline == VK_NULL_HANDLE) {
        return false; // Pipeline not created yet
    }

    // Bind descriptor sets and pipeline for volume raymarching
    VkCommandBuffer cmdBuffer = Driver->currentCmdBuffer();

    vkCmdBindPipeline(cmdBuffer, VK_PIPELINE_BIND_POINT_GRAPHICS, m_graphicsPipeline);
    vkCmdBindDescriptorSets(cmdBuffer, VK_PIPELINE_BIND_POINT_GRAPHICS,
                           m_pipelineLayout, 0, 1, &m_descriptorSet, 0, nullptr);

    return true;
}

void MrVkFluid::OnSetMaterial(const SMaterial& material, const SMaterial& lastMaterial,
                             bool resetAllRenderstates, IMaterialRendererServices* services)
{
    CurrentMaterial = material;
    services->setBasicRenderStates(material, lastMaterial, resetAllRenderstates);
    // Update graphics descriptor sets to reflect the new SHADER_READ_ONLY_OPTIMAL layout
    updateGraphicsDescriptorSetsForShaderRead();

    // Set render pipeline from material parameter
    m_renderPipeline = static_cast<FluidRenderPipeline>(static_cast<u32>(material.MaterialTypeParam));
    m_needsUpdate = true;
}

void MrVkFluid::OnUnsetMaterial()
{
    // Nothing specific to do here
}

const void* MrVkFluid::getShaderByteCode() const
{
    // Return vertex shader bytecode for pipeline validation
    return Sbc_VkFluid__VkFluidShaderToy_vert.code;
}

u32 MrVkFluid::getShaderByteCodeSize() const
{
    return Sbc_VkFluid__VkFluidShaderToy_vert.length;
}

void MrVkFluid::cleanFrameCache()
{
    // Clean up per-frame resources if any
}

void MrVkFluid::preSubmit()
{
    // Pre-submission setup if needed
}

void MrVkFluid::ReloadShaders()
{

    m_vertexFragmentShader->recreatePS("D:/AProj/UaIrrlicht/source/Irrlicht/VulkanRenderer/shader/Compiled/SnTemplate/VkFluid/VkFluidShaderToy_frag_SpirV.spv");
	m_particleToGridComputeShader->recreateCS(  "D:/AProj/UaIrrlicht/source/Irrlicht/VulkanRenderer/shader/Compiled/SnTemplate/VkFluid/VkFluidParticleToGrid_comp_SpirV.spv");
	m_densityComputeShader->recreateCS(         "D:/AProj/UaIrrlicht/source/Irrlicht/VulkanRenderer/shader/Compiled/SnTemplate/VkFluid/VkFluidDensity_comp_SpirV.spv");
    recreatePipelines();
}

// Volume parameter setters
void MrVkFluid::setVolumeExtents(const core::vector3df& minBounds, const core::vector3df& maxBounds)
{
    m_uniforms.volumeMin = minBounds;
    m_uniforms.volumeMax = maxBounds;
    m_uniforms.volumeSize = maxBounds - minBounds;
    m_needsUpdate = true;
}

void MrVkFluid::setRaymarchParameters(u32 maxSteps, f32 stepSize, f32 densityThreshold)
{
    m_uniforms.maxRaymarchSteps = maxSteps;
    m_uniforms.rayStepSize = stepSize;
    m_uniforms.densityThreshold = densityThreshold;
    m_needsUpdate = true;
}

 
void MrVkFluid::setAbsorptionColor(const core::vector3df& absorption)
{
    // Store absorption color if needed
    m_needsUpdate = true;
}

void MrVkFluid::setCameraPosition(const core::vector3df& position)
{
    cb.cameraPos = float4(position.X, position.Y, position.Z, 0.0f) ;
    m_needsUpdate = true;
}

// Matrix setters
void MrVkFluid::setWorldMatrix(const core::matrix4& world)
{
    m_worldMatrix = world;
    m_needsUpdate = true;
}

void MrVkFluid::setViewProjMatrix(const core::matrix4& view, const core::matrix4& projection)
{
    m_viewMatrix = view; m_projectionMatrix = projection;
    
    // Update view-projection inverse matrix
    updateViewProjectionInverse();
    m_needsUpdate = true;
}

 

void MrVkFluid::transitDepthImageLayout()
{


    if (0&&vkDepthImg) {
        // Transition depth image layout from attachment optimal to read-only optimal for shader sampling
        VkCommandPool pool;
        VkCommandBuffer copyCmd = Driver->mDevice->createCommandBuffer(VK_COMMAND_BUFFER_LEVEL_PRIMARY, pool, true);

        VkImageSubresourceRange depthSubRange{};
        depthSubRange.aspectMask = VK_IMAGE_ASPECT_DEPTH_BIT;
        depthSubRange.baseMipLevel = 0;
        depthSubRange.levelCount = 1;
        depthSubRange.baseArrayLayer = 0;
        depthSubRange.layerCount = 1;

        vks::tools::setImageLayout(
            copyCmd,
            vkDepthImg,
            VK_IMAGE_LAYOUT_DEPTH_STENCIL_ATTACHMENT_OPTIMAL,  // oldLayout
            VK_IMAGE_LAYOUT_DEPTH_STENCIL_READ_ONLY_OPTIMAL,   // newLayout
            depthSubRange 
        );

        Driver->mDevice->flushCommandBuffer(copyCmd, pool, Driver->queueCopy());
    }
}

// ShaderToy compatibility methods (following VkMr2D pattern)
void MrVkFluid::setResolution(const float3& resolution)
{
    cb.iResolution = resolution;
    cb.res = float2(resolution.x, resolution.y);
    cb.resWdH = resolution.x / resolution.y;
    cb.resHdW = resolution.y / resolution.x;
    m_needsUpdate = true;
}

void MrVkFluid::setFrame(s32 frame)
{
    cb.iFrame = frame;
    m_needsUpdate = true;
}

void MrVkFluid::setMouse(const float4& mouse)
{
    cb.iMouse = mouse;
    m_needsUpdate = true;
}

// Buffer management
void MrVkFluid::setParticleBuffer(VkHardwareBuffer* buffer)
{
    m_particleBuffer = buffer;
    
    // Note: Graphics pipeline now uses 3D textures instead of storage buffers
    // Storage buffers are only used by compute pipelines
}
void MrVkFluid::setCovarianceBuffer(VkHardwareBuffer* buffer)
{
    m_covarianceBuffer = buffer;

    // Note: Graphics pipeline now uses 3D textures instead of storage buffers
    // Storage buffers are only used by compute pipelines
}
void MrVkFluid::setParticleGridTexture(ITexture* texture)
{
    m_gridTexture = texture;  // Track the grid texture
    
    if (texture) {
        VkTexture* vkTexture = static_cast<VkTexture*>(texture);
        
 
        
        // Update compute descriptor set binding 1 for particle grid storage image
        for (VkDescriptorSet computeSet : { m_densityComputeDescriptorSet, m_covarianceComputeDescriptorSet, m_mainComputeDescriptorSet }) {
            if (computeSet != VK_NULL_HANDLE) {
                VkDescriptorImageInfo storageImageDescriptor{};
                storageImageDescriptor.imageView = vkTexture->getShaderResourceView();
                storageImageDescriptor.sampler = VK_NULL_HANDLE; // Storage images don't use samplers
                storageImageDescriptor.imageLayout = VK_IMAGE_LAYOUT_GENERAL;
                
                VkWriteDescriptorSet writeDescriptorSet = vks::initializers::writeDescriptorSet(
                    computeSet,
                    VK_DESCRIPTOR_TYPE_STORAGE_IMAGE,
                    GRID_TEXTURE_BINDING,   
                    &storageImageDescriptor
                );
                vkUpdateDescriptorSets(Driver->Device, 1, &writeDescriptorSet, 0, nullptr);
            }
        }
        
        DPINFO(("DEBUG: Updated particle grid texture (R32F format) for compute descriptors\n"));
    }
}

void MrVkFluid::setParticleGridTextureSecondary(ITexture* texture)
{
    m_gridTextureSecondary = texture;  // Track the secondary grid texture
    
    if (texture) {
        VkTexture* vkTexture = static_cast<VkTexture*>(texture);
        
        // Update compute descriptor set binding 6 for secondary particle grid storage image
        for (VkDescriptorSet computeSet : { m_particleToGridComputeDescriptorSet, m_densityComputeDescriptorSet, m_covarianceComputeDescriptorSet, m_mainComputeDescriptorSet }) {
            if (computeSet != VK_NULL_HANDLE) {
                VkDescriptorImageInfo storageImageDescriptor{};
                storageImageDescriptor.imageView = vkTexture->getShaderResourceView();
                storageImageDescriptor.sampler = VK_NULL_HANDLE; // Storage images don't use samplers
                storageImageDescriptor.imageLayout = VK_IMAGE_LAYOUT_GENERAL;
                
                VkWriteDescriptorSet writeDescriptorSet = vks::initializers::writeDescriptorSet(
                    computeSet,
                    VK_DESCRIPTOR_TYPE_STORAGE_IMAGE,
                    GRID_TEXTURE_SECONDARY_BINDING,   
                    &storageImageDescriptor
                );
                vkUpdateDescriptorSets(Driver->Device, 1, &writeDescriptorSet, 0, nullptr);
            }
        }
        
        DPINFO(("DEBUG: Updated secondary particle grid texture for compute descriptors\n"));
    }
}

// Volume texture setters (these would update descriptor sets when textures change)
void MrVkFluid::setDensityFieldTexture(ITexture* texture)
{
    // Only update if texture actually changed
    if (m_densityTexture == texture) {
        return; // No change, skip expensive descriptor set update
    }
    
    m_densityTexture = texture;
    
    if (texture) {
        VkTexture* vkTexture = static_cast<VkTexture*>(texture);
        
        // Update compute descriptor set binding 2 for storage image usage
        if (m_densityComputeDescriptorSet != VK_NULL_HANDLE) {
            VkDescriptorImageInfo storageImageDescriptor{};
            storageImageDescriptor.imageView = vkTexture->getShaderResourceView();
            storageImageDescriptor.sampler = VK_NULL_HANDLE; // Storage images don't use samplers
            storageImageDescriptor.imageLayout = VK_IMAGE_LAYOUT_GENERAL;
            
            VkWriteDescriptorSet writeDescriptorSet = vks::initializers::writeDescriptorSet(
                m_densityComputeDescriptorSet,
                VK_DESCRIPTOR_TYPE_STORAGE_IMAGE,
                DENSITY_TEXTURE_BINDING,  
                &storageImageDescriptor
            );
            vkUpdateDescriptorSets(Driver->Device, 1, &writeDescriptorSet, 0, nullptr);
            
            DPINFO(("DEBUG: Updated density texture compute descriptor set\n"));
        }
        
    }
}



 
void MrVkFluid::setCovarianceFieldTexture(ITexture* texture)
{
#if COVARIANCE_COMPUTATION
    // Only update if texture actually changed
    if (m_covarianceTexture == texture) {
        return; // No change, skip expensive descriptor set update
    }
    
    m_covarianceTexture = texture;
    
    if (texture) {
        VkTexture* vkTexture = static_cast<VkTexture*>(texture);
        
        // Update COVARIANCE compute descriptor set binding 2 for storage image usage (MAIN BINDING)
        if (m_covarianceComputeDescriptorSet != VK_NULL_HANDLE) {
            VkDescriptorImageInfo storageImageDescriptor{};
            storageImageDescriptor.imageView = vkTexture->getShaderResourceView();
            storageImageDescriptor.sampler = VK_NULL_HANDLE; // Storage images don't use samplers
            storageImageDescriptor.imageLayout = VK_IMAGE_LAYOUT_GENERAL;
            
            VkWriteDescriptorSet writeDescriptorSet = vks::initializers::writeDescriptorSet(
                m_covarianceComputeDescriptorSet,
                VK_DESCRIPTOR_TYPE_STORAGE_IMAGE,
                COVARIANCE_TEXTURE_BINDING,   
                &storageImageDescriptor
            );
            vkUpdateDescriptorSets(Driver->Device, 1, &writeDescriptorSet, 0, nullptr);
            
            DPINFO(("DEBUG: Updated covariance texture covariance compute descriptor set (binding 2)\n"));
        }               

    }
#endif
}

void MrVkFluid::setDepthTexture(ITexture* texture)
{
    // Only update if texture actually changed
    if (m_depthTexture == texture) {
        return; // No change, skip expensive descriptor set update
    }
    
    m_depthTexture = texture;
    
    if (texture && m_descriptorSet != VK_NULL_HANDLE) {
        VkTexture* vkTexture = static_cast<VkTexture*>(texture);
        
        // Update graphics descriptor set binding 2 for depth texture sampling
        VkDescriptorImageInfo depthSamplerDescriptor{};
        depthSamplerDescriptor.imageView = ((VkTexture*)vkTexture)->getVRP()->DepthAtm.view;

        vkDepthImg = ((VkTexture*)vkTexture)->getVRP()->DepthAtm.image;

        
        depthSamplerDescriptor.sampler = m_defaultSampler;
        depthSamplerDescriptor.imageLayout = VK_IMAGE_LAYOUT_DEPTH_ATTACHMENT_STENCIL_READ_ONLY_OPTIMAL;  // Use read-only layout after transition
        
        VkWriteDescriptorSet writeDescriptorSet = vks::initializers::writeDescriptorSet(
            m_descriptorSet,
            VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER,
            2,  
            &depthSamplerDescriptor
        );
        vkUpdateDescriptorSets(Driver->Device, 1, &writeDescriptorSet, 0, nullptr);
        
        DPINFO(("DEBUG: Updated depth texture graphics descriptor set (binding 2)\n"));
    }
}

void MrVkFluid::setEnvironmentMap(ITexture* texture)
{
    // Only update if texture actually changed
    if (m_environmentTexture == texture) {
        return; // No change, skip expensive descriptor set update
    }
    
    m_environmentTexture = texture;
    
    if (texture && m_descriptorSet != VK_NULL_HANDLE) {
        VkTexture* vkTexture = static_cast<VkTexture*>(texture);
        
        // Update graphics descriptor set binding 4 for environment map (iChannel3)
        VkDescriptorImageInfo envMapDescriptor{};
        envMapDescriptor.imageView = vkTexture->getShaderResourceView();
        envMapDescriptor.sampler = m_defaultSampler;
        envMapDescriptor.imageLayout = VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL;
        
        VkWriteDescriptorSet writeDescriptorSet = vks::initializers::writeDescriptorSet(
            m_descriptorSet,
            VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER,
            4,  // binding 4 for environment map (iChannel3)
            &envMapDescriptor
        );
        vkUpdateDescriptorSets(Driver->Device, 1, &writeDescriptorSet, 0, nullptr);
        
        DPINFO(("DEBUG: Updated environment map graphics descriptor set (binding 4)\n"));
	}
}

void MrVkFluid::updateVolumeUniforms(const VkFluidVolumeUniforms& uniforms)
{
    m_uniforms = uniforms;
    m_uniforms.kernelRadiusSq = m_uniforms.kernelRadius * m_uniforms.kernelRadius;
    cb.u = m_uniforms;
    // Add mass scaling parameters for diffuse particle handling
    // Regular particles: mass = 1.0, Diffuse particles: mass = 0.5
    // These will be used by compute shaders to properly weight particle contributions
    
    m_needsUpdate = true;
}

// Private implementation methods
void MrVkFluid::initializeShaders()
{
    try {
        // Load and create vertex/fragment shader for volume raymarching
        m_vertexFragmentShader = std::make_shared<VkFxUtil::VkFxBase>(Driver->Device);

        // Use actual VkFluid compiled shaders
        m_vertexFragmentShader->createVS(Sbc_VkFluid__VkFluidShaderToy_vert, "main");
        m_vertexFragmentShader->createPS(Sbc_VkFluid__VkFluidShaderToy_frag, "main");

        // Load compute shaders - now available
        m_particleToGridComputeShader = std::make_shared<VkFxUtil::VkFxBase>(Driver->Device);
        m_particleToGridComputeShader->createCS(Sbc_VkFluid__VkFluidParticleToGrid_comp, "main");

        m_densityComputeShader = std::make_shared<VkFxUtil::VkFxBase>(Driver->Device);
        m_densityComputeShader->createCS(Sbc_VkFluid__VkFluidDensity_comp, "main");

        m_covarianceComputeShader = std::make_shared<VkFxUtil::VkFxBase>(Driver->Device);
        m_covarianceComputeShader->createCS(Sbc_VkFluid__VkFluidCovariance_comp, "main");
         
    } catch (...) {
        // If shader loading fails, set vertex/fragment shader to null
        m_vertexFragmentShader = nullptr;
    }
}

void MrVkFluid::createUniformBuffers()
{
    VkDriver* vkDriver = static_cast<VkDriver*>(Driver);
    
    // Create uniform buffer for graphics pipeline (ShaderToy uniforms)
    VK_CHECK_RESULT(vkDriver->mDevice->createBuffer(
        VK_BUFFER_USAGE_UNIFORM_BUFFER_BIT,
        VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT,
        &m_cbBuffer,
        sizeof(CbVkFluid)));

    // Create uniform buffer for compute pipelines (legacy volume uniforms)
    VK_CHECK_RESULT(vkDriver->mDevice->createBuffer(
        VK_BUFFER_USAGE_UNIFORM_BUFFER_BIT,
        VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT,
        &m_computeUniformBuffer,
        sizeof(VkFluidVolumeUniforms)));

    // Create dummy storage buffer for descriptor set validation
    VK_CHECK_RESULT(vkDriver->mDevice->createBuffer(
        VK_BUFFER_USAGE_STORAGE_BUFFER_BIT,
        VK_MEMORY_PROPERTY_DEVICE_LOCAL_BIT,
        &m_dummyStorageBuffer,
        64));

    // Map buffers persistently

    VK_CHECK_RESULT(m_computeUniformBuffer.map());
    assert(m_cbBuffer.mMemoryPropertyFlags & VK_MEMORY_PROPERTY_HOST_COHERENT_BIT); // one time map need VK_MEMORY_PROPERTY_HOST_COHERENT_BIT
}

void MrVkFluid::createDefaultSampler()
{
    VkDevice device = Driver->Device;
    
    VkSamplerCreateInfo samplerInfo{};
    samplerInfo.sType = VK_STRUCTURE_TYPE_SAMPLER_CREATE_INFO;
    samplerInfo.magFilter = VK_FILTER_LINEAR;
    samplerInfo.minFilter = VK_FILTER_LINEAR;
    samplerInfo.mipmapMode = VK_SAMPLER_MIPMAP_MODE_LINEAR;
    samplerInfo.addressModeU = VK_SAMPLER_ADDRESS_MODE_CLAMP_TO_EDGE;
    samplerInfo.addressModeV = VK_SAMPLER_ADDRESS_MODE_CLAMP_TO_EDGE;
    samplerInfo.addressModeW = VK_SAMPLER_ADDRESS_MODE_CLAMP_TO_EDGE;
    samplerInfo.mipLodBias = 0.0f;
    samplerInfo.anisotropyEnable = VK_FALSE;
    samplerInfo.maxAnisotropy = 1.0f;
    samplerInfo.compareEnable = VK_FALSE;
    samplerInfo.compareOp = VK_COMPARE_OP_ALWAYS;
    samplerInfo.minLod = 0.0f;
    samplerInfo.maxLod = 1.0f;
    samplerInfo.borderColor = VK_BORDER_COLOR_FLOAT_TRANSPARENT_BLACK;
    samplerInfo.unnormalizedCoordinates = VK_FALSE;

    VK_CHECK_RESULT(vkCreateSampler(device, &samplerInfo, nullptr, &m_defaultSampler));
}

void MrVkFluid::createComputeCommandBuffers()
{
    VkDevice device = Driver->Device;
    VkDriver* vkDriver = static_cast<VkDriver*>(Driver);

    // Create command buffers for compute operations
    VkCommandBufferAllocateInfo cmdBufAllocateInfo = vks::initializers::commandBufferAllocateInfo(
        vkDriver->getComputeCommandPool(),
        VK_COMMAND_BUFFER_LEVEL_PRIMARY,
        4);

    VkCommandBuffer cmdBuffers[4];
    VK_CHECK_RESULT(vkAllocateCommandBuffers(device, &cmdBufAllocateInfo, cmdBuffers));
    
    m_particleToGridComputeCommandBuffer = cmdBuffers[0];
    m_particleToGridComputeCommandBuffer = cmdBuffers[1];
    m_covarianceComputeCommandBuffer = cmdBuffers[2];
    m_mainComputeCommandBuffer = cmdBuffers[3];

    // Create fence for compute synchronization
    VkFenceCreateInfo fenceCreateInfo = vks::initializers::fenceCreateInfo(VK_FENCE_CREATE_SIGNALED_BIT);
    VK_CHECK_RESULT(vkCreateFence(device, &fenceCreateInfo, nullptr, &m_computeFence));
}

void MrVkFluid::updateViewProjectionInverse()
{
    // Compute view-projection matrix using consistent matrices
    glm::mat4 viewProjection = m_projectionMatrix * m_viewMatrix;
    
    cb.mViewProjectionInverse = glm::inverse(viewProjection);
     
}

void MrVkFluid::updateUniformBuffers()
{
    // Sync legacy uniforms to flat ShaderToy uniform structure


    cb.u = m_uniforms;
    


    // Convert volume bounds
    cb.volumeMin = float4(
        m_uniforms.volumeMin.x, m_uniforms.volumeMin.y, m_uniforms.volumeMin.z, 0.0f);
    cb.volumeMax = float4(
        m_uniforms.volumeMax.x, m_uniforms.volumeMax.y, m_uniforms.volumeMax.z, 0.0f);
	
    VK_CHECK_RESULT(m_cbBuffer.map());
    // Update ShaderToy uniform buffer for graphics pipeline
    if (m_cbBuffer.mapped) {
        // Copy flat ShaderToy compatible uniforms to graphics pipeline
        memcpy(m_cbBuffer.mapped, &cb, sizeof(CbVkFluid));
    }
    m_cbBuffer.unmap();
	m_computeParams = m_uniforms; // Copy legacy uniforms to compute params
    // Update compute uniform buffer (legacy volume uniforms)
    m_computeParams.time = m_time;
    m_computeParams.numParticles = m_uniforms.numParticles;
    
    if (m_computeUniformBuffer.mapped) {
        // Copy only the GPU-relevant part of VkFluidVolumeUniforms (skip resolution fields)
        // Start from volumeMin field onwards to match shader layout
        size_t gpuStructOffset = offsetof(VkFluidVolumeUniforms, volumeMin);
        size_t gpuStructSize = sizeof(VkFluidVolumeUniforms) - gpuStructOffset - sizeof(f32); // Exclude kernelRadius
        memcpy(m_computeUniformBuffer.mapped, 
               ((char*)&m_computeParams) + gpuStructOffset, 
               gpuStructSize);
    }
}

void MrVkFluid::setupDescriptorSets()
{
    VkDevice device = Driver->Device;

    // Graphics descriptor set layout (updated for Image.glsl fragment shader with depth testing)
    std::vector<VkDescriptorSetLayoutBinding> graphicsBindings = {
        // Binding 0: Uniform buffer
        { 0, VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER, 1, VK_SHADER_STAGE_VERTEX_BIT | VK_SHADER_STAGE_FRAGMENT_BIT, nullptr },
        // Binding 1: Grid particles texture (iChannel0 - sampler3D)
        { 1, VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER, 1, VK_SHADER_STAGE_FRAGMENT_BIT, nullptr },
        // Binding 2: Depth buffer texture (iDepth - sampler2D) for depth testing
        { 2, VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER, 1, VK_SHADER_STAGE_FRAGMENT_BIT, nullptr },
        // Binding 3: Density field texture (iChannel1 - sampler3D)  
        { 3, VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER, 1, VK_SHADER_STAGE_FRAGMENT_BIT, nullptr },
        // Binding 4: Environment map (iChannel3 - samplerCube)
        { 4, VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER, 1, VK_SHADER_STAGE_FRAGMENT_BIT, nullptr },
        #if COVARIANCE_COMPUTATION
        // Binding 5: Covariance matrices texture (iChannel2 - sampler3D)
        { 5, VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER, 1, VK_SHADER_STAGE_FRAGMENT_BIT, nullptr },
#endif
    };

    VkDescriptorSetLayoutCreateInfo layoutInfo{};
    layoutInfo.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_SET_LAYOUT_CREATE_INFO;
    layoutInfo.bindingCount = static_cast<uint32_t>(graphicsBindings.size());
    layoutInfo.pBindings = graphicsBindings.data();

    VK_CHECK_RESULT(vkCreateDescriptorSetLayout(device, &layoutInfo, nullptr, &m_descriptorSetLayout));

    // Compute descriptor set layout (for Density and Covariance shaders that use storage images)
    std::vector<VkDescriptorSetLayoutBinding> computeBindings = {
        // Binding 0: Compute uniform buffer (FluidUniforms - same for all shaders)
        { 0, VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER, 1, VK_SHADER_STAGE_COMPUTE_BIT, nullptr },
        // Binding 1: Particle grid texture (storage image for density/covariance shaders)
        { 1, VK_DESCRIPTOR_TYPE_STORAGE_IMAGE, 1, VK_SHADER_STAGE_COMPUTE_BIT, nullptr },
        // Binding 2: Output texture (storage image output)
        { 2, VK_DESCRIPTOR_TYPE_STORAGE_IMAGE, 1, VK_SHADER_STAGE_COMPUTE_BIT, nullptr },
        // Binding 3: Velocity texture (storage image)
        { 3, VK_DESCRIPTOR_TYPE_STORAGE_IMAGE, 1, VK_SHADER_STAGE_COMPUTE_BIT, nullptr },
        // Binding 4: Distance texture (storage image)
        { 4, VK_DESCRIPTOR_TYPE_STORAGE_IMAGE, 1, VK_SHADER_STAGE_COMPUTE_BIT, nullptr },
        // Binding 5: Covariance texture (storage image)
                #if COVARIANCE_COMPUTATION
        { 5, VK_DESCRIPTOR_TYPE_STORAGE_IMAGE, 1, VK_SHADER_STAGE_COMPUTE_BIT, nullptr },
#endif
        // Binding 6: Secondary particle grid texture (storage image)
        { 6, VK_DESCRIPTOR_TYPE_STORAGE_IMAGE, 1, VK_SHADER_STAGE_COMPUTE_BIT, nullptr }
    };

    // ParticleToGrid shader layout (uses storage buffer for particle positions input)
    std::vector<VkDescriptorSetLayoutBinding> particleToGridBindings = {
        // Binding 0: Compute uniform buffer (FluidUniforms)
        { 0, VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER, 1, VK_SHADER_STAGE_COMPUTE_BIT, nullptr },       
        { 1, VK_DESCRIPTOR_TYPE_STORAGE_IMAGE, 1, VK_SHADER_STAGE_COMPUTE_BIT, nullptr },
        { 2, VK_DESCRIPTOR_TYPE_STORAGE_BUFFER, 1, VK_SHADER_STAGE_COMPUTE_BIT, nullptr },
        // Binding 6: Secondary particle grid texture (storage image)
        { 6, VK_DESCRIPTOR_TYPE_STORAGE_IMAGE, 1, VK_SHADER_STAGE_COMPUTE_BIT, nullptr }
    };

    VkDescriptorSetLayoutCreateInfo computeLayoutInfo{};
    computeLayoutInfo.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_SET_LAYOUT_CREATE_INFO;
    computeLayoutInfo.bindingCount = static_cast<uint32_t>(computeBindings.size());
    computeLayoutInfo.pBindings = computeBindings.data();

    VK_CHECK_RESULT(vkCreateDescriptorSetLayout(device, &computeLayoutInfo, nullptr, &m_computeDescriptorSetLayout));

    // Create ParticleToGrid-specific descriptor set layout
    VkDescriptorSetLayoutCreateInfo particleToGridLayoutInfo{};
    particleToGridLayoutInfo.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_SET_LAYOUT_CREATE_INFO;
    particleToGridLayoutInfo.bindingCount = static_cast<uint32_t>(particleToGridBindings.size());
    particleToGridLayoutInfo.pBindings = particleToGridBindings.data();

    VK_CHECK_RESULT(vkCreateDescriptorSetLayout(device, &particleToGridLayoutInfo, nullptr, &m_particleToGridDescriptorSetLayout));

    // Create descriptor pool
    std::vector<VkDescriptorPoolSize> poolSizes = {
        { VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER, 10 },           // Graphics + 4 compute uniform buffers
        { VK_DESCRIPTOR_TYPE_STORAGE_BUFFER, 2 },           // ParticleToGrid storage buffer only
        { VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER, 20 },  // Graphics image samplers (4) + extras
        { VK_DESCRIPTOR_TYPE_STORAGE_IMAGE, 20 }            // Compute storage images (grid + density + covariance textures)
    };

    VkDescriptorPoolCreateInfo poolInfo = vks::initializers::descriptorPoolCreateInfo(
        static_cast<uint32_t>(poolSizes.size()),
        poolSizes.data(),
        5  // maxSets: graphics + 4 compute descriptor sets (ParticleToGrid + 3 others)
    );

    VK_CHECK_RESULT(vkCreateDescriptorPool(device, &poolInfo, nullptr, &m_descriptorPool));

    // Allocate descriptor sets
    VkDescriptorSetLayout layouts[] = {
        m_descriptorSetLayout,
        m_particleToGridDescriptorSetLayout,
        m_computeDescriptorSetLayout,
        m_computeDescriptorSetLayout,
        m_computeDescriptorSetLayout
    };

    VkDescriptorSetAllocateInfo allocInfo = vks::initializers::descriptorSetAllocateInfo(
        m_descriptorPool,
        layouts,
        5
    );

    VkDescriptorSet descriptorSets[5];
    VK_CHECK_RESULT(vkAllocateDescriptorSets(device, &allocInfo, descriptorSets));
    
    m_descriptorSet = descriptorSets[0];
    m_particleToGridComputeDescriptorSet = descriptorSets[1];
    m_densityComputeDescriptorSet = descriptorSets[2];
    m_covarianceComputeDescriptorSet = descriptorSets[3];
    m_mainComputeDescriptorSet = descriptorSets[4];

    // Initialize descriptor sets with dummy data
    // This will be properly updated when buffers and textures are set
    VkDriver* vkDriver = static_cast<VkDriver*>(Driver);
    
    // Default texture descriptor
    VkDescriptorImageInfo defaultTextureDescriptor{};
    defaultTextureDescriptor.imageView = VKTEX(vkDriver->NullTexture)->getShaderResourceView();
    defaultTextureDescriptor.sampler = m_defaultSampler;
    defaultTextureDescriptor.imageLayout = VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL;

    // Update graphics descriptor set (Image.glsl pattern with depth testing)
    std::vector<VkWriteDescriptorSet> writeDescriptorSets = {
        // Binding 0: Uniform buffer
        vks::initializers::writeDescriptorSet(m_descriptorSet, VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER, 0, &m_cbBuffer.descriptor),
        // Binding 1: Grid particles texture (iChannel0 - default for now)
        vks::initializers::writeDescriptorSet(m_descriptorSet, VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER, 1, &defaultTextureDescriptor),
        // Binding 2: Depth buffer texture (iDepth - default for now)
        vks::initializers::writeDescriptorSet(m_descriptorSet, VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER, 2, &defaultTextureDescriptor),
        // Binding 3: Density field texture (iChannel1 - default for now)
        vks::initializers::writeDescriptorSet(m_descriptorSet, VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER, 3, &defaultTextureDescriptor),
        // Binding 4: Environment map (iChannel3 - default for now)
        vks::initializers::writeDescriptorSet(m_descriptorSet, VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER, 4, &defaultTextureDescriptor),
                #if COVARIANCE_COMPUTATION
        // Binding 5: Covariance matrices texture (iChannel2 - default for now)
        vks::initializers::writeDescriptorSet(m_descriptorSet, VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER, 5, &defaultTextureDescriptor),
#endif
    };
    vkUpdateDescriptorSets(device, static_cast<uint32_t>(writeDescriptorSets.size()), writeDescriptorSets.data(), 0, nullptr);

    // Create a dummy storage image for compute shaders (since NullTexture doesn't have STORAGE_BIT)
    // For now, we'll skip binding 3 in the initial setup and only bind it when we have actual storage images
    VkDescriptorImageInfo defaultStorageImageDescriptor{};
    defaultStorageImageDescriptor.imageView = VK_NULL_HANDLE; // Will be set when actual storage images are available
    defaultStorageImageDescriptor.sampler = VK_NULL_HANDLE;
    defaultStorageImageDescriptor.imageLayout = VK_IMAGE_LAYOUT_GENERAL;

    // Update ParticleToGrid descriptor set with uniform buffer only
    {
        std::vector<VkWriteDescriptorSet> particleToGridWriteDescriptorSets = {
            vks::initializers::writeDescriptorSet(m_particleToGridComputeDescriptorSet, VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER, 0, &m_computeUniformBuffer.descriptor)
            // Note: Bindings 1-2 (storage buffer and storage image) will be updated when actual buffers/textures are set
        };
        vkUpdateDescriptorSets(device, static_cast<uint32_t>(particleToGridWriteDescriptorSets.size()), 
                              particleToGridWriteDescriptorSets.data(), 0, nullptr);
    }

    // Update compute descriptor sets with uniform buffer only
    // Storage images will be bound when actual textures are set
    for (VkDescriptorSet computeSet : { m_densityComputeDescriptorSet, m_covarianceComputeDescriptorSet, m_mainComputeDescriptorSet }) {
        std::vector<VkWriteDescriptorSet> computeWriteDescriptorSets = {
            vks::initializers::writeDescriptorSet(computeSet, VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER, 0, &m_computeUniformBuffer.descriptor)
            // Note: Bindings 1-5 (storage images) will be updated when actual textures are set
        };
        vkUpdateDescriptorSets(device, static_cast<uint32_t>(computeWriteDescriptorSets.size()), 
                              computeWriteDescriptorSets.data(), 0, nullptr);
    }
}

void MrVkFluid::recreatePipelines()
{
    VkDevice device = Driver->Device;

    // Clean up existing pipelines
    if (m_graphicsPipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_graphicsPipeline, nullptr);
        m_graphicsPipeline = VK_NULL_HANDLE;
    }

    if (m_particleToGridComputePipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_particleToGridComputePipeline, nullptr);
        m_particleToGridComputePipeline = VK_NULL_HANDLE;
    }

    if (m_densityComputePipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_densityComputePipeline, nullptr);
        m_densityComputePipeline = VK_NULL_HANDLE;
    }

    if (m_covarianceComputePipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_covarianceComputePipeline, nullptr);
        m_covarianceComputePipeline = VK_NULL_HANDLE;
    }

    if (m_mainComputePipeline != VK_NULL_HANDLE) {
        vkDestroyPipeline(device, m_mainComputePipeline, nullptr);
        m_mainComputePipeline = VK_NULL_HANDLE;
    }

    // Clean up pipeline layouts
    if (m_pipelineLayout != VK_NULL_HANDLE) {
        vkDestroyPipelineLayout(device, m_pipelineLayout, nullptr);
        m_pipelineLayout = VK_NULL_HANDLE;
    }

    if (m_computePipelineLayout != VK_NULL_HANDLE) {
        vkDestroyPipelineLayout(device, m_computePipelineLayout, nullptr);
        m_computePipelineLayout = VK_NULL_HANDLE;
    }

    // Recreate everything
    createPipelineLayouts();
    createGraphicsPipelines();
    createComputePipelines();
}

void MrVkFluid::createPipelineLayouts()
{
    VkDevice device = Driver->Device;

    // Create graphics pipeline layout
    VkPipelineLayoutCreateInfo pipelineLayoutInfo{};
    pipelineLayoutInfo.sType = VK_STRUCTURE_TYPE_PIPELINE_LAYOUT_CREATE_INFO;
    pipelineLayoutInfo.setLayoutCount = 1;
    pipelineLayoutInfo.pSetLayouts = &m_descriptorSetLayout;
    pipelineLayoutInfo.pushConstantRangeCount = 0;
    pipelineLayoutInfo.pPushConstantRanges = nullptr;

    VK_CHECK_RESULT(vkCreatePipelineLayout(device, &pipelineLayoutInfo, nullptr, &m_pipelineLayout));

    // Create ParticleToGrid pipeline layout
    VkPipelineLayoutCreateInfo particleToGridPipelineLayoutInfo{};
    particleToGridPipelineLayoutInfo.sType = VK_STRUCTURE_TYPE_PIPELINE_LAYOUT_CREATE_INFO;
    particleToGridPipelineLayoutInfo.setLayoutCount = 1;
    particleToGridPipelineLayoutInfo.pSetLayouts = &m_particleToGridDescriptorSetLayout;
    particleToGridPipelineLayoutInfo.pushConstantRangeCount = 0;
    particleToGridPipelineLayoutInfo.pPushConstantRanges = nullptr;

    VK_CHECK_RESULT(vkCreatePipelineLayout(device, &particleToGridPipelineLayoutInfo, nullptr, &m_particleToGridPipelineLayout));

    // Create compute pipeline layout
    VkPipelineLayoutCreateInfo computePipelineLayoutInfo{};
    computePipelineLayoutInfo.sType = VK_STRUCTURE_TYPE_PIPELINE_LAYOUT_CREATE_INFO;
    computePipelineLayoutInfo.setLayoutCount = 1;
    computePipelineLayoutInfo.pSetLayouts = &m_computeDescriptorSetLayout;
    computePipelineLayoutInfo.pushConstantRangeCount = 0;
    computePipelineLayoutInfo.pPushConstantRanges = nullptr;

    VK_CHECK_RESULT(vkCreatePipelineLayout(device, &computePipelineLayoutInfo, nullptr, &m_computePipelineLayout));
}

void MrVkFluid::createGraphicsPipelines()
{
    VkDevice device = Driver->Device;
    VkDriver* vkDriver = static_cast<VkDriver*>(Driver);

    // Set up pipeline state using base class helper
    SetPipelineCommonInfo();

    // Create graphics pipeline for volume raymarching
    VkGraphicsPipelineCreateInfo pipelineInfo = vks::initializers::pipelineCreateInfo(
        m_pipelineLayout,
        vkDriver->curRenderPass(),
        0);

    // Set up vertex input state to match the full-screen quad vertices from SnVkFluid
    auto declNode = vkDriver->declarationMap.find(E_VERTEX_TYPE::EVT_STANDARD);
    if (declNode && m_vertexFragmentShader) {
        pipelineInfo.pVertexInputState = declNode->getValue()->getInputLayout(this, 
            (u64)m_vertexFragmentShader->CompiledCodeWithSignature());
    } else {
        // Fallback to empty vertex input state
        VkPipelineVertexInputStateCreateInfo emptyVertexInputState = vks::initializers::pipelineVertexInputStateCreateInfo();
        pipelineInfo.pVertexInputState = &emptyVertexInputState;
    }

    // Set up blending for transparency
    VkPipelineColorBlendAttachmentState blendAttachmentState = 
        vks::initializers::pipelineColorBlendAttachmentState(0xf, VK_TRUE);
    blendAttachmentState.srcColorBlendFactor = VK_BLEND_FACTOR_SRC_ALPHA;
    blendAttachmentState.dstColorBlendFactor = VK_BLEND_FACTOR_ONE_MINUS_SRC_ALPHA;
    blendAttachmentState.colorBlendOp = VK_BLEND_OP_ADD;
    blendAttachmentState.srcAlphaBlendFactor = VK_BLEND_FACTOR_ONE;
    blendAttachmentState.dstAlphaBlendFactor = VK_BLEND_FACTOR_ONE_MINUS_SRC_ALPHA;
    blendAttachmentState.alphaBlendOp = VK_BLEND_OP_ADD;

    VkPipelineColorBlendStateCreateInfo colorBlendState = 
        vks::initializers::pipelineColorBlendStateCreateInfo(1, &blendAttachmentState);

    // Apply common pipeline state
    pipelineInfo.pInputAssemblyState = &inputAssemblyState;
    pipelineInfo.pRasterizationState = &rasterizationState; 
    pipelineInfo.pColorBlendState = &colorBlendState;
    pipelineInfo.pMultisampleState = &multisampleState;
    pipelineInfo.pViewportState = &viewportState;
    pipelineInfo.pDepthStencilState = &depthStencilStateNoZWrite;
    pipelineInfo.pDynamicState = &dynamicState;

    // Apply shaders (when available)
    if (m_vertexFragmentShader) {
        m_vertexFragmentShader->applyShaders(&pipelineInfo);
        
        // Create graphics pipeline now that shaders are available
        VK_CHECK_RESULT(vkCreateGraphicsPipelines(device, vkDriver->PipelineCache, 1, &pipelineInfo, nullptr, &m_graphicsPipeline));
    }
}

void MrVkFluid::createComputePipelines()
{
    VkDevice device = Driver->Device;
    VkDriver* vkDriver = static_cast<VkDriver*>(Driver);

    // Create ParticleToGrid compute pipeline (uses separate layout)
    if (m_particleToGridComputeShader) {
        VkComputePipelineCreateInfo pipelineInfo = vks::initializers::computePipelineCreateInfo(m_particleToGridPipelineLayout, 0);
        m_particleToGridComputeShader->applyComputeShader(&pipelineInfo);
        VK_CHECK_RESULT(vkCreateComputePipelines(device, vkDriver->PipelineCache, 1, &pipelineInfo, nullptr, &m_particleToGridComputePipeline));
    }

    // Create compute pipelines now that actual shaders are available
    if (m_densityComputeShader) {
        VkComputePipelineCreateInfo pipelineInfo = vks::initializers::computePipelineCreateInfo(m_computePipelineLayout, 0);
        m_densityComputeShader->applyComputeShader(&pipelineInfo);
        VK_CHECK_RESULT(vkCreateComputePipelines(device, vkDriver->PipelineCache, 1, &pipelineInfo, nullptr, &m_densityComputePipeline));
    }
#if COVARIANCE_COMPUTATION
    if (m_covarianceComputeShader) {
        VkComputePipelineCreateInfo pipelineInfo = vks::initializers::computePipelineCreateInfo(m_computePipelineLayout, 0);
        m_covarianceComputeShader->applyComputeShader(&pipelineInfo);
        VK_CHECK_RESULT(vkCreateComputePipelines(device, vkDriver->PipelineCache, 1, &pipelineInfo, nullptr, &m_covarianceComputePipeline));
    }
#endif
 
}

// Compute shader dispatching methods
void MrVkFluid::dispatchParticleToGridComputation3D(const core::array<float4>& particles, u32 resX, u32 resY, u32 resZ)
{
    if (m_particleToGridComputePipeline == VK_NULL_HANDLE || particles.size() <2u) return;
    
    // Check if grid texture is set (required for storage image binding)
    if (!m_gridTexture) {
        DPINFO(("DEBUG: Skipping particle-to-grid computation - no grid texture set\n"));
        return;
    }
    
    // Check if secondary grid texture is set (also required)
    if (!m_gridTextureSecondary) {
        DPINFO(("DEBUG: Skipping particle-to-grid computation - no secondary grid texture set\n"));
        return;
    }

    VkDevice device = Driver->Device;
    
    // Update particle positions buffer with input data
    VkTexture* gridTexture = static_cast<VkTexture*>(m_gridTexture);
    VkTexture* gridTextureSecondary = static_cast<VkTexture*>(m_gridTextureSecondary);
    VkTexture* densityTexture = static_cast<VkTexture*>(m_densityTexture);

    // Transition required storage images to GENERAL layout
    //if (gridTexture) gridTexture->transitionLayoutForStorageAccess();
    //if (gridTextureSecondary) gridTextureSecondary->transitionLayoutForStorageAccess();
    densityTexture->transitionLayoutForStorageAccess();
#if COVARIANCE_COMPUTATION
    VkTexture* covarianceTexture = static_cast<VkTexture*>(m_covarianceTexture);
    if (covarianceTexture) covarianceTexture->transitionLayoutForStorageAccess();
#endif

    if (m_particleBuffer ) {
        video::VkHardwareBuffer* vkBuffer = static_cast<video::VkHardwareBuffer*>(m_particleBuffer);
        void* data = vkBuffer->lock(video::ETLM_WRITE_ONLY, 0, particles.size() * sizeof(float4));
        if (data) {
            memcpy(data, particles.const_pointer(), particles.size() * sizeof(float4));
            vkBuffer->unlock();
        }
        
        // Update ParticleToGrid descriptor set with particle buffer
        VkDescriptorBufferInfo bufferInfo{};
        bufferInfo.buffer = vkBuffer->getBufferResource();
        bufferInfo.offset = 0;
        bufferInfo.range = particles.size() * sizeof(float4);
        
        VkWriteDescriptorSet writeDescriptorSet = vks::initializers::writeDescriptorSet(
            m_particleToGridComputeDescriptorSet,
            VK_DESCRIPTOR_TYPE_STORAGE_BUFFER,
            2,  // binding 1 for particle positions
            &bufferInfo
        );
        vkUpdateDescriptorSets(device, 1, &writeDescriptorSet, 0, nullptr);
    }
    
    // Update grid texture binding  
    
    VkDescriptorImageInfo storageImageDescriptor{};
    storageImageDescriptor.imageView = gridTexture->getShaderResourceView();
    storageImageDescriptor.sampler = VK_NULL_HANDLE; // Storage images don't use samplers
    storageImageDescriptor.imageLayout = VK_IMAGE_LAYOUT_GENERAL;
    
    VkDescriptorImageInfo storageImageDescriptorSecondary{};
    storageImageDescriptorSecondary.imageView = gridTextureSecondary->getShaderResourceView();
    storageImageDescriptorSecondary.sampler = VK_NULL_HANDLE; // Storage images don't use samplers
    storageImageDescriptorSecondary.imageLayout = VK_IMAGE_LAYOUT_GENERAL;
    
    VkWriteDescriptorSet writeDescriptorSets[] = {
        vks::initializers::writeDescriptorSet(
            m_particleToGridComputeDescriptorSet,
            VK_DESCRIPTOR_TYPE_STORAGE_IMAGE,
            GRID_TEXTURE_BINDING,  // binding 1 for primary grid texture output
            &storageImageDescriptor
        ),
        vks::initializers::writeDescriptorSet(
            m_particleToGridComputeDescriptorSet,
            VK_DESCRIPTOR_TYPE_STORAGE_IMAGE,
            GRID_TEXTURE_SECONDARY_BINDING,  // binding 6 for secondary grid texture output
            &storageImageDescriptorSecondary
        )
    };
    vkUpdateDescriptorSets(device, 2, writeDescriptorSets, 0, nullptr);
    {
        VkDescriptorImageInfo storageImageDescriptor{};
        storageImageDescriptor.imageView = gridTexture->getShaderResourceView();
        storageImageDescriptor.sampler = VK_NULL_HANDLE; // Storage images don't use samplers
        storageImageDescriptor.imageLayout = VK_IMAGE_LAYOUT_GENERAL;

        VkDescriptorImageInfo storageImageDescriptorSecondary{};
        storageImageDescriptorSecondary.imageView = gridTextureSecondary->getShaderResourceView();
        storageImageDescriptorSecondary.sampler = VK_NULL_HANDLE; // Storage images don't use samplers
        storageImageDescriptorSecondary.imageLayout = VK_IMAGE_LAYOUT_GENERAL;

        VkWriteDescriptorSet writeDescriptorSets[] = {
            vks::initializers::writeDescriptorSet(
                m_densityComputeDescriptorSet,
                VK_DESCRIPTOR_TYPE_STORAGE_IMAGE,
                1,  // binding 1 for primary grid texture input
                &storageImageDescriptor
            ),
            vks::initializers::writeDescriptorSet(
                m_densityComputeDescriptorSet,
                VK_DESCRIPTOR_TYPE_STORAGE_IMAGE,
                GRID_TEXTURE_SECONDARY_BINDING,  // binding 6 for secondary grid texture input
                &storageImageDescriptorSecondary
            )
        };
        vkUpdateDescriptorSets(device, 2, writeDescriptorSets, 0, nullptr);

    }
    // Update uniforms with particle count (includes both regular and diffuse particles)
    m_computeParams.numParticles = particles.size();
    if (m_computeUniformBuffer.mapped) {
        memcpy(m_computeUniformBuffer.mapped, &m_computeParams, sizeof(VkFluidVolumeUniforms));
    }
    
 
    vkResetFences(device, 1, &m_computeFence);

    // Record and submit compute commands
    VkCommandBufferBeginInfo cmdBufInfo = vks::initializers::commandBufferBeginInfo();
    vkBeginCommandBuffer(m_particleToGridComputeCommandBuffer, &cmdBufInfo);

    // Clear grid texture at the beginning of each frame to prevent accumulation from previous frames
    VkClearColorValue clearColor = { 0.0f, 0.0f, 0.0f, 0.0f };
    VkImageSubresourceRange clearRange{};
    clearRange.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
    clearRange.baseMipLevel = 0;
    clearRange.levelCount = 1;
    clearRange.baseArrayLayer = 0;
    clearRange.layerCount = 1;
    
    // Clear both grid textures
    vkCmdClearColorImage(m_particleToGridComputeCommandBuffer, 
                       gridTexture->getTextureResource(),
                       VK_IMAGE_LAYOUT_GENERAL, &clearColor, 1, &clearRange);
                       
    vkCmdClearColorImage(m_particleToGridComputeCommandBuffer, 
                       gridTextureSecondary->getTextureResource(),
                       VK_IMAGE_LAYOUT_GENERAL, &clearColor, 1, &clearRange);
    //vkCmdClearColorImage(m_particleToGridComputeCommandBuffer,
    //                   densityTexture->getTextureResource(),
    //    VK_IMAGE_LAYOUT_GENERAL, &clearColor, 1, &clearRange);
    DPINFO(("DEBUG: Cleared both grid textures for new frame (prevents particle accumulation)\n"));
    
    if (0) 
    {
        // Add memory barrier to ensure clear is complete before compute shader, NEED?
        VkMemoryBarrier barrier{};
        barrier.sType = VK_STRUCTURE_TYPE_MEMORY_BARRIER;
        barrier.srcAccessMask = VK_ACCESS_TRANSFER_WRITE_BIT;
        barrier.dstAccessMask = VK_ACCESS_SHADER_READ_BIT | VK_ACCESS_SHADER_WRITE_BIT;
        vkCmdPipelineBarrier(m_particleToGridComputeCommandBuffer,
            VK_PIPELINE_STAGE_TRANSFER_BIT,
            VK_PIPELINE_STAGE_COMPUTE_SHADER_BIT,
            0, 1, &barrier, 0, nullptr, 0, nullptr);
    }

    vkCmdBindPipeline(m_particleToGridComputeCommandBuffer, VK_PIPELINE_BIND_POINT_COMPUTE, m_particleToGridComputePipeline);
    vkCmdBindDescriptorSets(m_particleToGridComputeCommandBuffer, VK_PIPELINE_BIND_POINT_COMPUTE,
                           m_particleToGridPipelineLayout, 0, 1, &m_particleToGridComputeDescriptorSet, 0, nullptr);

    // Dispatch one thread per particle (much more efficient than one thread per grid cell)
    // Local size is 64x1x1, so we need (numParticles + 63) / 64 work groups
    // Each particle (regular or diffuse) will be processed according to its mass value (particle.w)
    uint32_t numParticles = particles.size();
    uint32_t groupCount = (numParticles + 63) / 64;  // 64 = local_size_x from shader
    vkCmdDispatch(m_particleToGridComputeCommandBuffer, groupCount, 1, 1);

   // vks::tools::insertMemoryBarrierWtoR(m_particleToGridComputeCommandBuffer, VK_PIPELINE_STAGE_COMPUTE_SHADER_BIT, VK_PIPELINE_STAGE_COMPUTE_SHADER_BIT);
    

    vkCmdBindPipeline(m_particleToGridComputeCommandBuffer, VK_PIPELINE_BIND_POINT_COMPUTE, m_densityComputePipeline);
    vkCmdBindDescriptorSets(m_particleToGridComputeCommandBuffer, VK_PIPELINE_BIND_POINT_COMPUTE,
                           m_computePipelineLayout, 0, 1, &m_densityComputeDescriptorSet, 0, nullptr);

 
 

    uint32_t groupCountX = (resX + 7) / 8;
    uint32_t groupCountY = (resY + 7) / 8;
    uint32_t groupCountZ = (resZ + 7) / 8;
    vkCmdDispatch(m_particleToGridComputeCommandBuffer, groupCountX, groupCountY, groupCountZ);
    

#if COVARIANCE_COMPUTATION
    vks::tools::insertMemoryBarrierWtoR(m_particleToGridComputeCommandBuffer, VK_PIPELINE_STAGE_COMPUTE_SHADER_BIT, VK_PIPELINE_STAGE_COMPUTE_SHADER_BIT);
#endif


    vkEndCommandBuffer(m_particleToGridComputeCommandBuffer);

    VkSubmitInfo submitInfo = vks::initializers::submitInfo();
    submitInfo.commandBufferCount = 1;
    submitInfo.pCommandBuffers = &m_particleToGridComputeCommandBuffer;

    VkDriver* vkDriver = static_cast<VkDriver*>(Driver);
    vkQueueSubmit(vkDriver->getComputeQueue(), 1, &submitInfo, m_computeFence);
    
 
    // Transition textures back to SHADER_READ_ONLY_OPTIMAL for later sampling
    DPINFO(("DEBUG: Transitioning textures back for shader read\n"));

#if COVARIANCE_COMPUTATION
    if (covarianceTexture) covarianceTexture->transitionLayoutForShaderRead();

#endif

}
 

void MrVkFluid::waitForComputeCompletion()
{
    if (m_computeFence != VK_NULL_HANDLE) {
        VkDevice device = Driver->Device;
        vkWaitForFences(device, 1, &m_computeFence, VK_TRUE, UINT64_MAX);
    }
    //if (gridTexture) gridTexture->transitionLayoutForShaderRead();
//if (gridTextureSecondary) gridTextureSecondary->transitionLayoutForShaderRead();
    VKTEX(m_densityTexture)->transitionLayoutForShaderRead();
}

void MrVkFluid::updateGraphicsDescriptorSetsForShaderRead()
{
    if (m_descriptorSet == VK_NULL_HANDLE) return;
    VkDescriptorImageInfo diInfos[3]{};
    diInfos[0].imageView = VKTEX(m_gridTexture)->getShaderResourceView();
    diInfos[0].sampler = m_defaultSampler;
    diInfos[0].imageLayout = VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL;
    diInfos[1].imageView = VKTEX(m_densityTexture)->getShaderResourceView();
    diInfos[1].sampler = m_defaultSampler;
    diInfos[1].imageLayout = VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL;
#if COVARIANCE_COMPUTATION
	diInfos[2].imageView = VKTEX(m_covarianceTexture)->getShaderResourceView();
	diInfos[2].sampler = m_defaultSampler;
	diInfos[2].imageLayout = VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL;
#endif

    std::vector<VkWriteDescriptorSet> writeDescriptorSets = {
        vks::initializers::writeDescriptorSet(m_descriptorSet,	VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER,	1,	&diInfos[0]),
        vks::initializers::writeDescriptorSet(m_descriptorSet,	VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER,	3,	&diInfos[1]),
#if COVARIANCE_COMPUTATION
        vks::initializers::writeDescriptorSet(m_descriptorSet,	VK_DESCRIPTOR_TYPE_COMBINED_IMAGE_SAMPLER,	5,	&diInfos[2]),
#endif
    }; 
    
    // Update all graphics descriptor sets at once
    if (!writeDescriptorSets.empty()) {
        vkUpdateDescriptorSets(Driver->Device, static_cast<uint32_t>(writeDescriptorSets.size()), 
                              writeDescriptorSets.data(), 0, nullptr);
        DPINFO(("DEBUG: Updated graphics descriptor sets for shader read layout\n"));
    }
    // descriptorInfos vector automatically stays in scope until function ends
}


void MrVkFluid::dispatchCovarianceComputation3D(u32 resX, u32 resY, u32 resZ)
{
#if COVARIANCE_COMPUTATION
    if (m_covarianceComputePipeline == VK_NULL_HANDLE) return;

    // Check if required textures are set (following BufferD pattern)
    if (!m_gridTexture || !m_gridTextureSecondary) {
        DPINFO(("DEBUG:  no   grid texture set\n"));
        return;
    }

    VkDevice device = Driver->Device;

    // Ensure grid texture descriptor is updated for covariance computation
    VkTexture* gridTexture = static_cast<VkTexture*>(m_gridTexture);

    VkDescriptorImageInfo storageImageDescriptor{};
    storageImageDescriptor.imageView = gridTexture->getShaderResourceView();
    storageImageDescriptor.sampler = VK_NULL_HANDLE; // Storage images don't use samplers
    storageImageDescriptor.imageLayout = VK_IMAGE_LAYOUT_GENERAL;

    VkWriteDescriptorSet writeDescriptorSet = vks::initializers::writeDescriptorSet(
        m_covarianceComputeDescriptorSet,
        VK_DESCRIPTOR_TYPE_STORAGE_IMAGE,
        1,  // binding 1 for grid texture input
        &storageImageDescriptor
    );
    vkUpdateDescriptorSets(device, 1, &writeDescriptorSet, 0, nullptr);

    DPINFO(("DEBUG: Updated grid texture for covariance computation (binding 1)\n"));

    // Transition textures to GENERAL layout for storage image access
    DPINFO(("DEBUG: Transitioning textures for covariance computation\n"));

    VkTexture* covarianceTexture = static_cast<VkTexture*>(m_covarianceTexture);

    // Transition storage images to GENERAL layout
    gridTexture->transitionLayoutForStorageAccess();
    covarianceTexture->transitionLayoutForStorageAccess();

    // Wait and reset fence
    vkWaitForFences(device, 1, &m_computeFence, VK_TRUE, UINT64_MAX);
    vkResetFences(device, 1, &m_computeFence);

    // Record and submit compute commands (BufferD SVD computation)
    VkCommandBufferBeginInfo cmdBufInfo = vks::initializers::commandBufferBeginInfo();
    vkBeginCommandBuffer(m_covarianceComputeCommandBuffer, &cmdBufInfo);

    vkCmdBindPipeline(m_covarianceComputeCommandBuffer, VK_PIPELINE_BIND_POINT_COMPUTE, m_covarianceComputePipeline);
    vkCmdBindDescriptorSets(m_covarianceComputeCommandBuffer, VK_PIPELINE_BIND_POINT_COMPUTE,
        m_computePipelineLayout, 0, 1, &m_covarianceComputeDescriptorSet, 0, nullptr);

    // CRITICAL FIX: Add memory barrier to ensure previous compute writes are visible to CovaranceComputation
    VkMemoryBarrier memoryBarrier{};
    memoryBarrier.sType = VK_STRUCTURE_TYPE_MEMORY_BARRIER;
    memoryBarrier.srcAccessMask = VK_ACCESS_SHADER_WRITE_BIT;  // From previous compute stages
    memoryBarrier.dstAccessMask = VK_ACCESS_SHADER_READ_BIT;   // For this CovaranceComputation read

    vkCmdPipelineBarrier(m_covarianceComputeCommandBuffer,
        VK_PIPELINE_STAGE_COMPUTE_SHADER_BIT,  // Previous compute stage 
        VK_PIPELINE_STAGE_COMPUTE_SHADER_BIT,  // This compute stage
        0, 1, &memoryBarrier, 0, nullptr, 0, nullptr);

    // Dispatch with 3D grid mapping (following VkFluidDensity.comp pattern: 8x8x8 local size)
    uint32_t groupCountX = (resX + 7) / 8;
    uint32_t groupCountY = (resY + 7) / 8;
    uint32_t groupCountZ = (resZ + 7) / 8; // Full 3D dispatch for 3D textures
    vkCmdDispatch(m_covarianceComputeCommandBuffer, groupCountX, groupCountY, groupCountZ);

    vkEndCommandBuffer(m_covarianceComputeCommandBuffer);

    VkSubmitInfo submitInfo = vks::initializers::submitInfo();
    submitInfo.commandBufferCount = 1;
    submitInfo.pCommandBuffers = &m_covarianceComputeCommandBuffer;

    VkDriver* vkDriver = static_cast<VkDriver*>(Driver);
    vkQueueSubmit(vkDriver->getComputeQueue(), 1, &submitInfo, m_computeFence);

    // Wait for compute to complete before transitioning back
    vkWaitForFences(device, 1, &m_computeFence, VK_TRUE, UINT64_MAX);

    // Transition textures back to SHADER_READ_ONLY_OPTIMAL for later sampling
    DPINFO(("DEBUG: Transitioning covariance texture back for shader read\n"));
    gridTexture->transitionLayoutForShaderRead();
    covarianceTexture->transitionLayoutForShaderRead();


    DPINFO(("DEBUG: Completed covariance computation dispatch %ux%ux%u\n", resX, resY, resZ));
#endif
}

} // namespace video
} // namespace irr





