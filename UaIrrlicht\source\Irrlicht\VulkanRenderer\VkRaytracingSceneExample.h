#pragma once

#include "IrrCompileConfig.h"
#ifdef _IRR_COMPILE_WITH_VULKAN_

#include "VkHeader.h"
#if VK_ENABLE_RAYTRACING

#include "VkRaytracingDemo.h"

namespace irr {
namespace scene {
    class ISceneManager;
}
namespace video {

// Forward declarations
class VkDriver;

//! Set up a raytracing scene with cube mirror and reflective spheres
//! This function demonstrates how to use SceneManager->addCubeSceneNode() and 
//! SceneManager->addSphereSceneNode() with raytracing
//! \param driver: VkDriver instance with raytracing support
//! \param sceneManager: Scene manager to create nodes in
//! \return true if setup was successful
bool setupRaytracingScene(VkDriver* driver, scene::ISceneManager* sceneManager);

//! Test raytracing with the current scene
//! \param driver: VkDriver instance with raytracing support
//! \param sceneManager: Scene manager containing the scene
//! \return true if test was successful
bool testRaytracingWithScene(VkDriver* driver, scene::ISceneManager* sceneManager);

//! Complete example showing how to set up and test raytracing with scene nodes
//! This is the main function you should call from your application
//! \param driver: VkDriver instance with raytracing support
//! \param sceneManager: Scene manager to create nodes in
void exampleRaytracingSceneUsage(VkDriver* driver, scene::ISceneManager* sceneManager);

} // namespace video
} // namespace irr

#endif // VK_ENABLE_RAYTRACING
#endif // _IRR_COMPILE_WITH_VULKAN_
