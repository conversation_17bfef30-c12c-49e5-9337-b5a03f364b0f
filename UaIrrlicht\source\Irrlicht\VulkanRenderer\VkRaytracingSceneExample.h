#pragma once

#include "IrrCompileConfig.h"
#ifdef _IRR_COMPILE_WITH_VULKAN_

#include "VkHeader.h"
#if VK_ENABLE_RAYTRACING

#include "VkRaytracingDemo.h"

namespace irr {
namespace scene {
    class ISceneManager;
}
namespace video {

// Forward declarations
class VkDriver;

//! NOTE: These functions are not used by the current raytracing implementation.
//! Raytracing geometry is created directly in VkRaytracingDemo::createTestGeometry()
//! These are kept for API compatibility only.

//! Unused - kept for API compatibility
bool setupRaytracingScene(VkDriver* driver, scene::ISceneManager* sceneManager);

//! Unused - kept for API compatibility
void exampleRaytracingSceneUsage(VkDriver* driver, scene::ISceneManager* sceneManager);

} // namespace video
} // namespace irr

#endif // VK_ENABLE_RAYTRACING
#endif // _IRR_COMPILE_WITH_VULKAN_
