#version 460
#extension GL_EXT_ray_tracing : enable  // 启用光线追踪扩展
#extension GL_GOOGLE_include_directive : require  // 启用#include指令

#include "../shared_with_shaders.h"  // 包含共享定义

/**
 * 阴影射线未命中着色器 - 处理阴影射线未命中任何几何体的情况
 * 主要功能:
 * 1. 设置payload距离为-1
 * 2. 表示光源未被遮挡
 */

// 阴影射线payload输入
layout(location = SWS_LOC_SHADOW_RAY) rayPayloadInEXT ShadowRayPayload ShadowRay;

/**
 * 主着色器函数 - 处理阴影射线未命中
 * 设置距离为-1表示光线未被遮挡
 */
void main() {
    ShadowRay.distance = -1.0f;  // -1表示光线未被遮挡
}
