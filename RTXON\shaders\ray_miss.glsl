#version 460
#extension GL_EXT_ray_tracing : enable  // 启用光线追踪扩展
#extension GL_GOOGLE_include_directive : require  // 启用#include指令

#include "../shared_with_shaders.h"  // 包含共享定义

/**
 * 主射线未命中着色器 - 处理射线未命中任何几何体的情况
 * 主要功能:
 * 1. 将射线方向转换为环境贴图UV坐标
 * 2. 采样环境贴图作为背景颜色
 * 3. 设置payload表示未命中
 */

// 环境贴图采样器
layout(set = SWS_ENVS_SET, binding = 0) uniform sampler2D EnvTexture;

// 主射线payload输入
layout(location = SWS_LOC_PRIMARY_RAY) rayPayloadInEXT RayPayload PrimaryRay;

// 数学常量
const float MY_PI = 3.1415926535897932384626433832795;
const float MY_INV_PI  = 1.0 / MY_PI;

/**
 * 将方向向量转换为经纬度UV坐标(用于环境贴图采样)
 * @param dir 归一化的方向向量
 * @return 对应的UV坐标
 */
vec2 DirToLatLong(vec3 dir) {
    // 计算方位角(phi) - 围绕Y轴的角度
    float phi = atan(dir.x, dir.z);
    // 计算极角(theta) - 与Y轴的夹角
    float theta = acos(dir.y);

    // 将角度映射到[0,1]范围的UV坐标:
    // phi: [-π,π] -> [0,1]
    // theta: [0,π] -> [0,1]
    return vec2((MY_PI + phi) * (0.5 / MY_PI), theta * MY_INV_PI);
}

/**
 * 主着色器函数 - 处理未命中情况
 */
void main() {
    // 将世界空间射线方向转换为环境贴图UV坐标
    vec2 uv = DirToLatLong(gl_WorldRayDirectionEXT);
    // 采样环境贴图
    vec3 envColor = textureLod(EnvTexture, uv, 0.0).rgb;
    
    // 设置payload:
    // colorAndDist.rgb: 环境颜色
    // colorAndDist.w: -1表示未命中任何几何体
    PrimaryRay.colorAndDist = vec4(envColor, -1.0);
    // 清空法线和物体ID
    PrimaryRay.normalAndObjId = vec4(0.0);
}
