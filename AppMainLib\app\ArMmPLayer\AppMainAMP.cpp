﻿#include "AppGlobal.h"
#include "AppMainAMP.h"
#include "cppIncDefine.h"
#include "FWManager.h"
#include <VulkanRenderer/VkDriver.h>
#include <vector>
#include <IrrFw/eqv/EQV.h>
#include "irrmmd/irrmmd.h"
#include "irrmmd/CLineGridSceneNode.h"
#include "FFHelper/UaFfmpeg.h"
#include "VulkanRenderer/VkMr2D.h"
#include "stlUtils.h"
#include "CSceneNodeAnimatorCameraTouchControl.h"
#include <filesystem>
#include <glm/gtx/euler_angles.hpp>
#if USE_IMGUI
#include "external/imgui/imgui.h"
#include "VulkanRenderer/base/VulkanUIOverlay.h"
#endif
#if USE_PYTHON
#include "PythonMan.h"
#endif
#include "NetMan.h"
#include "IrrFw/SnPiano.h"
#include "LeapMan.h"
#include "IrrFw/SnWater.h"
#include "irrmmd/CVoxelMeshSceneNode.h"
#include "irrmmd/CInstancedMeshSceneNode.h"
#include "sns/SnVkFuild/SnVkFluid.h"
#if VK_ENABLE_RAYTRACING
#include "VulkanRenderer/VkRaytracingDemo.h" 
#include "VulkanRenderer/VkRaytracingSceneExample.h"
#endif

using namespace ualib;using namespace irr;using namespace irr::core;using namespace irr::video;using namespace irr::scene;using namespace EQVisual;using namespace AppNameSpace;  using namespace saba;
using namespace glm;
irr::video::ITexture* gDomeTex{};
namespace {
#define FW_TEXT  L"赛"//L"ABCDEFGHIJKLMNOPQRSTUVWXYZ#@!$%"//LOVE&PEACE"//L"2"//M"//".  
#define TEST_MMD_ONSTART		1//USE_PHYSX
#define FRAME_STEP_BASE			1 
#define PHYSICS_TIME_MUL		1.f//0.5f//0.75
#define ASYNC_AR  HAS_ARCORE

#define FW_MAX_PARTICLE (IS_WIN? 10240 * 8 *(0?2:MMD_JOYSTICK_GAMECAST?16: HAS_PIANO?32:FW_USE_OIT?128:MMD_CONTROL_SD?8:16):320000)
#define WIN_OUTPUT_FILE  "output.mp4"//webm" // 

//#define LOAD_EQ_BANDLIST "out/shiningStar.json"// 

#define DRAW_BG_VIDEO		1// !IS_WIN
#define RECORD_MIDRT		1		// 不录制 UI
#define PAUSE_VIDEO_PROCESS_WITH_SCENE	1			//需要同步VMD动画时用，但录制时暂停会画面跳跃
#define PAUSE_REC_TOUCHCAMANIM			1

#define START_OFS  0// 
#define SAVE_FRAME_INTVAL  0
#define DRAW_BG_VIDEO_TO_FILE_FRAME_DIV  4*APP_FPS/60  //APP_FPS / n
#define DRAW_BG_IMG			0//HIDE_PIANO

//#define FORCE_LAND_GRID 0
#define SB_INIT_Y			3 //0
#define CAM_FOV 90
#define CAM_INIT_POS_X  0
#define CAM_INIT_POS_Y   (10+(HAS_PIANO && HIDE_PIANO?20:0))*MMD_SABA_SCALE 
#define CAM_BOX_VISUAL	0
#define CAM_PHY_COLLISION      -1 //0x0000FFFF//   -1 //or 0
#define CAMFAR    250 * MMD_SABA_SCALE
#define CAM_BACK_BOX_RB		0
#define CAM1_MIX_RATE	0.01f //0.01f
#define FPVCAM_LOCK_ALL_NEW_SB         1	//跟踪新建角色
#define SKY_DOME				 	1//!IS_WIN_DBG		//IS_WIN
#define SKY_DOME_FLOOD									0		//只画上半球
#define MMD_HAS_SCENE			(!MMD_CONTROL_SD &&	 MA_POLE_DANCE ||	0		 ) 
#define IMG_CUBE_WALL			1
#define VOX_NUM					1
#define HAS_FLUID				0
#define MMD_ZERO_FRICTION		0
#define NUM_ATTACH_WINGS		0
#define SEESAW					0

#define MMD_ADD_FINGER_RBS		1			// *** PERFORMANCE ***
#define HAS_PHY_WALL			0
#define TEST_FW_CUR	0
#define FORCE_LAND_GRID 0
#define OBJ_FLY_TO_MMD_CONN		0
#define MMD_WRITE_MASK 1
#define DEPTH_WHITE_FAR 0
#define HAS_DEPTHONLY_PASS COLMAP_MODEL


#define MMD_HAS_MIC				0
#define MMD_HAS_WHEEL			0
#define MMD_HAS_CLOTH			MMD_JOYSTICK_GAMECAST//SVG_MMD_WRITE//PHYSX GPU
#define MMD_ALL_LOOK_AT_SB0		0
#define AVOID_MISS_HIT			0

#define DYNIMG_TYPE				12 //0 
#define TEST_FW_MATRIX			1
#define BG_COLOR				0//MMD_CONTROL_SD|| 0 ?0xFF000000:0xFF101010//0xFFCCAA88//0xFF000000//0xFF88CCFF
#define IRR_SHADOW_COLOR		0x80080808
#define GROUND_SHADOW			0

#define SYNC_MIDI_FROM "D:/Tmp/!sv/dango.mid"  // output midi with karaoke ass subtitle 
#define SBT_USE_STYLE_ID_ONLY	0
#define SBT_CAM_THROW			1
#define CONN_RB_SIZE 1.5f
#define RECORD_MIDI 1
#define BEAT_TO_JSON  0
#define BASE_CHAR_NUM			0//	3//0 底层sb不同动作时用
#define BASE_CHAR_DY			6

#if BEAT_TO_JSON
	static bool BEAT_TO_JSON_first = true;
	static ualib::UaJsonSetting b2j_jss;
#endif
	const int CAM_DYN_RB =			0			;//altF3
	const float CAM_PHY_SIZE = CAM_DYN_RB ? 2.0f : 1.5f, CAM_RB_MASS = 32.f; 
	bool KickRbCam = 0;
	float LIGHT_FOV = 60;

	const float BULLETTIMEMUL = 1.f/16;

#define PLAY_AUDIO		0
#define PLAY_AUDIO_SYNC 0
#define ACTANIM_BEAT	0
#define VMD_NAME "BassKnight" //"lamb" //"loli"// "maware"//
	std::wstring PLAY_AUDIO_FILE = 0 ? L"D:/Tmp/" VMD_NAME "/1.wav" : L"D:/Tmp/AIMUSIC/lastunion1.mp3";//L"D:/Tmp/AIMUSIC/ganyuAI.mp3";// L"D:/Tmp/aimusic/hohohei2.mp3"; //L"D:/Tmp/AIMUSIC/Shining Star.mp3" ;// bassknight/1.wav";////VisionV22.mp3";//swan/1.wav";//trust1.mp3";//  Echoes.mp3";//moon/m.wav";// "  abab2.mp3";// ababa.mp3";// awawa.mp3";//LoveJF.wav";//ron1.mp3";//sac.wav";//  L"D:\\Tmp\\!sv/girlmad1.wav";// maware.wav";//=senpai\\s.wav";
	const char* MMD_SCENE_FILE = MA_POLE_DANCE ?"D:/MMD/PMX/JQL/qy/wpup.pmx":"D:/MMD/SCENE/bar/1.pmx";// "D:/MMD/SCENE/bbBridge/1.pmx";//stage96Rt90.pmx";//  daxi/1ns.pmx";//pc/1.pmx";//  "D:\\MMD\\SCENE\\qin\\1.pmx";//"d:/mmd/items/trafficLight/1.pmx"; //
	const float MMD_SCENE_SCALE = 1.f;
	const char* MMD_CAM_FILE = 0 ? "D:/Tmp/MarieRose/camera.vmd" : "";//"d:/mmd/vmd/camFront.vmd":"";// "D:/Tmp/" VMD_NAME "/cam.vmd": "";// "G:/MMD/v/MMD Taekwondo Training/Camera.vmd";//"d:/mmd/vmd/daxiCam.vmd"; //"d:/mmd/vmd/xyHoldCam.vmd";// "d:/mmd/vmd/clothJcCam.vmd";//"D:/mmd/vmd/mawareCam.vmd";// "D : \\Tmp\\senpai\\cam3.vmd";// "";//"D: / mmd / vmd / gypStockingCam.vmd";//"D : / Tmp / SeeTinh / cam.vmd";//   "D: / Tmp / SeeTinh / cam.vmd";// "d : / mmd / vmd / triCam.vmd";
#define AUDIO_NAME "Song of Heroes Full - Dense"//Shining Star"//"Song of Heroes Full"//"happy"//
	io::path AI_PH_NAME = ACTANIM_BEAT ? L"d:/Tmp/aimusic/GPT/" AUDIO_NAME ".mp3" : L"";
	std::wstring PLAY_AUDIO_BEAT = ACTANIM_BEAT ? L"d:/Tmp/aimusic/GPT/" AUDIO_NAME ".beat":L"";// VisionV22.btrack"; // Lyrical
	std::wstring SUBTITLE_PATH = L"";// L"D:/Tmp/AIMUSIC/Shining Star.ass";// L"D:/Tmp/!sv/MV_ASS/mawarejp.ass";//dango.ass";//L"D:/Tmp/zdj/ctc/ce.ass"; //bliss.ass"//renai/1.ass"//gok/1.ass"//"qinglian/1.ass"//2/fqlxh.ass"// "SeeTinh/1.ass"// "d:/tmp/2/ai.ass"//(IS_WIN?"d:/tmp/echo/1.ass":"")

#if 0
#define TEST_VIDEO   "data/dummyVideo.mp4"//e:/tmp/!OUTPUT/cs1.mkv"//trSakura.mkv"//treeRoad.mkv"//
#elif 1// force audio: record screen only
#define TEST_VIDEO   "D:/Tmp/aimusic/awawa.mp3"//ShiningStarFull.mp4"//LoveJF.mp4"//"D:/Tmp/aimusic/ron1.mp3"//
#else
#define TEST_VIDEO    "D:/tmp/qinglian/1.mp4"//D:/Tmp/ar/lgrass.mp4"//ygroad.mp4"//"e:/tmp/!output/cs1.mkv"//desk.mp4"//gr1.mp4"//aroutRock.mp4"//Neon Dreams.mp4"//"data/dummyVideo.mp4"//lw.mp4"//"D:/Tmp/aimusic/0.mp4"//vidback/bwx2.mp4"//SeeTinh/2.mp4"
//darling/1.mp4" "data/Action.mkv"//d1.mp4"//sunflw.mp4"//D:/tmp/qinglian/1.mp4"//""data/dummyVideo.mp4"//2ph/2ph.mp4"
//d:/tmp/ar/1/1.mp4"//"s:/tmp/!output/fpvts.mkv"// deskfall.mp4"//"d:/tmp/ar/floor.mp4"// d2.mp4"//  "d:/tmp/2/ub.mp4"//
// "s:/tmp/out.mkv"//"s:/tmp/aro.mp4"//!output/t1.mkv"// fp2.mp4"//
#endif

	std::string PRELOAD_VMD = "";// "data/mmd/stand.vmd";// rideStick2.vmd";

#if USE_IMGUI
	vks::UIOverlay* uiOverlay;
#endif

}
 
#define IS_SIT_VMD  0 
#if defined(MMD_HAS_CLOTH) && MMD_JOYSTICK_GAMECAST
//#define CLOTH_VMD  "d:/mmd/vmd/clothBoard3p.vmd"
#endif
static const char* VMD_FILE[] = { 
 //"d:/mmd/vmd/sitOnTree.vpd", "D:/Tmp/SeeTinh/1.vmd",	"D:\\Tmp\\senpai\\s.vmd",
	//"d:/mmd/vmd/maware.vmd","d:/mmd/vmd/maware1.vmd",
#if IS_SIT_VMD
	"d:/mmd/vmd/facialBaseSitF.vmd",//"d:/mmd/vmd/facialBaseSitFSR.vmd","d:/mmd/vmd/facialBaseSitFSL.vmd",
#else
	//"d:/mmd/vmd/handGrab.vmd",//"d:/mmd/vmd/facialBase.vmd",		
	//	"D:/Tmp/darling/1.vmd",	"D:/Tmp/darling/1.vmd",	"D:/Tmp/darling/1.vmd","D:/Tmp/darling/1.vmd",	9"D:/Tmp/darling/1.vmd",	"D:/Tmp/darling/1.vmd",
#endif
	"d:/mmd/vmd/layFaceDown.vmd","data/mmd/layDA.vmd",
			"d:/mmd/vmd/tr1.vmd","d:/mmd/vmd/tr2.vmd",
	"d:/mmd/vmd/swim1.vmd", "d:/mmd/vmd/footPush.vmd",	"data/mmd/stdDA.vmd",	"d:/mmd/vmd/runcyc1.vmd","d:/mmd/vmd/runcyc1p.vmd","d:/mmd/vmd/runcyc2.vmd","D:/Tmp/2ph/cyc.vmd",
	"D:/mmd/vmd/runFroppy.vmd","D:/mmd/vmd/buildwall.vmd",
	"d:/mmd/vmd/JianMei/1.vmd",
	"D:/Tmp/" VMD_NAME "/1.vmd",//"d:/Tmp/lamb/1.vmd","d:/Tmp/renai/1.vmd",	
	"D:/Tmp/swan/1.vmd","d:/tmp/loli/1.vmd",
	 "D:/mmd/vmd/rest01.vmd",
	"data/mmd/shortVmd/zi.vmd","data/mmd/shortVmd/zi2.vmd",
	//"data/mmd/shortVmd/battou.vmd","data/mmd/shortVmd/flyKick.vmd", "D:/MMD/Poses/R18/sleep/002.vpd",//"data/mmd/shortVmd/ohyear1.vmd","data/mmd/shortVmd/ohyear2.vmd", 
	//"D:/Tmp/moon/1.vmd","D:/MMD/VMD/handRel.vmd",
	//"data/mmd/stdDA.vmd","data/mmd/shouPeng.vmd",
	"D:/Tmp/Victory/1.vmd",
	"D:/Tmp/darling/1.vmd",
	
	
	"d:/mmd/vmd/runNearFarHdUp.vmd","d:/mmd/vmd/runNearFar.vmd",
	//PRELOAD_VMD.c_str(),


	 "d:/mmd/vmd/cunQuan.vmd",	"D:/Tmp/gok/1.vmd","D:\\Tmp\\magicbomb/1.vmd","d:/mmd/vmd/test2.vmd",
		//"D:/Tmp/MarieRose/1.vmd","D:/Tmp/MarieRose/2.vmd",
	 //"d:/mmd/vmd/rina.vmd",
	//	
//"d:/mmd/vmd/qyTail1.vmd",	//"d:/mmd/vmd/poleDance.vmd",11
	"d:/mmd/vmd/balei.vmd",		
	//"d:/mmd/vmd/tc.vmd",// ik disabled
	"d:/mmd/vmd/jump360.vmd","d:/mmd/vmd/diveroll2.vmd",
	
		"d:/tmp/BassKnight/1.vmd",//"d:/mmd/vmd/facialBase.vmd",	 
	"D:/mmd/vmd/dabaji.vmd","d:/mmd/vmd/shaolin.vmd",
	"D:/Tmp/2ph/1.vmd",	"D:/Tmp/gok/1.vmd",	//sing.vmd"//maware.vmd"//stand.vmd",		
	//"G:/MMD/mdr/MMDR Movement Motions-20230614T113828Z-001/MMDR Movement Motions/walking on hands.vmd"
	//flyFrBk.vmd"//crawl.vmd"//arCsTitan.vmd","d:/mmd/vmd/arCsTitan2.vmd"//"d:/mmd/vmd/skyCatch.vmd","d:/mmd/vmd/skyCatch2.vmd","d:/mmd/vmd/skyCatch3.vmd"
	//floorWalk.vmd"//deskjmp2.vmd",//
	//"D:/Tmp/gok/1.vmd",//"D:/Tmp/2ph/gHands.vmd", ///	"d:/mmd/vmd/tak/tak1.vmd","d:/mmd/vmd/tak/tak2.vmd","d:/mmd/vmd/tak/tak3.vmd",

 // "D:\\Tmp\\rir\\rir.vmd","d:/mmd/vmd/stand.vmd","d:/mmd/vmd/selfshot.vmd" 
};// , "d:/mmd/vmd/hang.vmd", "d:/mmd/vmd/hang1.vmd" };//"D:/Tmp/sumo/winner.vmd","D:/Tmp/sumo/loser.vmd",
#define POT_PMX "D:/MMD/items/slide.pmx"//PMX/Y/ganyu/v2/ballP.pmx"//items/pot/potLo.pmx"//test.pmx"//"d:/mmd/items/pot/potr.pmx"//
#define ITEM_PMX  "d:/mmd/PMX/ganyu/v2/joystick2x.pmx"
#define ITEM_PMX1 "D:/MMD/items/pot/box6pl2.pmx"//test.pmx"//
#define ITEM_BOX_OPEN "D:/MMD/items/pot/donut.pmx" //boxOpen.pmx"//test.pmx"//
//#define EYE_BOT 	"D:/MMD/PMX/robot/1.pmx"//eyeline.pmx"	//"D:/MMD/PMX/JQL/ling/1.pmx"//	"D:/MMD/PMX/lxzfix/o.pmx",
#define LEAP_COUNT 2
#define MIDI_START_MMD 0
#define FIRST_SABA //"D:/MMD/Items/yuanshi/20.pmx"//"D:/MMD/Items/1wheel.zip" //
//#define CHILD_SABA_SCALE 0.87f

static const char* pmxfiles[] = {
	//"D:/MMD/PMX/Y/qill2/box.pmx",
#if IS_WIN   
#if IS_VMD_CONVERTER
	"drs.zip",
#endif
#if MMD_HAND_OXR f
	"D:/MMD/pmx/8ch/ik.pmx",	"D:/MMD/pmx/ld/rdhik2.pmx",
#endif  
#if SVG_MMD_WRITE
	//"D:/MMD/PMX/ld/wrt.pmx",	"D:/MMD/PMX/xy/wr.pmx",	//"D:/MMD/PMX/xg/wrt.pmx",//"D:/MMD/PMX/ld/wrt.fpmx",	// // "D:/MMD/PMX/ganyu/gyWrt.pmx",//	//"d:/mmd/pmx/Ai/aiwrt.pmx",
	//"D:/MMD/PMX/Y/linit/wrt.pmx",//	"D:/MMD/PMX/Y/linit/wrtbz.pmx","D:/MMD/PMX/Y/linit/wrtbz.pmx",	//"D:/MMD/PMX/xbr/xbrWrt.pmx",//ld/wrt.pmx"///
#endif 
#if 0					//ht model

		//"D:/MMD/PMX/n/Nerissa_bikini/1.pfmx",	"D:/MMD/PMX/n/!Koyori/3.pmx", "D:/MMD/PMX/n/!Koyori/2.pmx", 
		//"D:/MMD/PMX/n/!Koyori/1.pmx", //"D:/MMD/PMX/n/!Koyori/2.pmx","D:/MMD/PMX/n/!Koyori/1.pmx", "D:/MMD/PMX/n/!Koyori/2.pmx","D:/MMD/PMX/n/!Koyori/1.pmx", "D:/MMD/PMX/n/!Koyori/2.pmx","D:/MMD/PMX/n/!Koyori/1.pmx", "D:/MMD/PMX/n/!Koyori/2.pmx","D:/MMD/PMX/n/!Koyori/1.pmx", "D:/MMD/PMX/n/!Koyori/2.pmx","D:/MMD/PMX/n/!Koyori/1.pmx", "D:/MMD/PMX/n/!Koyori/2.pmx","D:/MMD/PMX/n/!Koyori/1.pmx", "D:/MMD/PMX/n/!Koyori/2.pmx","D:/MMD/PMX/n/!Koyori/1.pmx", "D:/MMD/PMX/n/!Koyori/2.pmx","D:/MMD/PMX/n/!Koyori/1.pmx", "D:/MMD/PMX/n/!Koyori/2.pmx",
			//	"D:/MMD/PMX/n/thickCl/1.pmx", "D:/MMD/PMX/n/thickNavia/1.pmx", 	"D:/MMD/PMX/n/Mavuika R18/1.pmx",
		"D:/MMD/PMX/n/marin/hi/bknTail.pmx","D:/MMD/PMX/n/marin/hi/bny.pmx",
		//"D:/MMD/PMX/n/marin/hi/n.pmx","D:/MMD/PMX/n/marin/mk2/n.pmx", "D:/MMD/PMX/n/marin/hi/jkbfw.pmx", "D:/MMD/PMX/n/marin/hi/jkbELTnc.pmx","D:/MMD/PMX/n/marin/hi/jkbLoShFw.pmx","D:/MMD/PMX/n/marin/hi/jkbLoShFw.pmx",//jkTail.pmx",//"D:/MMD/PMX/n/marin/hi/bknTail.pmx",
#endif 
	"D:/MMD/PMX/JQL/jff/1.pmx","D:/MMD/PMX/JQL/jff/1.pmx","D:/MMD/PMX/JQL/jff/1.pmx","D:/MMD/PMX/JQL/jff/1.pmx","D:/MMD/PMX/JQL/jff/1.pmx","D:/MMD/PMX/JQL/jff/1.pmx","D:/MMD/PMX/JQL/jff/1.pmx","D:/MMD/PMX/JQL/jff/1.pmx","D:/MMD/PMX/JQL/jff/1.pmx","D:/MMD/PMX/JQL/jff/1.pmx","D:/MMD/PMX/JQL/jff/1.pmx","D:/MMD/PMX/JQL/jff/1.pmx","D:/MMD/PMX/JQL/jff/1.pmx","D:/MMD/PMX/JQL/jff/1.pmx","D:/MMD/PMX/JQL/jff/1.pmx","D:/MMD/PMX/JQL/jff/1.pmx","D:/MMD/PMX/JQL/jff/1.pmx",
	//"D:/MMD/PMX/Y/skk/2b.pmx","D:/MMD/PMX/Y/skk/1b.pmx","D:/MMD/PMX/Y/skk/2b.pmx","D:/MMD/PMX/Y/skk/1b.pmx","D:/MMD/PMX/Y/skk/2b.pmx","D:/MMD/PMX/Y/skk/1b.pmx","D:/MMD/PMX/Y/skk/2b.pmx","D:/MMD/PMX/Y/skk/1b.pmx","D:/MMD/PMX/Y/skk/2b.pmx","D:/MMD/PMX/Y/skk/1b.pmx",
	//"D:/MMD/PMX/Y/skk/2.pmx","D:/MMD/PMX/Y/skk/1.pmx","D:/MMD/PMX/Y/ddly/1.pmx",
	
	  //"D:/MMD/PMX/JQL/yx2/1b.pmx",  "D:/MMD/PMX/JQL/yx/1b.pmx", 
	  "D:/MMD/PMX/JQL/yjy/1.pmx","D:/MMD/PMX/JQL/vva/1.pmx",
	   "D:/MMD/PMX/JQL/yx2/1.pmx","D:/MMD/PMX/JQL/pyh/1.pmx","D:/MMD/PMX/JQL/yx/1.pmx",
	  //
	   // "D:/MMD/PMX/JQL/vva/1.pmx", 	"D:/MMD/PMX/JQL/jd/1.pmx","D:/MMD/PMX/JQL/yjy/1.pmx","D:/MMD/PMX/JQL/yfl/1.pmx",
	 //	"D:/MMD/PMX/qqr/strqqr/1.pmx",
	 //
//"D:/MMD/PMX/JQL/zhe/1.pmx","D:/MMD/PMX/JQL/ling/1.pmx",
 //"D:/MMD/PMX/JQL/vva/1.pmx","D:/MMD/PMX/JQL/vva/1.pmx","D:/MMD/PMX/JQL/vva/1.pmx","D:/MMD/PMX/JQL/vva/1.pmx",
	// "D:/MMD/PMX/Y/akf/1.pmx", 	
	//"D:/MMD/PMX/JQL/ling/1.pmx",
	//"D:/MMD/PMX/JQL/vva/1nsn.pmx",
  // "D:/MMD/PMX/JQL/vva/1n.pmx","D:/MMD/PMX/JQL/vva/1nsn.pmx", "D:/MMD/PMX/JQL/vva/1n.pmx", "D:/MMD/PMX/JQL/vva/1n.pmx", "D:/MMD/PMX/JQL/vva/1n.pmx", "D:/MMD/PMX/JQL/vva/1n.pmx", "D:/MMD/PMX/JQL/vva/1n.pmx", "D:/MMD/PMX/JQL/vva/1n.pmx", "D:/MMD/PMX/JQL/vva/1n.pmx", "D:/MMD/PMX/JQL/vva/1n.pmx", "D:/MMD/PMX/JQL/vva/1n.pmx", "D:/MMD/PMX/JQL/vva/1n.pmx", "D:/MMD/PMX/JQL/vva/1n.pmx", "D:/MMD/PMX/JQL/vva/1n.pmx",
	
	"D:/MMD/PMX/JQL/yjy/1.pmx", "D:/MMD/PMX/JQL/vva/1.pmx","D:/MMD/PMX/JQL/yjy/1.pmx", "D:/MMD/PMX/JQL/vva/1.pmx",
	//"D:/MMD/PMX/Y/ganyu/v2/1.pmx", "D:/MMD/PMX/Y/ganyu/1.pmx", "D:/MMD/PMX/JQL/vva/1jRbw.pmx",

 
	//  "D:/MMD/PMX/CB/srs/j.pmx", "D:/MMD/PMX/CB/ftyPt/1.pmx", // "D:/MMD/PMX/CB/ftyPt/1.pmx", "D:/MMD/PMX/CB/srs/1.pmx", "D:/MMD/PMX/CB/ftyPt/1.pmx", "D:/MMD/PMX/CB/srs/1.pmx", "D:/MMD/PMX/CB/ftyPt/1.pmx", "D:/MMD/PMX/CB/srs/1.pmx", "D:/MMD/PMX/CB/ftyPt/1.pmx", "D:/MMD/PMX/CB/srs/1.pmx", "D:/MMD/PMX/CB/ftyPt/1.pmx", "D:/MMD/PMX/CB/srs/1.pmx", "D:/MMD/PMX/CB/ftyPt/1.pmx", "D:/MMD/PMX/CB/srs/1.pmx", "D:/MMD/PMX/CB/ftyPt/1.pmx", "D:/MMD/PMX/CB/srs/1.pmx", "D:/MMD/PMX/CB/ftyPt/1.pmx", "D:/MMD/PMX/CB/srs/1.pmx", "D:/MMD/PMX/CB/ftyPt/1.pmx", "D:/MMD/PMX/CB/srs/1.pmx", "D:/MMD/PMX/CB/ftyPt/1.pmx", "D:/MMD/PMX/CB/srs/1.pmx", "D:/MMD/PMX/CB/ftyPt/1.pmx", "D:/MMD/PMX/CB/srs/1.pmx",
	// "D:/MMD/PMX/CB/wdybkn/1.pmx", "D:/MMD/PMX/CB/bb/1/1.pmx","D:/MMD/PMX/CB/bb/fhx/bkn.pmx",	  "D:/MMD/PMX/CB/bb/2/1.pmx", "D:/MMD/PMX/CB/bb/fhx/hx.pmx",//"D:/MMD/PMX/CB/bb/0/1.pmx",//"D:/MMD/PMX/CB/bb/0/1.pmx","D:/MMD/PMX/CB/bb/0/1.pmx","D:/MMD/PMX/CB/bb/0/1.pmx","D:/MMD/PMX/CB/bb/0/1.pmx","D:/MMD/PMX/CB/bb/0/1.pmx","D:/MMD/PMX/CB/bb/0/1.pmx","D:/MMD/PMX/CB/bb/0/1.pmx","D:/MMD/PMX/CB/bb/0/1.pmx","D:/MMD/PMX/CB/bb/0/1.pmx",
	
	//	 "D:/MMD/PMX/CB/lifu/1.pmx","D:/MMD/PMX/CB/zla/1.pmx",	"D:/MMD/PMX/CB/fn/1.pmx",	"D:/MMD/PMX/CB/wdy/hs.pmx",	"D:/MMD/PMX/CB/wdy/1.pmx",	"D:/MMD/PMX/CB/huiyao/1.pmx","D:/MMD/PMX/CB/wxzs/1.pmx",	//"D:/MMD/PMX/CB/xy/1.pmx",	//"D:/MMD/PMX/CB/n_ych/1.pmx",	
	//  "D:/MMD/PMX/JQL/vva/1jRbw.pmx", //  "D:/MMD/PMX/JQL/vva/1jRbw.pmx", "D:/MMD/PMX/JQL/vva/1jRbw.pmx", "D:/MMD/PMX/JQL/vva/1jRbw.pmx", "D:/MMD/PMX/JQL/vva/1jRbw.pmx", "D:/MMD/PMX/JQL/vva/1jRbw.pmx", 

	//"D:/MMD/PMX/JQL/yjy/1_rbw.pmx","D:/MMD/PMX/JQL/yjy/1_rbw.pmx","D:/MMD/PMX/JQL/yjy/1_rbw.pmx","D:/MMD/PMX/JQL/yjy/1_rbw.pmx","D:/MMD/PMX/JQL/yjy/1_rbw.pmx","D:/MMD/PMX/JQL/yjy/1_rbw.pmx",
	//"D:/MMD/PMX/JQL/yjy2/1_rbw.pmx","D:/MMD/PMX/JQL/yjy2/1_rbw.pmx","D:/MMD/PMX/JQL/yjy2/1_rbw.pmx","D:/MMD/PMX/JQL/yjy2/1_rbw.pmx","D:/MMD/PMX/JQL/yjy2/1_rbw.pmx","D:/MMD/PMX/JQL/yjy2/1_rbw.pmx",

	//"D:/MMD/PMX/JQL/bns/1rbq.pmx",   "D:/MMD/PMX/JQL/bns/1rbq.pmx", "D:/MMD/PMX/JQL/bns/1rbq.pmx",

	//"D:/MMD/PMX/MC/feibi/1.pmx",
	//	"D:/MMD/PMX/ganyubs/1hkFg.pmx",
	//"D:/MMD/PMX/Y/lxzfix/s.pmx","D:/MMD/PMX/Y/qill2/1.pmx", "D:/MMD/PMX/Y/ntMln/1.pmx",  	
	//	 "D:/MMD/PMX/Y/wls/ani.pmx", 	// "D:/MMD/PMX/Y/wls/ani.pmx",	 "D:/MMD/PMX/Y/wls/ani.pmx",	 "D:/MMD/PMX/Y/wls/ani.pmx",	 "D:/MMD/PMX/Y/wls/ani.pmx",	 "D:/MMD/PMX/Y/wls/ani.pmx",	 "D:/MMD/PMX/Y/wls/ani.pmx",	 "D:/MMD/PMX/Y/wls/ani.pmx",	 "D:/MMD/PMX/Y/wls/ani.pmx",	 "D:/MMD/PMX/Y/wls/ani.pmx",	 "D:/MMD/PMX/Y/wls/ani.pmx",	 "D:/MMD/PMX/Y/wls/ani.pmx",	 "D:/MMD/PMX/Y/wls/ani.pmx",	 "D:/MMD/PMX/Y/wls/ani.pmx",	 "D:/MMD/PMX/Y/wls/ani.pmx",	 "D:/MMD/PMX/Y/wls/ani.pmx",	 "D:/MMD/PMX/Y/wls/ani.pmx",	 "D:/MMD/PMX/Y/wls/ani.pmx",	 "D:/MMD/PMX/Y/wls/ani.pmx",	 "D:/MMD/PMX/Y/wls/ani.pmx",
	//"D:/MMD/PMX/Y/wls/1.pmx", "D:/MMD/PMX/Y/mwk/1.pmx","D:/MMD/PMX/Y/puren/1.pmx",
	// "D:/MMD/PMX/Y/xtll/1.pmx",	"D:/MMD/PMX/Y/xinhai/1.pmx",	 "D:/MMD/PMX/Y/ganyu/v2/fg.pmx","D:/MMD/PMX/shenhe/v2/1.pmx","D:/MMD/PMX/Y/ganyu/1.pmx",
	// "D:/MMD/PMX/Y/xtll/b.pmx",	
   //"D:/MMD/PMX/JQL/bkn/ani.pmx",
 //  "D:/MMD/PMX/JQL/dog/fw.pmx",//"D:/MMD/PMX/Y/mjy/1.pmx",		
	//"D:/MMD/PMX/JQL/nike2/1.pmx",
	//"D:/MMD/PMX/JQL/bns/1rbq.pmx",
 
	"D:/MMD/PMX/JQL/yjy/1.pmx","D:/MMD/PMX/JQL/yfl/1.pmx", "D:/MMD/PMX/JQL/yjy2/1.pmx",// "D:/MMD/PMX/JQL/yjy/1.pmx","D:/MMD/PMX/JQL/yjy2/1.pmx","D:/MMD/PMX/JQL/yjy/1.pmx","D:/MMD/PMX/JQL/yjy2/1.pmx","D:/MMD/PMX/JQL/yjy/1.pmx","D:/MMD/PMX/JQL/yjy2/1.pmx", "D:/MMD/PMX/JQL/yjy/1.pmx","D:/MMD/PMX/JQL/yjy2/1.pmx","D:/MMD/PMX/JQL/yjy/1.pmx","D:/MMD/PMX/JQL/yjy2/1.pmx",
	"D:/MMD/PMX/JQL/nike2/1.pmx", 	
		// "D:/MMD/PMX/JQL/bns/1rbq.pmx", "D:/MMD/PMX/JQL/bns/1rbq.pmx", "D:/MMD/PMX/JQL/bns/1rbq.pmx",
	"D:/MMD/PMX/JQL/xjy/1.pmx","D:/MMD/PMX/JQL/ycl/1.pmx", 	//"D:/MMD/PMX/JQL/yclM/1.pmx", "D:/MMD/PMX/JQL/KELIN/longSkt.pmx",	
	 "D:/MMD/PMX/JQL/Rina/1.pmx","D:/MMD/PMX/JQL/KELIN/rbw.pmx","D:/MMD/PMX/JQL/luxi/1.pmx",
	 
	
	//"D:/MMD/PMX/Y/ddly/1.pmx","D:/MMD/PMX/Y/fywy/1.pmx","D:/MMD/PMX/Y/bnt/1.pmx", "D:/MMD/PMX/Y/xl_hdj/1.pmx",
	// 	"D:/MMD/PMX/XQ/dht/1.pmx",
	  
	//	 "D:/MMD/PMX/JQL/xjy/1d.pmx",
		  //"D:/MMD/PMX/JQL/bl/1.pmx",
		"D:/MMD/PMX/JQL/ycl/1.pmx","D:/MMD/PMX/JQL/nike/1.pmx",//"D:/MMD/PMX/JQL/cj/1.pmx",  "D:/MMD/PMX/JQL/bl/1.pmx", 
 
	  //"D:/MMD/PMX/JQL/xjy/1d.pmx",
	//"D:/MMD/PMX/Y/lxzYin/hy/1.pmx", "D:/MMD/PMX/Y/lxzfix/s.pmx", 
 

 
  
   
	  "D:/MMD/PMX/JQL/ksj/1.pmx","D:/MMD/PMX/JQL/gls/1.pmx",   "D:/MMD/PMX/JQL/ling/1.pmx", 
		//"D:/MMD/PMX/JQL/jd/mod1.pmx",
 "D:/MMD/Blender/New Folder/jql_bns/bkn.pmx","D:/MMD/PMX/JQL/bns/1rbqup.pmx", "D:/MMD/PMX/JQL/bns/1rbq.pmx",
//"D:/MMD/PMX/JQL/bns/1rbqup.pmx",	
"D:/MMD/Blender/New Folder/jql_bns/hs.pmx",		

	"D:/MMD/Blender/New Folder/jql_bns/hs.pmx",			//"D:/MMD/PMX/Y/qll/m1.pmx", //	"D:/MMD/PMX/Y/qll/m1.pmx",	"D:/MMD/PMX/Y/qll/m1.pmx",	"D:/MMD/PMX/Y/qll/m1.pmx",	"D:/MMD/PMX/Y/qll/m1.pmx",	"D:/MMD/PMX/Y/qll/m1.pmx",	"D:/MMD/PMX/Y/qll/m1.pmx",	"D:/MMD/PMX/Y/qll/m1.pmx",	"D:/MMD/PMX/Y/qll/m1.pmx",	"D:/MMD/PMX/Y/qll/m1.pmx",	"D:/MMD/PMX/Y/qll/m1.pmx",
	"D:/MMD/PMX/JQL/ksj/1.pmx",	"D:/MMD/PMX/JQL/bns/1.pmx","D:/MMD/PMX/JQL/bns/1rbqup.pmx","D:/MMD/Blender/New Folder/jql_bns/hs.pmx",
"D:/MMD/Blender/New Folder/jql_bns/hs.pmx",		  "D:/MMD/Blender/New Folder/jql_bns/2.pmx",		"D:/MMD/PMX/JQL/bns/1rbqup.pmx",

	  "D:/MMD/Blender/New Folder/jql_bns/2.pmx",		  "D:/MMD/Blender/New Folder/jql_bns/2.pmx",	  "D:/MMD/Blender/New Folder/jql_bns/2.pmx",	  "D:/MMD/Blender/New Folder/jql_bns/2.pmx",	  "D:/MMD/Blender/New Folder/jql_bns/2.pmx",	  "D:/MMD/Blender/New Folder/jql_bns/2.pmx",	  "D:/MMD/Blender/New Folder/jql_bns/2.pmx",	  "D:/MMD/Blender/New Folder/jql_bns/2.pmx",	  "D:/MMD/Blender/New Folder/jql_bns/2.pmx",	  "D:/MMD/Blender/New Folder/jql_bns/2.pmx",	  "D:/MMD/Blender/New Folder/jql_bns/2.pmx",	  "D:/MMD/Blender/New Folder/jql_bns/2.pmx",
		"D:/MMD/PMX/JQL/bns/1rbqup.pmx","D:/MMD/PMX/JQL/bns/1rb.pmx","D:/MMD/Blender/New Folder/jql_bns/wf.pmx", 		"D:/MMD/Blender/New Folder/jql_bns/wfWeighted.pmx",	"D:/MMD/Blender/New Folder/jql_bns/wfWeighted.pmx",	"D:/MMD/Blender/New Folder/jql_bns/wfWeighted.pmx",	"D:/MMD/Blender/New Folder/jql_bns/wfWeighted.pmx",	"D:/MMD/Blender/New Folder/jql_bns/wfWeighted.pmx",	"D:/MMD/Blender/New Folder/jql_bns/wfWeighted.pmx",	"D:/MMD/Blender/New Folder/jql_bns/wfWeighted.pmx",	"D:/MMD/Blender/New Folder/jql_bns/wfWeighted.pmx",	"D:/MMD/Blender/New Folder/jql_bns/wfWeighted.pmx",	"D:/MMD/Blender/New Folder/jql_bns/wfWeighted.pmx",	"D:/MMD/Blender/New Folder/jql_bns/wfWeighted.pmx",	"D:/MMD/Blender/New Folder/jql_bns/wfWeighted.pmx",	"D:/MMD/Blender/New Folder/jql_bns/wfWeighted.pmx",	"D:/MMD/Blender/New Folder/jql_bns/wfWeighted.pmx",	"D:/MMD/Blender/New Folder/jql_bns/wfWeighted.pmx",
//"D:/MMD/Blender/New Folder/jql_bns/tmp.pmx","D:/MMD/Blender/New Folder/jql_bns/fpx.pmx","D:/MMD/Blender/New Folder/jql_bns/wf.pmxOUT1pt.pmx","D:/MMD/Blender/New Folder/jql_bns/wf.pmxOUTold.pmx","D:/MMD/Blender/New Folder/jql_bns/wf.pmx",
	//"D:/MMD/PMX/JQL/alj/1.pmx",	"D:/MMD/PMX/Ryuko/2/1.pmx","D:/MMD/PMX/Ryuko/2/1.pmx","D:/MMD/PMX/Ryuko/2/1.pmx","D:/MMD/PMX/Ryuko/2/1.pmx","D:/MMD/PMX/Ryuko/2/1.pmx","D:/MMD/PMX/Ryuko/2/1.pmx","D:/MMD/PMX/Ryuko/2/1.pmx","D:/MMD/PMX/Ryuko/2/1.pmx","D:/MMD/PMX/Ryuko/2/1.pmx","D:/MMD/PMX/Ryuko/2/1.pmx","D:/MMD/PMX/Ryuko/2/1.pmx","D:/MMD/PMX/Ryuko/2/1.pmx","D:/MMD/PMX/Ryuko/2/1.pmx","D:/MMD/PMX/Ryuko/2/1.pmx","D:/MMD/PMX/Ryuko/2/1.pmx","D:/MMD/PMX/Ryuko/2/1.pmx",
	//	"D:/MMD/PMX/2b/1.pmx","D:/MMD/PMX/2b/1.pmx","D:/MMD/PMX/2b/1.pmx","D:/MMD/PMX/2b/1.pmx","D:/MMD/PMX/2b/1.pmx","D:/MMD/PMX/2b/1.pmx","D:/MMD/PMX/2b/1.pmx","D:/MMD/PMX/2b/1.pmx","D:/MMD/PMX/2b/1.pmx","D:/MMD/PMX/2b/1.pmx","D:/MMD/PMX/2b/1.pmx","D:/MMD/PMX/2b/1.pmx","D:/MMD/PMX/2b/1.pmx","D:/MMD/PMX/2b/1.pmx","D:/MMD/PMX/2b/1.pmx","D:/MMD/PMX/2b/1.pmx",
	//	"D:/MMD/PMX/Y/paimonST/1.pmx",	//"D:/MMD/PMX/Y/lxzfix/s.pmx",//"D:/MMD/PMX/Y/lxzKong/AetherJB.pmx",
	// 	//"D:/MMD/PMX/Y/xnn/fg.pmx",	
	//"D:/MMD/PMX/Y/ganyu/v2/rb.pmx",//"D:/MMD/PMX/Y/ganyu/v2/rbBrumaS.pmx",	// //"D:/MMD/PMX/ganyubs/fw.pmx",
 	//"D:/MMD/PMX/n/2B/fg.Pmx",	"D:/MMD/PMX/2b/1.pmx",		"D:/MMD/PMX/2b/2.pmx","D:/MMD/PMX/n/2B/asedt.Pmx",	
	//"D:/MMD/PMX/Y/ntMln/1.pmx",	"D:/MMD/PMX/Y/qill2/1.pmx","D:/MMD/PMX/Y/qll/1.pmx",
	//"D:/MMD/PMX/Y/ntKqn/1.pmx",	"D:/MMD/PMX/Y/qill2/1.pmx","D:/MMD/PMX/Y/ntjnq/1.pmx",
	//"D:/MMD/PMX/Y/fnn/flw/1.pmx",
	//"D:/MMD/PMX/JQL/bns/1i2.pmx","D:/MMD/PMX/JQL/bns/1i2.pmx",
	"D:/MMD/PMX/JQL/bns/1rbqup.pmx","D:/MMD/PMX/JQL/bns/1rb.pmx",	"D:/MMD/PMX/JQL/bns/1rbqup.pmx","D:/MMD/PMX/JQL/bns/1rb.pmx",	"D:/MMD/PMX/JQL/bns/1rbqup.pmx","D:/MMD/PMX/JQL/bns/1rb.pmx",	"D:/MMD/PMX/JQL/bns/1rbqup.pmx","D:/MMD/PMX/JQL/bns/1rb.pmx",	"D:/MMD/PMX/JQL/bns/1rbqup.pmx","D:/MMD/PMX/JQL/bns/1rb.pmx",	"D:/MMD/PMX/JQL/bns/1rbqup.pmx","D:/MMD/PMX/JQL/bns/1rb.pmx",	"D:/MMD/PMX/JQL/bns/1rbqup.pmx","D:/MMD/PMX/JQL/bns/1rb.pmx",	"D:/MMD/PMX/JQL/bns/1rbqup.pmx","D:/MMD/PMX/JQL/bns/1rb.pmx",	"D:/MMD/PMX/JQL/bns/1rbqup.pmx","D:/MMD/PMX/JQL/bns/1rb.pmx",	"D:/MMD/PMX/JQL/bns/1rbqup.pmx","D:/MMD/PMX/JQL/bns/1rb.pmx",
	"D:/MMD/PMX/JQL/bns/1.pmx","D:/MMD/PMX/JQL/bns/1.pmx","D:/MMD/PMX/JQL/bns/1.pmx","D:/MMD/PMX/JQL/bns/1.pmx","D:/MMD/PMX/JQL/bns/1.pmx","D:/MMD/PMX/JQL/bns/1.pmx","D:/MMD/PMX/JQL/bns/1.pmx","D:/MMD/PMX/JQL/bns/1.pmx","D:/MMD/PMX/JQL/bns/1.pmx","D:/MMD/PMX/JQL/bns/1.pmx","D:/MMD/PMX/JQL/bns/1.pmx","D:/MMD/PMX/JQL/bns/1.pmx","D:/MMD/PMX/JQL/bns/1.pmx",	"D:/MMD/PMX/JQL/bns/1.pmx","D:/MMD/PMX/JQL/bns/1.pmx","D:/MMD/PMX/JQL/bns/1.pmx","D:/MMD/PMX/JQL/bns/1.pmx","D:/MMD/PMX/JQL/bns/1.pmx","D:/MMD/PMX/JQL/bns/1.pmx","D:/MMD/PMX/JQL/bns/1.pmx","D:/MMD/PMX/JQL/bns/1.pmx","D:/MMD/PMX/JQL/bns/1.pmx","D:/MMD/PMX/JQL/bns/1.pmx","D:/MMD/PMX/JQL/bns/1.pmx","D:/MMD/PMX/JQL/bns/1.pmx","D:/MMD/PMX/JQL/bns/1.pmx","D:/MMD/PMX/JQL/bns/1.pmx",
	"D:/MMD/PMX/JQL/bns/1rbq.pmx",//"D:/MMD/PMX/JQL/bns/1rbq.pmx","D:/MMD/PMX/JQL/bns/1rbq.pmx","D:/MMD/PMX/JQL/bns/1rbq.pmx","D:/MMD/PMX/JQL/bns/1rbq.pmx","D:/MMD/PMX/JQL/bns/1rbq.pmx","D:/MMD/PMX/JQL/bns/1rbq.pmx","D:/MMD/PMX/JQL/bns/1rbq.pmx","D:/MMD/PMX/JQL/bns/1rbq.pmx","D:/MMD/PMX/JQL/bns/1rbq.pmx","D:/MMD/PMX/JQL/bns/1rbq.pmx","D:/MMD/PMX/JQL/bns/1rbq.pmx","D:/MMD/PMX/JQL/bns/1rbq.pmx","D:/MMD/PMX/JQL/bns/1rbq.pmx","D:/MMD/PMX/JQL/bns/1rbq.pmx","D:/MMD/PMX/JQL/bns/1rbq.pmx","D:/MMD/PMX/JQL/bns/1rbq.pmx","D:/MMD/PMX/JQL/bns/1rbq.pmx","D:/MMD/PMX/JQL/bns/1rbq.pmx","D:/MMD/PMX/JQL/bns/1rbq.pmx","D:/MMD/PMX/JQL/bns/1rbq.pmx",
	"D:/MMD/PMX/JQL/ksj/1.pmx",//  "D:/MMD/PMX/JQL/ksj/1.pmx",//"D:/MMD/PMX/JQL/ksj/1.pmx","D:/MMD/PMX/JQL/ksj/1.pmx","D:/MMD/PMX/JQL/ksj/1.pmx","D:/MMD/PMX/JQL/ksj/1.pmx","D:/MMD/PMX/JQL/ksj/1.pmx","D:/MMD/PMX/JQL/ksj/1.pmx","D:/MMD/PMX/JQL/ksj/1.pmx","D:/MMD/PMX/JQL/ksj/1.pmx",
	//"D:/MMD/PMX/JQL/ksj/1.pmx","D:/MMD/PMX/JQL/ksj/1.pmx","D:/MMD/PMX/JQL/ksj/1.pmx","D:/MMD/PMX/JQL/ksj/1.pmx","D:/MMD/PMX/JQL/ksj/1.pmx","D:/MMD/PMX/JQL/ksj/1.pmx","D:/MMD/PMX/JQL/ksj/1.pmx","D:/MMD/PMX/JQL/ksj/1.pmx","D:/MMD/PMX/JQL/ksj/1.pmx","D:/MMD/PMX/JQL/ksj/1.pmx",
	"D:/MMD/PMX/JQL/jd/1.pmx","D:/MMD/PMX/JQL/pai/1.pmx","D:/MMD/PMX/JQL/luxi/1.pmx",
		 "D:/MMD/PMX/JQL/qy/1.pmx", "D:/MMD/PMX/JQL/cat/fw.pmx",//"D:/MMD/PMX/JQL/ss/1.pmx",
		
	//"D:/MMD/PMX/JQL/zhy/1up.pmx",
	"D:/MMD/PMX/JQL/dog/noeyem.pmx",	 "D:/MMD/PMX/JQL/bl/tere.pmx",  
	
	//"D:/MMD/PMX/JQL/zhy/1.pmx",//		"D:/MMD/PMX/JQL/dog/rbw.pmx",
	"D:/MMD/PMX/JQL/Rina/1.pmx",  //"D:/MMD/PMX/JQL/Rina/Qsame.pmx","D:/MMD/PMX/JQL/Rina/Qsame.pmx","D:/MMD/PMX/JQL/Rina/Qsame.pmx",
	"D:/MMD/PMX/JQL/11/1.pmx", 
	//"D:/MMD/PMX/JQL/qy/2.pmx",
	"D:/MMD/PMX/JQL/KELIN/rbw.pmx", "D:/MMD/PMX/JQL/nike/1.pmx",	 "D:/MMD/PMX/JQL/nike/1.pmx","D:/MMD/PMX/JQL/nike/1.pmx","D:/MMD/PMX/JQL/nike/1.pmx","D:/MMD/PMX/JQL/nike/1.pmx","D:/MMD/PMX/JQL/nike/1.pmx",
	"D:/MMD/PMX/JQL/ben/1.pmx",//"D:/MMD/PMX/JQL/zhe/1.pmx",
	
	//"D:/MMD/PMX/games/Princess/1.pmx"
	"D:/MMD/PMX/ld/hi.pmx",//"D:/MMD/PMX/lxzYin/hy/1.pmx",//

	
	//"D:/MMD/PMX/keluo/1.pmx",
	"D:/MMD/PMX/sparkle/jio/fw.pmx",
	
	"D:/MMD/PMX/n/Hifumi/1.pmx","D:/MMD/PMX/n/Hifumi/2.pmx",
		//"D:/MMD/VROID/t/ln2/2/1.pmx","D:/MMD/VROID/t/ln2/1/2.pmx","D:/MMD/VROID/t/ln2/1/1.pmx","D:/MMD/VROID/t/ln2/1/1.pmx",//"D:/MMD/VROID/sd/sd0.pmx",
 
	//	"D:/MMD/PMX/sparkle/jio/ik.pmx",//"D:/MMD/PMX/sparkle/jio/fwDnJio.pmx",
	"D:/MMD/PMX/sparkle/jio/fw.pmx", 
	
	 //"D:/MMD/PMX/Y/linit/1.pmx",//"D:/MMD/PMX/liuying/1/1.pmx",//"D:/MMD/PMX/liuying/swim/1.pmx",	
	
	//"D:/MMD/PMX/liuying/jio/fw.pmx",
	 
	//"D:/MMD/PMX/xinhai/2.pmx",
	//
	//"D:/MMD/PMX/ganyu/v2/rbBrmFW.pmx",
	//"d:/MMD/PMX/angela/1.pmx","d:/MMD/PMX/angela/1.pmx",//"d:/MMD/PMX/angela/1n.pmx","d:/MMD/PMX/angela/1nO.pmx",//	
	"D:/MMD/PMX/xy/fg.pmx",
	"D:/MMD/PMX/qianzhi/1.pmx", 
//"D:/MMD/PMX/games/zhezhi/1.pmx","D:/MMD/PMX/games/chl/fw.pmx",	//"D:/MMD/PMX/games/jinxi/1.pmx",

// "D:/MMD/PMX/Y/linit/1.pmx","D:/MMD/PMX/Y/linit2/1.pmx",//	"D:/MMD/PMX/nilu/s.pmx","D:/MMD/PMX/navia/1hk.pmx",
 
	//"D:/MMD/PMX/baizhu/1.pmx",
	//	 "d:/mmd/PMX/mikuA/1c.pmx",//"D:/MMD/PMX/n/MikuV4X/1.pmx",
 //	"D:/MMD/PMX/angela/1nf.pmx",

	//"C:\\koikatsu_model\\Namikata Hizuru_1699\\1.pmx",
	// "D:/MMD/PMX/Setsuna/sl/1.pmx",  "D:/MMD/PMX/Setsuna/ss/1s.pmx","D:/MMD/PMX/Setsuna/ss/1t.pmx",  "D:/MMD/PMX/Setsuna/ss/1tn.pmx","D:/MMD/PMX/Setsuna/ss/1sn.pmx", "D:/MMD/PMX/Setsuna/ss/1n.pmx",
	// //  "D:/MMD/PMX/zz/bareFoot.pmx",  "D:/MMD/PMX/zz/bareFoot.pmx",  "D:/MMD/PMX/zz/bareFoot.pmx",  "D:/MMD/PMX/zz/bareFoot.pmx",

	//"D:/MMD/PMX/furina/an2.pmx","D:/MMD/PMX/furina/an1.pmx",
	//"D:/MMD/PMX/ld/rdhrbtest.pmx", 	
	//"D:/MMD/PMX/ganyu/1hk.pmx", // 
	// "d:/mmd/pmx/sl2/1hk.pmx",	
	// 
	//"D:/MMD/zip/ganyuvfe",	//ERRORf
	//"D:/MMD/PMX/sl3/1.pmx",
	//"D:/MMD/PMX/furina/1.pmx", "D:/MMD/PMX/furina/2.pmx",//"D:/MMD/PMX/furina/1hk.pmx",
	//"D:/MMD/PMX/rds/2.pmx","D:/MMD/PMX/rds/1.pmx",//"D:/MMD/PMX/xq/rm/1hkFg.pmx","D:/MMD/PMX/xq/rm/1hkGhFg.pmx","D:/MMD/PMX/xq/rm/1hkFg.pmx","D:/MMD/PMX/xq/ht/1.pmx","D:/MMD/PMX/xq/ht/1.pmx","D:/MMD/PMX/xq/ht/1.pmx","D:/MMD/PMX/xq/ht/1.pmx","D:/MMD/PMX/xq/ht/1.pmx","D:/MMD/PMX/xq/ht/1.pmx","D:/MMD/PMX/xq/ht/1.pmx","D:/MMD/PMX/xq/ht/1.pmx",

	//"D:/MMD/PMX/SQ/tll/1.pmx","D:/MMD/PMX/SQ/kllk/1hk.pmx","D:/MMD/PMX/SQ/q9/1hk.pmx", "D:/MMD/PMX/SQ/kllk2/1.pmx","D:/MMD/PMX/SQ/mxng/1.pmx",
	//"D:/MMD/PMX/ganyu/vf.pmx",

	//"D:/MMD/PMX/n/Mika_Set_Mark2_1.0/Mika_Tracksuit_Mark2_1.0.pmx",	"D:/MMD/PMX/n/Chisato&Takina_Set_Hi_1.01/Takina_Negligee_Hi_1.01.pmx",
	//"D:/MMD/PMX/ailixiya/hik.pmx", //"D:/MMD/PMX/ailixiya/1.pmx", "D:/MMD/PMX/gelei/hik.pmx",				
	//"D:/MMD/PMX/n/mark2/bfk.pmx", "D:/MMD/PMX/n/1.01/1.pmx",
	// "D:/MMD/n/boobs/1.pmx", 		//"D:/MMD/PMX/drs/1N.pmx",	
	
	//	"D:/MMD/n/vGanyu/n.pmx",
	//"d:/mmd/n/n_Fiona/1.pmx",	 "d:/mmd/n/n_Hitomi/1.pmx",
	//"D:/MMD/PMX/zz/bareFoot.pmx",	"D:/MMD/PMX/zz/stocking.pmx",
	//	"D:/MMD/PMX/ganyu/v2/rbEye.pmx",	"D:/MMD/PMX/ganyu/v2/rbEyeM.pmx",	
	//		"d:/MMD/PMX/wenti/1.pmx",		"d:/MMD/PMX/wenti/1.pmx",		"d:/MMD/PMX/wenti/1.pmx",		"d:/MMD/PMX/wenti/1.pmx",		"d:/MMD/PMX/wenti/1.pmx",
	
	//"D:/MMD/PMX/shenhe/s.pmx",
	"D:/MMD/PMX/8ch/rb.pmx",	
			"D:/MMD/PMX/youla/s.pmx",	
	"D:/MMD/PMX/ganyu/1.pmx",
	"d:/mmd/pmx/Ai/Akane.pmx",//			
	 "D:/MMD/PMX/furina/2.pmx",//"D:/MMD/PMX/furina/an3.pmx","D:/MMD/PMX/furina/2.pmx",
	// "D:/MMD/PMX/xinhai1/1.pmx",	
	//	"D:/MMD/PMX/ganyu/gywrt.pmx",		
	//"d:/mmd/pmx/Ai/ai1.pmx",
	// "d:/mmd/pmx/svWolf/s.pmx", 
	//"D:/MMD/PMX/ganyuxc/gy1.pmx",	
		"D:/MMD/PMX/zz/stocking.pmx",
	"D:/MMD/PMX/shenhe/shenheVfe.pmx","d:/mmd/zip/gs_yy.zip",
	"D:/MMD/Items/yuanshi/20chr.pmx",//	"D:/MMD/Items/yuanshi/20s.pmx",
	//	"d:/tmp/",	 	
	//"d:/tmp/ar/zzHandIk.zip",
//  "d:/mmd/n/Fiona Nude",		  // "d:/mmd/n/MisakiNude.zip",	
//	"d:/mmd/pmx/nakano/5",//"C:\\koikatsu_model\\it",
 // "D:/MMD/n/Fiona Nude",
//"d:/mmd/n/n_Hitomi",//"d:/mmd/n/n_Hitomi","d:/mmd/n/n_Hitomi","d:/mmd/n/n_Hitomi","d:/mmd/n/n_Hitomi","d:/mmd/n/n_Hitomi",
// "d:/mmd/n/n_Hitomi Nude Remix.zip",				  "d:/mmd/n/n_Hitomi Nude Remix.zip",	  "d:/mmd/n/n_Hitomi Nude RemixS.zip", "d:/mmd/n/n_Hitomi Nude RemixS.zip",	  "d:/mmd/n/n_Hitomi Nude RemixS.zip",
// "d:/mmd/n/Nagisa/",	"d:/mmd/n/Nagisa/","d:/mmd/n/Nagisa/","d:/mmd/n/Nagisa/","d:/mmd/n/Nagisa/",
"D:/MMD/pmx/swimLd/sfw.pmx",// 
//	"d:/mmd/zip/miyaN.zip",// 
//"D:/MMD/n/boobs/hbNoBra.zip",//"D:/MMD/n/boobs/hbNude.zip",//"D:/MMD/n/boobs/hb.zip",
	//"D:/MMD/n/Mermaid",	
//"D:/MMD/Items/chair.zip",//"D:/MMD/Items/1wheel.zip",
// "D:/MMD/n/man",//"D:/MMD/n/misaki",
//"D:/MMD/2kkout/catn",		// "D:/MMD/2kkout/cat",// "D:/MMD/n/bbs", //"D:/MMD/n/boobs/models","D:/MMD/n/boobs/models",
//"D:/MMD/n/bdgGanyu.zip",	"D:\\MMD\\M\\TdaRin_Bikini_TypeD",  //"D:/MMD/n/bknGanyu2/gy.pmx",//	"D:/MMD/n/vGanyu.zip","D:/MMD/n/vGanyuHB.zip",//	"D:/MMD/n/bknShenhehb.zip","D:/MMD/n/hbShenfe.zip",	//"D:/MMD/n/bknRin.zip",
//"D:/MMD/n/boobs/nbSwim.zip","D:/MMD/n/boobs/bbNoBra.zip","D:/MMD/n/boobs/bbNoBraIK.zip",	//"d:/mmd/n/Fiona Nude/hands.pmx", 
//"D:/MMD/PMX/miya/1n.pmx", //"D:/MMD/PMX/miya/1n.pmx",// 
// "d:/mmd/pmx/shenli/sl.pmx",//
//"D:/MMD/PMX/lsk/s.pmx", //"D:/MMD/PMX/bb/bbL.pmx",
//"D:/MMD/pmx/youla/s.pmx",//"D:/MMD/pmx/ld/fix.pmx", //"D:/MMD/pmx/8ch/s.pmx",//	//// 	 "D:/MMD/pmx/kq/kq.pmx","D:/MMD/pmx/keli/s.pmx",////"D:/MMD/PMX/ganyu/spf.pmx",	

#else
	Ctx->getDataFilePath("mmd/gs_NilouIK.zip").c_strA(),
#endif
};

static const char* pmxEqvCharfiles[] = {
	//"D:/MMD/PMX/Setsuna/ss/srm.pmx", 	"D:/MMD/PMX/Setsuna/sln/1.pmx",  "D:/MMD/PMX/Setsuna/ssn/1.pmx", 
	///"D:/MMD/PMX/Y/xtll/toy.pmx",
#if 0
	//"D:/MMD/ITEMS/wheel/dunFlw.pmx","D:/MMD/ITEMS/wheel/dunFlw1.pmx","D:/MMD/ITEMS/wheel/dunFlw2.pmx",
	 "D:/MMD/PMX/JQL/vva/1nsn.pmx", //"D:/MMD/PMX/JQL/vva/jet.pmx",
#elif 0
	"D:/MMD/PMX/Y/akf/swj.pmx",
	//"D:/MMD/ITEMS/coins/coinWBG1.pmx",
	
	//"data/mmd/model/brick_wall_model.pmx",//
	//"data/mmd/model/tail.pmx",//f
	//"data/mmd/model/ropeLeafYao.pmx",//
	//"D:/MMD/ITEMS/Petal/leafFlower.pmx",//"D:/MMD/ITEMS/Petal/ropeLeaf1.pmx", "data/mmd/model/ropeGold.pmx", //"data/mmd/model/rope_model.pmx",
#else
	"D:/MMD/PMX/Y/ddly/1.pmx",
	//"D:/MMD/ITEMS/0/bubble.pmx",//"D:/MMD/ITEMS/0/moon.pmx",//  "data/mmd/model/ropeHeavy.pmx",//"D:/MMD/PMX/Y/akf/tail.pmx",
	// "D:/MMD/ITEMS/DRESS/Random Stuff/wings/angelWingsOIT.pmx", "D:/MMD/ITEMS/DRESS/Random Stuff/wings/angelWings.pmx", 
	//	"D:/MMD/ITEMS/DRESS/Random Stuff/wings/leafWings.pmx", "D:/MMD/ITEMS/DRESS/Random Stuff/wings/prWings.pmx",
	//	 "D:/MMD/PMX/JQL/dog/fw.pmx","D:/MMD/PMX/JQL/bns/1rbq.pmx","D:/MMD/PMX/JQL/yjy/1.pmx", "D:/MMD/PMX/JQL/yjy2/1.pmx",
	// "D:/MMD/PMX/zz/dress.pmx",	"D:/MMD/ITEMS/DRESS/PinkDress/a.pmx",
	//"D:/MMD/ITEMS/DRESS/skirt/1.pmx",

	//"D:/MMD/ITEMS/wan/fc0.pmx","D:/MMD/ITEMS/wan/fc1.pmx","D:/MMD/ITEMS/wan/fc2.pmx",
#endif
	//"D:/MMD/PMX/JQL/zhe/1.pmx",//	"D:/MMD/PMX/JQL/yjy/1.pmx","D:/MMD/PMX/JQL/yjy2/1.pmx",
	//"D:/MMD/PMX/JQL/xjy/dao.pmx",// "D:/MMD/PMX/JQL/xjy/yl.pmx",
	//"D:/MMD/Blender/New Folder/jql_bns/hs.pmx",	
	//"D:/MMD/PMX/JQL/bns/1rb.pmx",
	//"D:/MMD/PMX/Y/lxzfix/s.pmx",//	"D:/MMD/PMX/Y/ntMln/sch.pmx", 	"D:/MMD/PMX/Y/qill2/box.pmx",
	// "D:/MMD/PMX/n/marin/hi/jkbLoSh.pmx", "D:/MMD/PMX/n/marin/hi/jkTail.pmx",//"D:/MMD/PMX/n1/Hifumi/2.pmx",	"D:/MMD/PMX/n1/Hifumi/1.pmx",
// "D:/MMD/PMX/n/marin/hi/bkn.pmx",//"D:/MMD/PMX/n/marin/hi/n.pmx",
//"D:/MMD/PMX/qqr/1/1.pmx",//"D:/MMD/PMX/qqr/1/1.pmx",//"D:/MMD/PMX/qqr/1/1.pmx",   // "D:/MMD/PMX/ld/rdhrb.pmx",
//"D:/MMD/PMX/qqr/strqqr/1.pmx",
   //"d:/mmd/n/n_Hitomi/2.pmx",	"d:/mmd/n/boobs/1.pmx",		
	//"d:/MMD/PMX/wenti/1.pmx",//"d:/MMD/PMX/xingqiu/1.pmx",
	//"D:/MMD/PMX/JQL/alj/1.pmx",//"D:/MMD/PMX/JQL/Rina/1.pmx",
	//"D:/MMD/PMX/JQL/ling/1.pmx","D:/MMD/PMX/JQL/cat/fw.pmx","D:/MMD/PMX/JQL/11/1.pmx","D:/MMD/PMX/JQL/nike/1.pmx",//"D:/MMD/PMX/JQL/dog/1.pmx",
	//"D:/MMD/PMX/JQL/Rina/n.pmx",//"d:/MMD/PMX/angela/1nf.pmx", // "d:/MMD/PMX/angela/1.pmx",//"d:/MMD/PMX/2b/1.pmx",//  
	//"D:/MMD/PMX/qqr/djs/2.pmx","D:/MMD/PMX/qqr/djz/1.pmx",
	// "D:/MMD/PMX/qianzhi/1.pmx","D:/MMD/PMX/shenhe/v2/1.pmx",	 "D:/MMD/PMX/ganyu/v2/rb.pmx",	 "D:/MMD/PMX/shenhe/s.pmx", 		"D:/MMD/PMX/zz/stocking.pmx","D:/MMD/PMX/ganyu/v2/rbBrumaS.pmx",		 "D:/MMD/PMX/ld/rdhrb.pmx",	 "D:/MMD/PMX/8ch/rb.pmx",	 "D:/MMD/PMX/ganyu/1.pmx",
};



static const bool OnArPath[] = { 0,0,0 };
static const int xrModes[] = { MMD_HAND_OXR ? 0x10000 : 0,0,0 };

static const char* pmxBallfiles[] = {
	"D:/MMD/PMX/qqr/mrpig/1.pmx","D:/MMD/PMX/qqr/mrpig/2.pmx",//
	//"D:/MMD/PMX/Setsuna/ss/srm.pmx", 
	//"d:/mmd/scene/daxi/s1.pmx",//"d:/mmd/scene/daxi/san.pmx", //"D:/MMD/PMX/qqr/1/1.pmx",//
};
#ifdef _WIN32
int jniCallbackMessage(int cmd, int pm1, int pm2, void* ptr) { return 0; }
#else
int jniCallbackMessage(int cmd, int pm1, int pm2, void* ptr);
void arcoreUpdateFrame(struct AhBufParam* pm);
#endif


#ifdef DBG_ON_PC
#define EQV_STYLE_PATH "D:/AProj/VkUpApp/eqv/mf"

#include "DbgHelpers.h"
static ualib::FileChangeDetector ScdShader(L"D:/AProj/UaIrrlicht/source/Irrlicht/VulkanRenderer/Shader/Compiled");
#include "VulkanRenderer/VkFixedFunctionMaterialRenderer.h"
static void* pThis = nullptr;
AppMainAMP* gAppMain{};
#endif
AppMainAMP::AppMainAMP(ualib::IUaLibBase* lib)
	:ILibStage(lib)
{
	gAppMain = this;
	Ctx = lib->getContext();
	Ctx->gd.oxrControl[1] = OXR_CONTROL_OBJ;
 
	mLib = lib;
	IrrDevice = Ctx->getDevice();
	mDriver = Ctx->getDriver();
	mDriver->setMinHardwareBufferVertexCount(16);


	//int x=std::thread::hardware_concurrency(); //mi11pro = 8
	vp.useFFEncoder = 0;
	vp.writeFrameToFileEveryNFrames = DRAW_BG_VIDEO_TO_FILE_FRAME_DIV;
	drawBG = DRAW_BG_VIDEO;
	vp.mis.formatDynamic = DYNIMG_TYPE ;
	vp.mis.dpSkipFrame = MIS_DP_SKIP_FRAME;
	vp.mis.dpPercent = MIS_DP_PERCENT; 
	vp.mis.dpFrameIntervalMs = MIS_DP_FRAME_MS;
	curInVideo = irr::io::path(TEST_VIDEO);
	initpsBasePos = irr::core::vector3df(0, SB_INIT_Y, 0);

	MMDPhysics::phyTimeMul = PHYSICS_TIME_MUL;
#if IS_WIN
	Ctx->FrameStepMul = FRAME_STEP_BASE;  //based on 60fps 
	//Ctx->useMidRT = 0;// !MMD_CONTROL_SD;
#if 1 //def _DEBUG
#if DRAW_BG_VIDEO_TO_FILE_FRAME_DIV
	Ctx->gd.bgColor =   BG_COLOR;
	std::filesystem::create_directories(std::filesystem::path("r:/img"));
	std::filesystem::create_directories(std::filesystem::path("r:/img01"));
	std::filesystem::create_directories(std::filesystem::path("r:/imgdp"));
	if (WRITE_FRAME_TXT) std::filesystem::create_directories(std::filesystem::path("r:/imgtxt"));
	if (MMD_CONTROL_SD && MMD_WRITE_MASK) {
		std::filesystem::create_directories(std::filesystem::path("r:/imgmk"));
		if (drawBG) Ctx->gd.bgColor = 0;
	}
	//std::filesystem::create_directories(std::filesystem::path("r:/imgbg"));
	if (WRITE_FRAME_TXT)vp.writeFrameTxt = WRITE_FRAME_TXT;
#endif
#endif
#endif
#if USE_LEAP
	leap = new LeapMan(Ctx);
	
	if (leap) {
		leap->cbOnHandTracking = [this](const LeapHandData& hd)
			{
				CMidiPlateSceneNode::nbp.ballMmdOfs = hd.mt.getTranslation();
				CMidiPlateSceneNode::nbp.rMul = hd.grab;
				auto it = it0;

				if (it) it->sb->leap = leap;
				if (it1) sb1->leap = leap;
				if (it2) it2->sb->leap = leap;

				if (1 &&  !leap->sbL) mmd->curCtrlSb([=](irr::scene::IrrSaba* sb) {					 
					vec3 ofs = hd.mt.getTranslation()*1.f; 
					quat rtt = glm::quat(hd.mt.getRotationRadians());// *quat(glm::rotate(glm::mat4(1), piFloat, vec3(0, 0, 1)));
					core::vector3df vec(ofs+vec3(0,-SB_INIT_Y-10,0));
					
					//sb->ndRoot->SetAnimationTranslate(  glm::vec3(vec * sb->ndRoot->GetScale().x));
					if (hd.hid == 0) {
						vec3 p = itScene->sb->ndRoot->GetAnimationTranslate();
						quat r = itScene->sb->ndRoot->GetAnimationRotate();
						p = glm::mix(p, glm::vec3(ofs * sb->ndRoot->GetScale().x), 0.2f);
						r = glm::slerp(r, rtt, 0.2f);
						itScene->sb->ndRoot->SetAnimationTranslate(p);						itScene->sb->ndRoot->SetAnimationRotate(r);

						if (hd.grab > 0.2f) {
							vec3 pos = { 0,  10,0 };
							pos = itScene->sb->ndRbRoot->transformVec(pos);
							int r = hd.grab * 10;
							addFluidParticlesGrid(r,r,r, 1, 1, 1, pos, { 0,0,0 });
						}
					}
					else 
					{
						auto ssb = itSns[1]->sb;
						vec3 p = ssb->ndRoot->GetAnimationTranslate();
						quat r = ssb->ndRoot->GetAnimationRotate();
						p = glm::mix(p, glm::vec3(ofs * sb->ndRoot->GetScale().x), 0.2f);
						r = glm::slerp(r, rtt, 0.2f);
						ssb->ndRoot->SetAnimationTranslate(p);						ssb->ndRoot->SetAnimationRotate(r);
					}
					});
			};
	}
#endif
}


AppMainAMP::~AppMainAMP()
{
#if USE_LEAP
	if (leap) delete leap; 
#endif
	
}



void AppMainAMP::StageBegin()
{

	oxrBegin();

	VkDrv = (VkDriver*)Driver;
	initData();
	Ctx->jss.SetFile("data/app_settings.json", true);
	Ctx->setFixFrameTime(FIX_FRAMETIME, 1.0 / APP_FPS, APP_FPS);


	auto sm = SceneManager;		
	Ctx->gd.baseLightPos = irr::core::vector3df(0.618,  2.1, -1.6) * (MMD_ZERO_FRICTION ?  300:60);
	snLight = sm->addLightSceneNode(arRoot, Ctx->gd.baseLightPos * MMD_SABA_SCALE, {1.0,1.0,1.0});
	snLight->getLightData().AmbientColor.set(.5, .5, .5);
	//snPtBall = sm->addSphereSceneNode(10, 16, Ctx->gd.RootSn, -1, {0,100,100});
	//snPtBall->setScale(curPtR);
	//snPtBall->setMaterialFlag(EMF_LIGHTING, true);
	//snPtBall->setMaterialType(EMT_TRANSPARENT_ALPHA_CHANNEL);
	//snPtBall->setMaterialDiffuse(0x80808080);
	snLight->passTypeFlags |= IrrPassType_GBuffer;
	StageOnBackBufferResized(true);
#if DRONE_AR
	snBox = sm->addCubeSceneNode(1000, Ctx->gd.RootSn, -1, { 0,0,5000 });
	snBoxSub = sm->addCubeSceneNode(100, snBox, -1, { 0,550,0 });
	snBoxSub->setMaterialDiffuse(0xFFFF0000);
	sm->addCubeSceneNode(1008, snBox, -1, { 0,2000,0 })->setMaterialDiffuse(0xFF00FF00);
	sm->addCubeSceneNode(1000, 0, -1, { 2000,0,5000 })->setMaterialDiffuse(0xFFFFFF00);
#elif 0
	auto snb = sm->addCubeSceneNode(100, snBox, -1, { 0,0,1000 });
	snb->setMaterialDiffuse(0xFF00FF00); snb->setMaterialFlag(EMF_LIGHTING, false);
	snb->setPickData(0x7777777);
	snb = sm->addCubeSceneNode(200, snBox, -1, { 220,220,220 });
	snb->setMaterialDiffuse(0xFF00FFF0);
	snb->getMaterial(0).PickColor = 0xFFFF0000;
	snb->setPassType(IrrPassType_PickPoint, true);
#endif
	auto cam = Ctx->gd.CamNormal;
	cam->bindTargetAndRotation(true);
	cam->setTarget({ CAM_INIT_POS_X, CAM_INIT_POS_Y, 0 });
	cam->setPosition({ CAM_INIT_POS_X,CAM_INIT_POS_Y,-Ctx->getDriver()->dsd.stdDistance*3 });
	cam->setUpVector({ 0,1,0 });

#if IS_WIN
	Ctx->setCameraId(-1);	Ctx->gd.CamRtt->bindTargetAndRotation(true);
	(*Ctx->gd.CamRtt->getAnimators().begin())->setTarget({ CAM_INIT_POS_X, CAM_INIT_POS_Y, 0 });
	auto anm = ((irr::scene::CSceneNodeAnimatorCameraTouchControl*)*Ctx->gd.CamRtt->getAnimators().begin());
	anm->setMinY(0.1f * MMD_SABA_SCALE);
	anm->trsMul = 20 * MMD_SABA_SCALE;
	if (!JOYSTICK_CAMERA) anm->acceptJoystick = false;
#if CAM_BOX_VISUAL  
	auto camms = SceneManager->getMesh("data/mesh/camRing.obj");// cameraModel.obj");
	snCam =//CAM_DYN_RB ?  SceneManager->addEmptySceneNode(Ctx->gd.CamRtt) : 
		SceneManager->addMeshSceneNode(camms, Ctx->gd.CamRtt);
	snCam->setMaterialDiffuse(0x8000FF00); snCam->setScale(100);
	snCam->getMaterial(0).SpecularColor = 0; 
	snCam->setVisible(false);
	snCam->setMaterialType(EMT_TRANSPARENT_ALPHA_CHANNEL);
#endif
#endif
	Eqv = new EQV(Ctx); Eqv->pvp = &vp;
#if 0 //err: clip obj , kirara box 
	if (Eqv) Eqv->mLight = snLight;
#endif
#if 0
	Eqv->recreateOnUpdate();
#else
	//InitTraceLogging();// by AI
	//TraceLoggingWrite(g_hMyComponentProvider, "Start_AddPoint");
	eqvRecreate();
	//TraceLoggingWrite(g_hMyComponentProvider, "End_AddPoint");
#endif
	
#if USE_NETMAN_HAND
	netMan = new NetMan(Ctx);
	netMan->connect("localhost:10001");// "***********:10001");
	netMan->cbOnData = [this](std::string s) {
		static int i = 0;
		ualib::UaJsonSetting jss("camdatjson2");
		jss.LoadJsonString(s);
		Json::Value js = jss.getRootValue();
		auto& frame = js;
		EQV::HandData hf;

		hf.c = frame.size();
		if (hf.c > 20) {
			for (int j = 0; j < frame.size(); j++)
				hf.pos[j] = glm::vec3(frame[j]["x"].asFloat(), frame[j]["y"].asFloat(), frame[j]["z"].asFloat());
			glm::quat rt = glm::vec3(0, 0, 45) * core::DEGTORAD;
			glm::quat rt1 = glm::vec3(90, 0, 0) * core::DEGTORAD;
			glm::quat rt2 = glm::vec3(0, 180, 0) * core::DEGTORAD;
			rt = rt * rt1 * rt2;
			for (int j = 1; j < frame.size(); j++) {
				hf.pos[j] = hf.pos[j] - hf.pos[0];
				hf.pos[j] = rt * hf.pos[j] * 10.f;
			}
			hf.pos[0] = { 0,0,0 };
 
			Eqv->handData=hf;
			DP(("hfd %d, %f", hf.c, hf.pos[20].x));
			i++;
			arRoot->curArSn->saba->showHandPt = true;
		}
		return  0;
	};
#endif
#if IRR_MTR_SSAO
	{

		//SceneManager->addCubeSceneNode(100, 0, -1, { -170,50 ,-190 })->passTypeFlags ;
		auto sn = SceneManager->addSphereSceneNode(30, 32, 0, -1, { -27.5,00,0 });
		sn->setMaterialType(EMT_SSAO_GBUFFER);
		sn->passTypeFlags = IrrPassType_GBuffer;
		sn = SceneManager->addSphereSceneNode(30, 32, 0, -1, { 27.5,00,0 });
		sn->setMaterialType(EMT_SSAO_GBUFFER);
		sn->passTypeFlags = IrrPassType_GBuffer;
	}
#endif
#ifdef DBG_ON_PC
	pThis = this;
	ScdShader.mCallBack = [this]() {
		static int cpy = 0;
		static int64_t tms = 0;
		if (ualib::NowMs() - tms < 2000)
			return;
		tms = ualib::NowMs();
		if (cpy == 0)
		{
			cpy = 1;
			ualib::SleepMs(500);
			mMrNeedRefresh = 1;
			//clearFws();
			//GenTextImage("ABC", 360);
			if (getLib()) getLib()->renderLockRun([this]() {
				Driver->waitDriverIdle(3);
				//VkMr2D::RefreshMr(Driver);

				});
			cpy = 0;
		}
		};
#endif
#if USE_IMGUI  
	VkDrv->OnUpdateUIOverlay = [this](vks::UIOverlay* overlay) {
		if (!Driver->renderUI) return;
		uiOverlay = overlay;
		static char sz[1024];
		//if (overlay->header("Cam")) 
		//{ 
		//	auto anm = ((irr::scene::CSceneNodeAnimatorCameraTouchControl*)*Ctx->gd.CamRtt->getAnimators().begin());			
		//	sprintf_s(sz, "fov=%.3f,%.3f | d=%.3f\nT %.3f %.3f %.3f\nR %.3f %.3f %.3f",
		//		Ctx->gd.CamRtt->getFOV(), anm->tld.fovyMul, anm->tld.zoomPow,
		//		anm->tld.trs.X, anm->tld.trs.Y, anm->tld.trs.Z,
		//		anm->tld.rtt.x, anm->tld.rtt.y, anm->tld.rtt.z);
		//	overlay->text(sz); 
		//}
		if (mmd->MPA) 		mmd->MPA->OnUpdateUIOverlay(overlay);
		arRoot->OnUpdateUIOverlay(overlay);
		};
#endif
	if (PLAY_AUDIO && PLAY_AUDIO_FILE.size())
		curInVideo = PLAY_AUDIO_FILE.c_str();
	if (SUBTITLE_PATH.size())
		curInSubtitle = SUBTITLE_PATH.c_str();
	if (AI_PH_NAME.size() > 0) {
		PLAY_AUDIO_FILE = AI_PH_NAME.c_str();
		curInVideo = AI_PH_NAME.c_str();
		if (fileExists(AI_PH_NAME.pathWithOutExt() + ".ass")) curInSubtitle = AI_PH_NAME.pathWithOutExt() + ".ass";
		
	}
	
	
	extern void createTemplateSceneNodeExample(ISceneManager * smgr, IVideoDriver * driver, io::IFileSystem * fs);
	//createTemplateSceneNodeExample(SceneManager, Driver, Ctx->getFileSystem());

//	Ctx->gd.apm.countRt = 10; //Ctx->gd.apm.countRt = 50;
	Ctx->gd.apm.spdMul = 3;
	Ctx->gd.apm.camOfs = vec3(0, 0, 2);
	Ctx->gd.apm.dirOfs = vec3{ 0,.3f,0 };
	Ctx->gd.apm.angle = 0;
    inited = true;
}

void AppMainAMP::StageEnd()
{
	 
#if ASYNC_AR
	if (arStarted)
	{
		arStop = true;
		arThread.join();
	}
#endif
#if USE_LEAP
	if (leap) delete leap;
#endif
	stopRecord();

	if (lockOnSb) lockOnSb->drop();
	if (mmd) mmd->freeAllRes();
	//CCubeGridSceneNode::clearWalls(); parent is mmd->RootNode, will free at last
	arRoot->removeAll();
   // ualib::SleepMs(100);
	SceneManager->clear();
	delete mmd;

#ifdef DBG_ON_PC
	pThis = nullptr;
	ScdShader.mCallBack = 0;
#endif
	
	mLib = nullptr;
	//delete indexBuffer; delete vertexBuffer;
}

void AppMainAMP::onFrameN(int fr)
{
	if (fr == 10)
	{
#if MMD_JOYSTICK_GAMECAST
		sendKeyEvent(KEY_KEY_V);
		sendKeyEvent(KEY_KEY_R);
#endif
		//sendKeyEvent(KEY_KEY_U);
	}
	else if (fr == 20) {

		//	sendKeyEvent(KEY_KEY_W);
	}
	else if (fr == 2) {
		arRoot->setCurSn(it0);
#ifndef _DEBUG
		// sendKeyEvent(KEY_KEY_E);
#endif
	}
	if (vp.paused) return;
	if (fr%2 && ModelCreateCD > 0) {
		ModelCreateCD--;
		sendKeyEvent(KEY_KEY_M);
		sendKeyEvent(KEY_KEY_P);
		arRoot->setCurSn(it0);
	}
	if (fr%60 == 0) {
		//addFluidParticlesGrid(10, 80, 10, 0.6,0.6,0.6, { 0,70,10 }, { 0,-28,0 });
	}
	if (fr % 3 == 0) {
		//sb0->ndHead->rb0->addLinearVel({ 0, 10, 0 });
	}

#if MMD_BAND_EQV
	 if (fr > 100 && fr <= 100+ Eqv->mPM.overrideBandCount *2 && fr%2) addMmdObj(0x100);
#endif
}

void AppMainAMP::initObjectsInBeginScene()
{
#if VK_ENABLE_RAYTRACING
	exampleRaytracingSceneUsage(VkDrv, SceneManager);

#endif

#if HAS_SHADOWMAP 
	auto ms = SceneManager->getMesh("res/cube.obj");
	snShadowRcv = SceneManager->addMeshSceneNode(ms, Ctx->gd.RootSn, -1, { 0,0.1f ,0 });
	snShadowRcv->setScale(2000* MMD_SABA_SCALE, 2, 2000* MMD_SABA_SCALE);
	snShadowRcv->getMaterial(0).RcvShadow = true;
	snShadowRcv->getMaterial(0).DiffuseColor = IRR_SHADOW_COLOR; //0xC0FF8080;
	snShadowRcv->setMaterialType(EMT_TRANSPARENT_ALPHA_CHANNEL_NoDepthWrite);
	snShadowRcv->setPassType(IrrPassType_Mirror, false);
	snShadowRcv->setVisible(GROUND_SHADOW);
	auto sn = SceneManager->addMeshSceneNode(ms, Ctx->gd.RootSn, -1, { 0,0 ,0 });	sn->setScale(10*MMD_SCALE_OLD);	sn->setPassType(IrrPassType_ShadowMap, true);
	sn->setVisible(!MMD_CONTROL_SD);
	snLight->setShadowLightCam(false);
	auto lc = snLight->getShadowLightCam();
	lc->setFOV(LIGHT_FOV * core::DEGTORAD);
	lc->setFarValue(1000 * MMD_SABA_SCALE);

	//auto snBall = SceneManager->addSphereSceneNode(100, 32, snLight, -1, { 0,100 ,0 });
	//snBall->passTypeFlags |= IrrPassType_ShadowMap;
	//snBall->getMaterial(0).EmissiveColor = 0xFFFFFFFF;
#endif

	{
	
		ArRootParam arpm{}; arpm.vp = &vp; arpm.stage = this; arpm.aip.arRoot = nullptr;  
		auto& aip = arpm.aip;
		aip.eqv = Eqv; aip.ctx = Ctx; aip.arRoot = nullptr;
		static char rootLabel[] = "[Root]";
		auto p = new CsEditorItem();
		initCsEditorItem(p);
		p->root = 1;
		p->label = rootLabel;// need free?	
		aip.cei = p;//		arpm.aip.ctx = Ctx; arpm.aip.eqv = Eqv;
		p->sn = arRoot = new SnArRoot(Ctx->gd.RootSn, SceneManager, -1, arpm);
		arRoot->drop();
		arRoot->setScale(ARROOT_SCALE);

		snLight->setPosition({arRoot->Cs.lightPos[0], arRoot->Cs.lightPos[1], arRoot->Cs.lightPos[2]});
		arRoot->onSbNodePicked = [this](MMDNode* nd) {
			if (mmd->MPA) {
				mmd->MPA->onSbNodePicked(nd);
			}
			};
	}

	irrSabaParam pm; pm.eqv = Eqv; pm.ctx = Ctx;  pm.snMmdRoot = Ctx->gd.RootSn;
	pm.hasFW = IS_WIN;
	IrrMmdParam mmndpm{ pm ,false,arRoot};
	mmndpm.mmdInfinity = MMD_Infinity;
	mmd = new irr::scene::IrrMMD(mmndpm);
	Ctx->mmd = mmd;
	Eqv->initComponents(mmd);
	
	if (1)
	{
		auto Ephy = USE_PHYSX ? EPhysicsEngine::PhysX : EPhysicsEngine::Jolt;
		auto physics = saba::createPhysicsInstance(Ephy);
		PhyObjManagerParam pm{ SceneManager,physics,Eqv, mmd };
		gPoMan[Ephy] = new PhyObjManager(pm);
	}
	arRoot->mmd = mmd;
	mmd->cs = &arRoot->Cs;

}

void AppMainAMP::StageUpdate(float stepTime)
{
	if (frameSleep)
	{
		ualib::SleepMs(frameSleep);
	}
	gTimeMul = Ctx->gd.timeMul;
	if (Ctx->scenePaused) Driver->dsd.noUpdateOnlyDraw = 1;
	frameMmd = 0;
	if (Ctx->gd.frameCount <= 60000) onFrameN(Ctx->gd.frameCount);
	
	FrameWaiter::updateFrame(1);

	auto cam = Ctx->gd.CamRtt;// SceneManager->getActiveCamera();
	baseCamera = cam;
	if (Ctx->gd.lightPosChanged && arRoot) Ctx->gd.lightPosChanged=false,updateLightPos(0);
	snShadowRcv->getMaterial(0).DiffuseColor=(cam->getAbsolutePosition().y > 0) ? IRR_SHADOW_COLOR : 0x20000000;
	
	 
	
	//DP(("renderM %f", vp.mediaTime));
	if (mMrNeedRefresh)  // Reload Material Renderer
	{
		mMrNeedRefresh = 0;

		Driver->waitDriverIdle(3);
		ualib::SleepMs(60);

		CMrCsParticle::RefreshMr(Driver, mFwBlendId, 0);
		IFppt->ResetParticles();
		if (mmd->fluidNode) mmd->fluidNode->RefreshMr();
		VkFixedFunctionMaterialRenderer::RefreshMr(Driver);
		Driver->waitDriverIdle(3);
		VkMr2D::RefreshMr(Driver);
		ualib::SleepMs(60);
	}
	if (Eqv->needRecreate())
	{
		eqvRecreate();


	} //EQV recreate
#if TEST_MMD_ONSTART
	static int testMMDOnstart = 0;
	if (!testMMDOnstart) {
		testMMDOnstart = 1;
		if (Ctx->isApp(APPID_WinTest))
			loadSabaModel(defaultFcp);//sendKeyEvent(KEY_KEY_P); //sendKeyEvent(KEY_KEY_O);
	}
#endif
	//lockCamUpdate();

	if (objCenterForce ) {
		sb0->phyObjForceAttack({ 0, float3(0,16, 0),10.f * objCenterForceMul });
	}
	//else if (Ctx->gd.oneShotForce) {
	//	Ctx->gd.oneShotForce = false;
	//	sb0->phyObjForceAttack({.rst=0, .ofs=float3(0,16, 0),.mul=1.f * objCenterForceMul, .tag = 'cbgr' });
	//}

	if (Ctx->gd.apm.autoShot || Ctx->gd.apm.toUpdate && Ctx->gd.apm.checkMode)
	{
		Ctx->gd.apm.toUpdate = 0;
		irr::SEvent::SKeyInput ke{};
		bool ret = 0;
		shotR(ke, ret);
	}


	//DP(("stageUpdate"));
	if (vp.colMapId >= 0) {
		ColMapImage& cmi = vp.cmArr[vp.colMapId];
		auto cam = Ctx->gd.CamNormal;
		auto mat =  //cmi.mat;// 
			cmi.snCam->getAbsoluteTransformation();
		cam->setPosition(mat.getTranslation());
		cam->bindTargetAndRotation(true);
		cam->setRotation(mat.getRotationDegrees());
		cam->upNode->updateTransform();
		cam->setUpVector(cam->getUpVectorByNode());
		cam->updateAbsolutePosition();
		//Eqv->LaunchFw3D(mat.getTranslation(), Eqv->getFwIdx(2, 0), { 0,100,0 });
	}
    if (!Ctx->gd.frameMoved){
        arRoot->addFakePtrMove();
    }
#if USE_LEAP
	if (leap) leap->updates(stepTime);
#endif

 
#if ASYNC_AR
/*	if (!arStarted){arStarted=true;arThread = std::thread([this](){			while (!arStop)	arUdpate();});}*/
#else
    arUdpate();
#endif
	phyFrameUpdate();
	phyModifyUpdate();
#if TEST_FW_CUR
	Eqv->LaunchFw3D({ 0,1000,0 }, Eqv->getCurPtrFwIdx(1), { 0,-100,0 });
#endif

    frameTimeS = stepTime;
	//Eqv->LaunchFw3D({0,0,0}, Eqv->getFwIdx(2, 1), {0,0,0});
	static int g_cc = 0; g_cc++;
	static int64_t lastT = ualib::NowMs();
	if (g_cc % 60 == 0 ||  ualib::NowMs() - lastT>1000LL)
	{
		lastT = ualib::NowMs();
		static char sz[256]; snprintf(sz, 256, "%d, %.2f ms  %d  th=%d pm=%.3f oit=%d WS=%d", Ctx->gd.fps, 
			Ctx->gd.sceneTime * 1000.f,

			arRoot->getItemCount()
			, Driver->threadPool.getThreadCount(),MMDPhysics::phyTimeMul, Driver->dsd.OitCb.uMaxCount, SceneManager->WSADMode);
		//DP(("FPS %S", sz));
#ifdef _WIN32
		extern HWND g_MainWin;
		SetWindowTextA(g_MainWin, sz);
#endif
		arRoot->Cs.dbgString = sz; arRoot->Cs.dbgStringUpdated = 1;
	}

#if USE_SBT_ANIM3D
	//if (arRoot->curArSn)		arRoot->curArSn->setParent(Eqv->snSbtItv);
#endif


	//snBox->setRotation({ Ctx->gd.time*20, Ctx->gd.time * 30, Ctx->gd.time * 60 });
	djiUpdate();

	processVideo();




	oxrUpdate();


#if USE_NETMAN_HAND
	netMan->updateFrameAmp(stepTime);
#endif
	 
	if (toSaveScrShot)
	{
		Driver->waitDriverIdle();
		toSaveScrShot = false;
		Eqv->saveScreenShot();
	}
	if (toSaveCamVmd) {
		toSaveCamVmd = false;
		saveCameraVmd();
	}

#if HAS_MIDI
	if (vp.working && vp.renderTime > 0.001f) {
		Ctx->Midi.setRecTime(vp.renderTime);
		if (FW_TO_MIDI) Ctx->MidiFw.setRecTime(vp.renderTime);
	}
#endif
	
	arRoot->updateMediaFrame(//vp.arFrameIdx>=0? float(vp.arFrameIdx)/AR_DATA_FPS: 
		vp.mediaTime, frameStepTimeS);

	if (toShowTextCD > 0) {
		toShowTextCD--;
		if (toShowTextCD == 0)	genStkText();
	}

	vp.doToFreeScrFrames();

	

	if (arTimeOfsAutoIncUs) 
		arRoot->forEachArChild([=](SnArItem* sn) {
		sn->mArTimeOfs += arTimeOfsAutoIncUs / 1000000.f;
			
			});
  
	bool animating = !Driver->dsd.paused();
	if (mmd) {
		mmd->preUpdate();
		if (animating || (vp.recMode == 9 && REC_PAUSE_RM9)) mmd->mdplr.updateFrame(stepTime);  
	}

	
	Ctx->mainViewId = ev_main;
	//SceneManager->setActiveCamera(Ctx->getViewCamera(Ctx->mainViewId));

	


	if (animating && sb0) //Frame first beginScene, for VR stereo 
	{		
		 
		sb0->Pom->reflectPhyToSn();
		if (Driver->dsd.forceUpdateCD > 0) Driver->dsd.forceUpdateCD--;
		

		SceneManager->frameTimeS = frameTimeS;
		u32 ms = Ctx->gd.time * 1000;
		int fs = 1;// Physics only once 
		//Ctx->getFrameStep();
		mDriver->dsd.deltaTime = Ctx->gd.deltaTime / fs;
		//DP(("time +%f", Ctx->gd.prevTime));
		//for (int i = 0; i < fs; i++) 
		{
		//	if (i > 0) {
		//		MMDPhysics::UpdateFrameStart(mDriver->dsd.deltaTime);
		//		MMDPhysics::UpdateFrameWait();
		//	} 
			//float t =(Ctx->gd.prevTime + Ctx->gd.deltaTime * (i + 1) / fs);
			//mDriver->dsd.time = t;

			//SceneManager->sceneTime = t;
			//gSceneTime = t;
			//assert(!Ctx->scenePaused);

			// if (!Ctx->scenePaused)				gSceneTime += frameTimeS / fs;
			//DP(("time >%f",t));
			//ms = t * 1000;
			auto cam = SceneManager->getActiveCamera();			

			cam->skipOnAnimateByParent = true;
			if (cam != Ctx->gd.CamRtt || (arRoot->allowRttCamControl() && Ctx->getEvtRcv()->curPointerCount() <2 && arRoot->snPickMap.size() == 0) && 
				cam->getParent() == SceneManager->getRootSceneNode()) 
					cam->OnAnimate(ms); 	 		
			else cam->skipOnAnimateByParent = false;

			onKeyUpdate();
			arRoot->updateTmpIk(); 
			SceneManager->getRootSceneNode()->OnAnimate(ms);

			if (MULTI_VIEW_COUNT < 2 ) updateFpvCamOnSaba();
			lockCamUpdate(); 	
		}

		if (vp.working) for (int i = 0; i < 8; i++) if (mmd->sabas.size() > i) 
		{
			auto sb = mmd->sabas[i];
			float sc = 0.1f;
			auto cam = SceneManager->getActiveCamera();
			X3dUpdateParam pm{}; pm.fElapsedTime = stepTime;
			pm.l.pos = float3(sb->irr2mmd(cam->getAbsolutePosition()));
			pm.l.dir = (cam->getTarget() - cam->getAbsolutePosition()).getNormalizeCopy();
			pm.e.pos = sb->ndUpper2->getGlobalPos();
			pm.e.dir = glm::mat3(sb->ndHead->GetGlobalTransform()) * float3(0, 0, -1);

			//pm.l.pos *= sc; pm.e.pos *= sc;

			pm.fElapsedTime = vp.renderTime;

			if (PLAY_AUDIO && Ctx->audioMan)  Ctx->audioMan->Update3DAudio(pm);

			if (MMD_VIRTUAL_SINGER) g_timeline[i].push_back(pm);
		}
	}
	else {
		auto tms = Ctx->gd.time * 1000;
		SceneManager->getActiveCamera()->OnAnimate(tms);
		snLight->OnAnimate(tms);
	}
    arDatUpdated=true;

	IFppt->mEyePos = SceneManager->getActiveCamera()->getAbsolutePosition();
	Driver->dsd.snLightPos = snLight->getAbsolutePosition();

	if (mmd) {
		mmd->postUpdate();
	
	}

#if HAS_PIANO
	if (Piano)
		Piano->updateFrame(stepTime);
#endif
#if HAS_MIDI


#if HAS_MMD
	Ctx->Midi.FindNextN = sb0?sb0->MmdMode:0;
#else
	Ctx->Midi.FindNextN = 0;
#endif
	if (!vp.paused)
	if (Plates.size()) Plates[0]->recordUpdate(vp.renderTime);
	if (!vp.paused)
	Ctx->Midi.updateTime(int64_t(gSceneTime * 1000), MIDI_DELAY*1000, [this](const midilib::MidiEventStruct& evt, bool isCurPlaying)
		{
#if HAS_MMD
			
			auto saba = evt.key >= 60|| it1==nullptr ? sb0:sb1;
			size_t cc = 0,id=0;
			if (midiSabas.size() > 0) {
				cc = (60) / midiSabas.size();
				id = evt.key<30?0:(evt.key - 30) / cc;
				
				saba = midiSabas[std::min(id,midiSabas.size()-1)];
			}
			switch (saba->MmdMode)
			{
			case 1:
			case 10:
				 
			case 11:
			{
#if HAS_PIANO
				if (!isCurPlaying)
				{
					if (evt.flag == 0xFF)
						return;
					auto e = evt;
#if MMD_MIDI_MODE==10
					e.track =  evt.key > 30 + id * cc + cc / 2;
#else
					if (sabas.size() == 1)
						e.track = evt.key < 60;
					else 
						e.track = evt.key <= 30 + id * cc + cc / 2;
#endif
						//e.track = evt.key <= 47 || evt.key >= 60 && evt.key < 72;
					
					saba->piano = Piano;
					saba->onMidiEvent(e);

				}
#endif
			}
			break;
			}//switch 
#endif
#if HAS_PIANO
			Piano->onMidiEvent(evt);
#endif
		});
#endif


	if (Eqv) Eqv->UpdateFrame(stepTime);
	Ctx->frameUpdate();
	// SABA_PHYSICS_ASYNC 模式下：确保所有修改已结束方可更新物理,Update中不可修改scenenode，避免render()访问冲突
	if (animating) MMDPhysics::UpdateFrameStart(mDriver->dsd.deltaTime);
 


	// ***************** Offscreen Renderings ******************

	if (vp.snPly) vp.colmapFw();

#if HAS_SHADOWMAP

	if (Driver->getShadowOn())
	{
		auto cam = SceneManager->getActiveCamera(), lc = snLight->getShadowLightCam();
		cam->updateTransform(); cam->updateMatrices();
		//snLight->setPosition(lc->getTarget() + (cam->getPosition() - cam->getTarget()).getLength() * (lc->getPosition() - lc->getTarget()).getNormalizeCopy());
		
		if (0&&sb0) snLight->getShadowLightCam()->setTarget(  //AR err
			SceneManager->getActiveCamera()->getTarget()
			//sb0->mmd2irr(sb0->ndCtr->rb0->getPosition())
		);
		else snLight->getShadowLightCam()->setTarget({ 0,0,0 });

		float t = Ctx->gd.time * core::PI / 10;
		float y = sin(t),x=cos(t);
		// snLight->setPosition(x*2000, 2000, y* 2000);
		// snLight->setShadowLightCam(true);
				//snLight->getShadowLightCam()->updateTransform();
		SceneManager->drawShadowMap(snLight);

		//Driver->saveTexture(Driver->getTexSdMap(), "r:/sdmap.png");
	}
#endif

	drawPD = false;
	if (mmd->pauseDemoing	|| (MMD_WRITE_MASK && vp.writeFrameToFile)
		) {
		if (!vp.texPD)
			vp.texPD = Driver->addRenderTargetTexture(Ctx->mainViewSize(), "rt", Driver->getSysRT() ? Driver->getSysRT()->getColorFormat() : Driver->getColorFormat());
		drawPD = (mmd->drawPauseDemo(vp.texPD)) && mmd->pauseDemoing;
	}



	drawBloom();
	if (copySScd > 0) {
		copySScd--;
		if (copySScd == 0)	vp.copyScrShotToClipboard();
	}


		//Draw Mirror Scene

	if (drawMirror) {}
#if IRR_MTR_SSAO
		else {
			Driver->setRenderTarget(VkDrv->ssaoRT, true, true, 0xFF00FF00);
			VkDrv->resetFrameCache();
			SceneManager->drawPassType(IrrPassType_GBuffer);
			VkDrv->UploadSharedBuffers();
			VkDrv->flush();
		}
#endif
 
		if (cloth) cloth->setClothTex(vp.curVFrame.tex);


#if FW_DRAW_WATER
		static int nextFrameCC = Ctx->gd.frameCount;
		if ((Ctx->gd.frameCount >= nextFrameCC || Ctx->gd.frameCount % 30 == 0))
		{
			nextFrameCC = Ctx->gd.frameCount + 3 + UaRand(3);
			//snWater->addWaveSource(Ctx->gd.scrWidth * UaRandm1to1()*10, -Ctx->gd.scrHeight * UaRandm1to1()*10, 1);
		}
		//water refection effect
		if (VkDrv->texDuDv[0]) {

			Driver->setRenderTarget(VkDrv->texDuDv[0], 1, 1, 0);
			VkDrv->resetFrameCache();

			SceneManager->drawPassType(IrrPassType_DuDv);
			VkDrv->UploadSharedBuffers();

			Driver->setRenderTarget(nullptr, 0, 0, 0);

		}
#endif
		renderSubview();
#if USERT2
		RT2Drawed = false;
		if (mmd->fluidNode && (mmd->fluidNode->passTypeFlags& IrrPassType_PostLayer))
		{
			Ctx->ensureMidRT(ev_post);
			Driver->setRenderTarget(Ctx->texMidRT[ev_post], 1, 1, 0);
			mmd->fluidNode->setDepthTexture(Ctx->texMidRT[ev_main]);
			VkDrv->resetFrameCache();
			bool useFpv = !Ctx->vrOn && Ctx->gd.CamFpv && (Ctx->vrOn || (MULTI_VIEW_COUNT == 1));
			if (useFpv) SceneManager->setActiveCamera( Ctx->gd.CamFpv);
 			SceneManager->drawPassType(IrrPassType_PostLayer);
			VkDrv->UploadSharedBuffers();
			Driver->setRenderTarget(nullptr, 0, 0, 0);  
		}
		else
		if (mmd->ikVisable) {
			Ctx->ensureMidRT(ev_ui);
			Driver->setRenderTarget(Ctx->texMidRT[ev_ui], 1, 1, 0);
			VkDrv->resetFrameCache();
			SceneManager->drawPassType(IrrPassType_3D_UI);
			VkDrv->UploadSharedBuffers();
			Driver->setRenderTarget(nullptr, 0, 0, 0);
			RT2Drawed = true;
			//Driver->saveTexture(Ctx->texMidRT[2], "r:/rt2.png");
		}
#endif
}
void AppMainAMP::StageRender()
{
	//DP(("render ++++++++++++++++++++++++++++++"));
	if (camSave) camSave->getViewMatrix().getInverse(IFppt->mCustom);
#if USE_JOLT
	MMDPhysics::DebugRender(Driver);

#endif

	bool hdrDraw = Ctx->gd.toneForHdr && Driver->getUseTexRT();
	{

		Ctx->gd.libCaptureScreen = mmd->fluidNode ||  (!(RECORD_MIDRT && Ctx->useMidRT) || MULTI_VIEW_COUNT >= 2 || mDriver->dsd.initHDR);
		if (Ctx->useMidRT) {
			if (Ctx->texMidRT[ev_post]) {
				Driver->setRenderTarget(Ctx->texMidRT[ev_main], 0, 0, 0);
				Ctx->texMidRT[ev_post]->waitRenderRT();
				Driver->draw2DImage(Ctx->texMidRT[ev_post], 0, 0, false);
			}
			int viewIds[2] = { ev_main, ev_fpv };
			int drawViewCount = Ctx->getViewState(ev_fpv) ? 2 : 1;

			for (int dvi = 0; dvi < drawViewCount; dvi++)
			{
				int rtvId = viewIds[dvi];

				int vi = rtvId;
				auto rc = Ctx->texMidRT[vi]->getRectI();
				if (mirrorLR)
					std::swap(rc.UpperLeftCorner.X, rc.LowerRightCorner.X);
				Driver->setRenderTarget(nullptr, 0, 0, 0);





				if (hdrDraw)
				{
					Ctx->ensureMidRT(ev_hdr, 1, Driver->getSysRT()->getSize(), Driver->dsd.drvMidTexFormat);
					Driver->setRenderTarget(HDR_USE_MIDRT ? Ctx->texMidRT[ev_hdr] : 0, dvi==0, dvi == 0, 0);
					video::SMaterial mr;
					mr.MaterialType = Driver->get2DNativeMaterialType(Fx2D_HDR);
					mr.setTexture(0, Ctx->texMidRT[vi]);
					video::VkMr2D* mr2d = (video::VkMr2D*)Driver->getMaterialRenderer(mr.MaterialType);
					mr2d->cbDraw.tone.exposure = Driver->dsd.hdrExposure;
					mr2d->cbDraw.tone.gamma = Driver->dsd.hdrGamma;
					//mr2d->cbDraw.tone.bit = int(pow(2, 1) + 0.5);  //test only
					Driver->draw2DImageMr(mr, Ctx->getViewRC(vi), rc);
				}
				else {
					Driver->setRenderTarget(nullptr, dvi == 0, dvi == 0, 0);
					Driver->draw2DImageRect(Ctx->texMidRT[vi], Ctx->getViewRC(vi), rc);
				}

			}


		}
		else {
			if (drawBG) drawBgVideo(Driver);
			drawSsaoRT();
			SceneManager->drawAll();
			if (Ctx->texMidRT[ev_post]) {
				 
				Driver->draw2DImage(Ctx->texMidRT[ev_post], 0, 0, false);
			}
		}

		SceneManager->setActiveCamera(Ctx->getActiveCam());





		if (Ctx->texMidRT[ev_bloom] && mmd->hasBloom && (editMode == 2 || IS_WIN)) {
			auto rtsize = Driver->getCurrentRenderTargetSize();
			irr::core::recti rc = Ctx->texMidRT[ev_bloom]->getRectI(), trc = { {0,0,},rtsize };
			rc.fitAspectRatioInside(trc);
			//Driver->draw2DImageRect(Ctx->texMidRT[ev_bloom], trc, rc, 0, 0, false);
			video::SMaterial mr;
			mr.MaterialType = VkDrv->getMT(Fx2D_VertexColorAdd);
			mr.setTexture(0, Ctx->texMidRT[ev_bloom]);
			Driver->draw2DImageMr(mr, trc, rc);
			//Driver->saveTexture(Ctx->texMidRT[ev_bloom], "r:/Bloom.png");
		}
#if USERT2
		if (Ctx->texMidRT[ev_ui] && RT2Drawed)		Driver->draw2DImage(Ctx->texMidRT[ev_ui],0,0,true);
 
#endif

		if (drawPD && vp.texPD)   //pause demo
		{
			//static auto cl = Driver->addTexture( Ctx->mainViewSize(), "cloneRt");
			//texPD->copyTo(cl);
			Driver->draw2DImageRect(vp.texPD, core::recti(0, 200, Ctx->mainViewSize().Width / 4, 200 + Ctx->mainViewSize().Height / 4), vp.texPD->getRectI(), 0, 0, true);
		}
#if VK_ENABLE_RAYTRACING
		VkRaytracingDemo::TestRtPm pm;
		pm.width = Ctx->getViewSize(0).Width;
		pm.height = Ctx->getViewSize(0).Height;
		pm.skyTexture = gDomeTex;
		
		// Get active camera for comprehensive data extraction
		auto camera = SceneManager->getActiveCamera();
		pm.viewMatrix = camera->getViewMatrix();
		pm.projMatrix = camera->getProjectionMatrix();
		
		// Extract camera position and vectors
		pm.cameraPos = camera->getAbsolutePosition();
		pm.cameraDir = camera->getTarget() - camera->getAbsolutePosition();
		pm.cameraDir.normalize();
		pm.cameraUp = camera->getUpVector();
		pm.cameraRight = pm.cameraDir.crossProduct(pm.cameraUp);
		pm.cameraRight.normalize();
		
		// Extract camera parameters
		pm.nearPlane = camera->getNearValue();
		pm.farPlane = camera->getFarValue();
		pm.fovY = camera->getFOV();  // FOV in radians
		pm.time = static_cast<f32>(gSceneTime);  // Use scene time
		
		auto demo = VkDrv->getRaytracingDemo(pm);
		if (demo)
		{
			testRaytracingWithScene(VkDrv, SceneManager);
			auto tex = demo->getOutputTexture();
			Driver->draw2DImage(tex);
		}
#endif
		if (drawDepth && vp.depthRT) {
			Driver->draw2DImage(vp.depthRT, { 0,0 });// , 0, 0, SColor(0xFF000000));
			if (depthRenderFw)
				IFppt->render();

		}


#if HAS_SHADOWMAP 
		if (SHADOW_MAP_DEBUG) {
			auto rc = Driver->getTexSdMap()->getRectI(); rc.LowerRightCorner /= 4;
			mDriver->draw2DImage(Driver->getTexSdMap(), rc, 0);
		}

		//if (arRoot->curArSn)		snLight->getShadowLightCam()->setTarget(arRoot->curArSn->saba->getAbsolutePosition());

#endif
		//save depth moved
#if MMD_CONTROL_SD
		if (drawOpenPoseFrames > 0)
		{
			drawOpenPoseFrames--;
			if (opRT) {
				Driver->draw2DImage(opRT, { 0,0 });
			}
			// openPoseDraw();
		}
#endif
		
 
		if (hdrDraw)
		{
			Driver->setRenderTarget(nullptr, 1, 1, 0);
			Driver->draw2DImage(Ctx->texMidRT[ev_hdr]);
		}
#if DRAW_SHADER_TOY_BG

#elif DRAW_SHADER_TOY_SCENE_BG
		static ITexture* texMip = Driver->addTexture(Ctx->texMidRT[ev_main]->getSize(), "<GenMip>tex");
		//VKTEXTURE_CAST(Driver->getSysRT())->setHasMipMap(true);
		Ctx->texMidRT[ev_main]->copyTo(texMip);
		texMip->regenerateMipMapLevels();
		 
		Eqv->SdToy->drawImage(texMip);
 
#endif

		 
	}

	if (vp.stage() == 1 && !vp.paused) {
		vp.mediaTime += frameTimeS;
		vp.renderTime += 1.0f / APP_FPS;//frameTimeS;
	}


	//DP(("render ----------------------------"));
	
	SceneManager->setActiveCamera(Ctx->getActiveCam());

}

void AppMainAMP::eqvRecreate()
{
	{
		UP_LOCK_GUARD(lockGenText);
		if (FwMan.HasExtEmbs()) {
			g.FwInited = false;
		}
		txtBaseHue = 0;
	 	CPU_COUNT_B(eqvRecreate);
		static bool first = true;
		if (first  )
		{
			first = false;
			initObjectsInBeginScene();//write SceneManager


			//SceneManager->addSkyDomeSceneNode(Ctx->getDriver(1)->getTexture(arRoot->Cs.skyDomePath.c_str()));
#if SKY_DOME
			arRoot->Cs.skyDomeR = -CAMFAR  *10.f ;
			snSkyDome = SceneManager->addSphereSceneNode(arRoot->Cs.skyDomeR, 50, 0,SKY_DOME_FLOOD?-2000:0);			//snSkyDome->setVisible(false);
#if IRR_DRAW_MIRROR
			snSkyDomeM = SceneManager->addSphereSceneNode(-arRoot->Cs.skyDomeR, 50,0,-2000);	// id<-1000:half		snSkyDomeM->setVisible(false);
#endif
			auto T1 = std::async(std::launch::async, [&]() {
				snSkyDome->setMaterialFlag(EMF_LIGHTING, false);
				snSkyDome->setMaterialType(EMT_TRANSPARENT_ALPHA_CHANNEL_NoDepthWrite);
				snSkyDome->setMaterialTexture(0, gDomeTex=Ctx->getDriver(1)->getTexture(arRoot->Cs.skyDomePath.c_str()));// bedroom.png"));// hayloft.jpg")); //
				snSkyDome->setRotation({ 0,arRoot->Cs.skyDomeRttY,0 });
				if (!IRR_DRAW_MIRROR || !snSkyDomeM) return;
				snSkyDomeM->setMaterialFlag(EMF_LIGHTING, false);
				snSkyDomeM->setMaterialType(EMT_TRANSPARENT_ALPHA_CHANNEL_NoDepthWrite);
				snSkyDomeM->setMaterialTexture(0, Ctx->getDriver(1)->getTexture(arRoot->Cs.skyDomePath.c_str()));// bedroom.png"));// hayloft.jpg")); //
				snSkyDomeM->setRotation({ 0,arRoot->Cs.skyDomeRttY,0 });
				 
				});
#endif
			auto T2 = std::async(std::launch::async, [&]() {
				CPU_COUNT_B(CreateEQV);
				FwMan.init();
				if (!Eqv->CreateEQV(Ctx->getDataFilePath("style.eqvdef"), 32))
					throw "error";


				{	//misc init 
					Ctx->gd.Light[0] = snLight;

#if USE_LEAP
					leap->root->setParent(arRoot);
#endif
					IAnimatedMesh* msGrid{};
					if (Ctx->isApp(APPID_ArDatMovie) || Ctx->isApp(APPID_WinTest))
					{
						arRoot->Cs.pickPhysics = true;
						snGrid = new irr::scene::CLineGridSceneNode(101, 101, 100.f, arRoot, SceneManager);
						snGrid->drop();
						snGrid->setMaterialFlag(EMF_LIGHTING, false);
						snGrid->setMaterialDiffuse(0x80808080);
						snGrid->setPickData(EPD_GroundGrid);
						if (FORCE_LAND_GRID) arRoot->Cs.showGroundPlane = 1;
						snGrid->setVisible(arRoot->Cs.showGroundPlane);

					}

					//Ctx->gd.RootSn = arRoot;
					if (ONLY_RENDER_FW)		arRoot->setVisible(false);

				}

				CPU_COUNT_E(CreateEQV);

				
				});

			 
			initsBeforePPT();
			 
			InitFwPPT();

			
			T2.get();
#if SKY_DOME
			T1.get();
#endif


		}
		else {
			FwMan.init();
			InitFwPPT();
			if (!Eqv->CreateEQV(Ctx->getDataFilePath("style.eqvdef"), 32))
				throw "error";
		}
		
	 	CPU_COUNT_E(eqvRecreate);
		Eqv->SetPPT(IFppt, IFppt);

		if (IFppt) {
			//mTxtFwX = -1;
			Eqv->clearFws();
			IFppt->SetFwMrCb(FwMan);
			IFppt->SetGravityAdd(core::vector3df(0, 0, 0));
		}
#if USE_IFPPT_TXT
		if (IFpptTxt) {
			//mTxtFwX = -1;
			IFpptTxt->ResetParticles();
			IFpptTxt->SetFwMrCb(FwMan);
			IFpptTxt->SetGravityAdd(core::vector3df(0, 0, 0));
		}
#endif
#if USE_FFMPEG
		vp.mFF->sortSubtitle(Eqv);
#endif
#if APP_HAS_CLOCK
		clockLastTimeS = L"XXXXXXXX";
#endif
#if SVG_MMD_WRITE
		if (svgPm.texts.size() > 0) { irr::SEvent::SKeyInput ke2{}; ke2.Key = KEY_KEY_1;	StageOnKeyEvent(1, ke2); }
#endif
	}
	Driver->ClearCC = 1;
	Driver->ClearOnBegin = 1;
}

void AppMainAMP::initsBeforePPT()
{


	Eqv->onPtrFwIdChanged = [this]() {
#if HAS_MIDI

		Ctx->Midi.setFwNoteInst(0, Eqv->mTfpm.soundInst);
		for (int i = 0; i < 6; i++)
			Ctx->Midi.setFwNoteInst(10 + i, Eqv->mTfpm.soundI10_15[i]);
#endif
		};
#if DRAW_BG_IMG
	bgImg = Driver->getTexture("data/bg.png");
#endif

	{
		if (PLAY_AUDIO_BEAT.size())
		{
			beatMan.loadBeatTrack(PLAY_AUDIO_BEAT);
		}
		else if (beatMan.useAubio) {
			beatMan.parseBeat(PLAY_AUDIO_FILE);
		}
	}


#if USE_JOLT
	mmd->phyType = EPhysicsEngine::Jolt;
#endif
#if MMD_MOUTH_FROM_LYRIC 
	Eqv->onSetSpeechId = [this](int sid, float dur) {
		arRoot->forEachArChild([=](SnArItem* sn) {
			if (sn->saba)
				sn->saba->setSpeechId(sid, dur);
			});
		};
#endif


	drawMirror =  IRR_DRAW_MIRROR;// && !SKY_DOME;
	//Draw Mirror Scene

	//mmd->Pm.spm.snMmdRoot = mmd->RootNode = arRoot;

#if DRONE_AR
	mmd->RootNode->setPosition({ 0,-500,2500 });
#endif
	//Eqv->initComponents(mmd);


	//vpInit
	vp.mFF = new UaFFmpegManager(Ctx, Eqv);
	vp.Ctx = Ctx;
	vp.Eqv = Eqv;
	vp.Driver = Driver;
	vp.DrvOfs = DrvOfs;
	vp.mLib = mLib;
	vp.gameStage = this;

	vp.isExtDev = false;
	vp.composeFrame = false;
	vp.cbSetAnimating = [this](bool ani) { if (mmd) mmd->updating = ani; };
	vp.cbStopRecord = [this]() { stopRecord(); };


	auto [w, h] =  Driver->getScreenSize();
	Ctx->setViewState(0, 1, core::recti(0, 0, w, h), w, h);

	VkDrv->makesureDuDv(0, Driver->dsd.drvMidTexFormat, Ctx->getViewSize(0));
}
 



void AppMainAMP::renderSubview()
{
	
	bool hdrDraw = Ctx->gd.toneForHdr && Driver->getUseTexRT();

#if MULTI_VIEW_COUNT>=2
		if (mmd->hasBloom) throw "conflict"; // todo move bloom to higher id
		//auto oldcam = SceneManager->getActiveCamera();
		lockItViewId = ev_fpv;
		int viewId = lockItViewId;
		if (Ctx->getViewState(viewId)) // TODO
		{
		 
			updateFpvCamOnSaba();

		}
		//SceneManager->setActiveCamera(oldcam);

#endif
	
		if (Ctx->useMidRT)
		{
			int viewIds[2] = { ev_main, ev_fpv };
			int drawViewCount = Ctx->getViewState(ev_fpv) ? 2 : 1;

			for (int dvi = 0; dvi < drawViewCount; dvi++)
			{
				int rtvId = viewIds[dvi];
				Ctx->ensureMidRT(rtvId, 1, Ctx->getViewSize(rtvId), Driver->dsd.drvMidTexFormat);
				auto viewRT = Ctx->texMidRT[dvi == 0 ? ev_main : ev_fpv];
				bool useFpv = !Ctx->vrOn && Ctx->gd.CamFpv && (Ctx->vrOn || (MULTI_VIEW_COUNT == 1 || rtvId == ev_fpv));
				auto viewCam = SceneManager->getActiveCamera();
				if (useFpv) SceneManager->setActiveCamera(viewCam = Ctx->gd.CamFpv);
				viewCam->renderVR = Ctx->vrOn;
				viewCam->isVRLeftEye = dvi == 0;
				if ((!Ctx->gd.debugScene || 1 || !IS_WIN_DBG) && drawMirror && SceneManager->getActiveCamera()->getAbsolutePosition().y > 0) // TODO
				{

					if (drawMirror && (!VkDrv->texMrRT[dvi] || VkDrv->texMrRT[dvi]->getSize() != Ctx->texMidRT[Ctx->mainViewId]->getSize())) {
						DP(("ssssssssssss"));
						VkDrv->makesureDuDv(dvi, Driver->dsd.drvMidTexFormat, Ctx->getViewSize(rtvId));
					}

					Driver->setRenderTarget(VkDrv->texMrRT[dvi], true, true, 0);//reflection image
					viewCam->setAspectRatioOnRTSize();
					static glm::mat4 mirmat(1.0f, 0, 0, 0, 0, -1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1);
					viewCam->setViewMatrixAffector(*(core::matrix4*)&mirmat);

					viewCam->updateMatrices();
					VkDrv->resetFrameCache();

					if (snSkyDome) snSkyDome->setVisible(false);
					SceneManager->drawPassType(IrrPassType_Mirror);
					if (snSkyDome) snSkyDome->setVisible(true);

					VkDrv->UploadSharedBuffers();
					viewCam->setViewMatrixAffector(core::matrix4());

					VkDrv->flush();
					//Driver->saveTexture(VkDrv->texMrRT[dvi],  "r:/mirror.png");
				}


				Driver->setRenderTarget(viewRT, true, true, BG_COLOR, MULTI_THREAD_RENDERING);
				viewCam->setAspectRatioOnRTSize();
				if (drawBG) drawBgVideo(Driver);

				if (drawMirror && viewCam->getAbsolutePosition().y > 0) {
					VkDrv->resetFrameCache();

					if (snSkyDome) {
						snSkyDome->render();
					}
#define MIRROR_DIFFUSE (SKY_DOME&& !SKY_DOME_FLOOD?0x30808080:0xFFCCCCCC)//0x80808080
#define MIRROR_DIFFUSE_BLUR (SKY_DOME&& !SKY_DOME_FLOOD?0xFFCCCCCC:0xFFCCCCCC) //0x303F3F3F //0x30202020 //
					video::SMaterial mr;
					if (FW_DRAW_WATER)  //water surface
					{
						mr.MaterialType = VkDrv->getMT(Fx2D_VertexColorDuDv);
						mr.setTexture(0, VkDrv->texMrRT[dvi]);
						mr.setTexture(1, VkDrv->texDuDv[dvi]);
						Driver->draw2DImageMr(mr, MIRROR_DIFFUSE);//water effect
					}
					else  //only mirror fw
					{
						mr.MaterialType = VkDrv->getMT(
							IRR_MIRROR_BLUR ? Fx2D_Blur :
							IFppt->Pm.blendMode ? Fx2D_VertexColor : Fx2D_VertexColorAdd
						);
						if (IRR_MIRROR_BLUR) {
							CbMr2D* cb = VkDrv->getCB(Fx2DIdEnum::Fx2D_Blur); cb->blur.setParam(2, 2, 1.f);

						}
						mr.setTexture(0, VkDrv->texMrRT[dvi]);
						mr.setTexture(1, nullptr);
						Driver->draw2DImageMr(mr, IRR_MIRROR_BLUR ? MIRROR_DIFFUSE_BLUR : MIRROR_DIFFUSE);
					}
					drawSsaoRT();

					if (snSkyDomeM) snSkyDomeM->setVisible(false);
					if (snSkyDome) snSkyDome->setVisible(false);
					mmd->renderingByCamSb = useFpv && lockIt == 1 && !CAM_MMD_SELFIE;
					SceneManager->drawAll();
					mmd->renderingByCamSb = false;
					if (snSkyDomeM) snSkyDomeM->setVisible(true);
					if (snSkyDome) snSkyDome->setVisible(true);
					if (useFpv) SceneManager->setActiveCamera(Ctx->gd.CamRtt);
					VkDrv->UploadSharedBuffers();
					VkDrv->flush();
				}
				else {
					drawSsaoRT();

					SceneManager->drawAll();
				}



#ifdef _DEBUG   		//dudv texture debug view
				//if (VkDrv->texDuDv[0] && FW_DRAW_WATER)			Driver->draw2DImage(VkDrv->texDuDv[0], 0, 0); //debug:display dudv tex
#endif
			//SMaterial sm; sm.MaterialType = EMT_LINE;
			//Driver->setMaterial(sm);
			//Driver->draw3DLine({ 0,0,0 }, { 0,1000,0 });






				viewCam->renderVR = false;

			}//for dvi

			//Ctx->setViewCamera(Ctx->getActiveCam(), 0);
#if HAS_ARCORE_SHARETEX
			if (texAHB)
			{
				Driver->draw2DImage(texAHB);
				if (toSaveAHB > 0)
				{
					toSaveAHB--;
					Driver->saveTexture(texAHB, Ctx->getDataFilePath("ahb.png"));
				}
			}
#endif

		}

}

bool AppMainAMP::updateFpvCamOnSaba()
{
	isRenderingFpv = false;
	//if (lockIt != 1) return false;
	if ((MULTI_VIEW_COUNT<2 || Ctx->getViewState(lockItViewId)) && lockOnSb && Ctx->gd.CamFpv)
	{
		int USE_RTT_CAM_TARGET = CAM_DYN_RB ;
		ICameraSceneNode* cam = Ctx->gd.CamFpv; 

		auto ndCam = lockOnSb->ndCamPos; //lockOnSb->setVisible(false);
		//lockOnSb->ndHead->rb0->ReflectGlobalTransform();
		Ctx->gd.usingCamRbMat = CAM_DYN_RB;
		matrix4 mh = lockOnSb->mmdBaseMat * (
			Ctx->gd.usingCamRbMat ? Ctx->gd.camRbMat:
			ndCam ? ndCam->GetGlobalTransform() :
			(CAM_MMD_SELFIE? lockOnSb->ndUpper2: lockOnSb->ndHead)->GetGlobalTransform()
			//lockOnSb->ndHead->rb0->getNodeTransform()
			);
		auto rtt=mh.getRotationDegrees();
		matrix4 m = Ctx->gd.usingCamRbMat ? mh : (mh * (ndCam ? glm::mat4(1)
			: glm::translate(glm::mat4(1), lockOnSb->ndCtr->absScale* float3(0, MULTI_VIEW_COUNT > 1 ? .7:0.5, MULTI_VIEW_COUNT>1? (CAM_MMD_SELFIE?-2.6: - 0.89f) : -1.2f))));  //(0, 3,3.97)
		// sbFw2("sw2", glh::matTransformVec(lockOnSb->mmdBaseInv,m.getTranslation()), {}, 0xFFFF0000);
 
		vector3df up(0, 1, 0); m.rotateVect(up);
		matrix4 t = mh; 
		cam->setUpVector(//USE_RTT_CAM_TARGET ? Ctx->gd.CamRtt->getUpVector() : 
			up);
		if (Ctx->gd.usingCamRbMat) {
			t = t * glm::translate(glm::mat4(1), float3(0, 0,100)); 
			//cam->bindTargetAndRotation(false);
			cam->setTarget(USE_RTT_CAM_TARGET? Ctx->gd.CamRtt->getTarget() :  t.getTranslation());			 
			//sbFw2D("sw2", cam->getTarget()/MMD_SABA_SCALE, {}, 0xFFFF0000);
		}
		else if (CAM_MMD_SELFIE) {
			t = t * glm::translate(glm::mat4(1), float3(0, 50, 100)); cam->setTarget(t.getTranslation());
			isRenderingFpv = true;
		}
		else{ 
			t = t * glm::translate(glm::mat4(1), float3(0, 0, -100)); cam->setTarget(t.getTranslation());
			isRenderingFpv = true;
		}
			
		cam->setPosition(m.getTranslation());
		//sbFw2d("sw2", m.getTranslation()/100.f, {}, 0xFFFF0000);
		assert(cam->getTargetAndRotationBinding());
		cam->updateTransform();
		cam->updateMatrices();

		return true;
	}
	return false;
}

void AppMainAMP::lockCamUpdate()
{
	if (lockIt) {
		//lockOnSb = curSaba(); moved to .. , ai not change, non ai change
		lockOnPo = sb0->Pom->getLastVisibleObj() ? sb0->Pom->getLastVisibleObj()  : nullptr;
		
		glm::vec3 tgt, tgtDir{1,0,0}; bool setTgt = true;
		if (lockIt == 1 && lockOnSb) {
			auto nd = lockOnSb->ndUpper2 ? lockOnSb->ndUpper2 : lockOnSb->ndRoot;
			tgt = lockOnSb->mmd2irr(nd->getGlobalPos());
			//sb0->mmdFw(2, "sw1s", nd->getGlobalPos(), {}, 0xFFFFFF00);
			if (lockRatio < 1.f) {
				lockRatio += 0.0333f;
				if (lockOnSbMorphSrc) tgt = glm::mix((float3)lockOnSbMorphSrc->mmd2irr(nd->getGlobalPos()), tgt, lockRatio);
				if (lockRatio >= 1.f) { if (lockOnSbMorphSrc) lockOnSbMorphSrc->drop(); lockOnSbMorphSrc = lockOnSb; lockOnSbMorphSrc->grab(); }
			}
			else if (lockOnSbMorphSrc != lockOnSb) 	lockRatio = 0.f;

		}
		else if (lockIt == 2 && lockOnPo) {
			tgt = lockOnPo->getGlobalPos()*MMD_SABA_SCALE;
			if (lockRatio < 1.f) {
				lockRatio += 0.0333f;
				if (lockOnPoLast) 
					tgt = glm::mix((float3)lockOnPo->getGlobalPos() * MMD_SABA_SCALE, tgt, lockRatio);
				if (lockRatio >= 1.f) {   lockOnPoLast = lockOnPo;   }
			}
			else if (lockOnPoLast != lockOnPo) 	lockRatio = 0.f;
			tgtDir = glm::quat(lockOnPo->GetGlobalTransform())*vec3(0,0,1);
		}
		else setTgt = false;


		if (setTgt )	if (Ctx->getCameraId() == 1 && lockIt == 1) { //RTT Camera
			auto cam = Ctx->gd.CamRtt;
			//sb0->mmdFw(2, "sw1s", sb0->irr2mmd(cam->getTarget()), {}, 0xFF0000FF);
			glm::vec3 t = vector3df(cam->getTarget());
			auto vel = lockOnSb->Rb0()->getLinearVel();
			float len = glm::length(vel);
			
			float rat = CAM1_MIX_RATE * Ctx->gd.camRttOnSbVel?6.f:5.f;
			if (lockOnSb && lockOnSb->ndCtr && !lockOnSb->ndCtr->phyAnim) {
				rat += 0.00001f * glm::clamp(glm::length2(lockOnSb->Rb0()->getLinearVel()), 0.f, 10000.f);
			}
			t=glm::mix(t, tgt, std::min(rat,0.5f));
			auto anm = Ctx->getCamTCAnimator();
			anm->setTarget(t);			
			if (lockOnSb && Ctx->gd.camRttOnSbVel)
			{


				if (len < 3.0f)
					return;
				float sm100 = glm::clamp(1 - len / 100.f, 0.f, 1.f);
				float sm30i = glm::clamp(  len / 30.f, 0.f, 1.f);
				auto qr = glh::directionToRotation(  vel)*	quat(vec3(piFloat/3.f*sm100,0,0));

				qr = glm::slerp(lastFollowCamQR, qr, lockOnSb->ndCtr->phyAnim?0.002f:0.05f* sm30i);
				lastFollowCamQR = qr;
				// Ensure quaternion is valid
				if (glm::length(qr) > 0.0f 
					//&& !glm::any(glm::isnan(qr))
					) {
					qr = glm::normalize(qr);
				}
				else {
					anm->tld.rtt = vec3(0.0f); // Fallback to safe value
					lastFollowCamQR = glm::quat(1, 0, 0, 0);
					return;
				}
				vec3 rtt; mat4 transformation = mat4(qr);
				glm::extractEulerAngleYXZ(transformation, rtt.y, rtt.x, rtt.z); 
				anm->tld.rtt = vec3(rtt.y, rtt.x, 0)*RADTODEG ;
				//DP(("rtt %f %f", anm->tld.rtt.x, anm->tld.rtt.y));
			}
			//if (lockOnPo->hasFollowCam) {
			//	//			 glm::quat qr=glm::quat(anm->tld.rtt*DEGTORAD);
			// //qr = glm::slerp(qr,  glm::quat(  mat4(lockOnPo->getAbsoluteTransformation())), CAM1_MIX_RATE);
			// //anm->setRotation(qr);
			//	// 
			//}
		}
		else //Normal Camera 
		{
			auto cam = Ctx->gd.CamFpv? Ctx->gd.CamFpv:SceneManager->getActiveCamera();
			glm::vec3 t= vector3df(cam->getTarget()); 
			
			if (lockIt == 1) cam->setTarget(glm::mix(t, tgt, CAM1_MIX_RATE/ cam->getFOV() * std::clamp(switchItemTime < gGameTime?1:switchItemTime-gGameTime,0.f,1.f)*10.f));
			cam->setUpVector({ 0,1,0 });
			bool flyingToSb = lockOnPo && lockOnPo->flyingToFlag == 1 && lockOnPo->age < lockOnPo->pm.p2pTime - gFrameTime * 1.5f
				&& glh::angleDegBetweenVec(lockOnPo->vel, (vec3(Ctx->gd.CamRtt->getTarget()) - vec3(Ctx->gd.CamRtt->getPosition())) / MMD_SABA_SCALE) < 90.f
				;

			if (camDisLock) {
				float dis = (cam->getTarget() - cam->getAbsolutePosition()).getLength();
				if (dis > 3790.f) {
					auto pos = glm::mix(vec3(cam->getPosition()), vec3(cam->getTarget()), CAM1_MIX_RATE);
					pos.y = std::max(pos.y, 100.f);				cam->setPosition(pos);
				}
				else if (dis < 2000.f)
				{
					auto pos = glm::mix(vec3(cam->getPosition()), vec3(cam->getTarget()), -CAM1_MIX_RATE);
					pos.y = std::max(pos.y, 100.f);				cam->setPosition(pos);
				}
			}
			if ((!SceneManager->isRigMode()) && lockIt == 2 && lockOnPo &&   lockOnPo->isflyingToSb && lockOnPo->mmdHitCount==0
				&& (lockOnPo->flyingToFlag == 0 || 
					(flyingToSb
					|| lockOnPo->flyingToFlag == 1 &&	Ctx->gd.timeMul== BULLETTIMEMUL || lockOnPo->age < lockOnPo->pm.p2pTime + gFrameTime * 15.5f
						)
					)
				
				) {
				if (flyingToSb && Ctx->gd.timeMul> BULLETTIMEMUL && lockOnPo->flyingToFlag == 1 &&
					lockOnPo->age> lockOnPo->pm.p2pTime - gFrameTime * 2.5f) 	sendKeyEvent(KEY_SPACE, 1, 0, 0, 0);
#if 0
				//if (lockOnPo->hasFollowCam) {
				// 	cam->setPosition( lockOnPo->followCamPos.getInterpolated(cam->getAbsolutePosition(), CAM1_MIX_RATE));
				//}
#else
				float rato = lockOnPo->age / lockOnPo->pm.p2pTime;
				float rat = std::clamp(rato, 0.f, 1.f);
				tgtDir = glm::fastNormalize(lockOnPo->vel*vec3(1,0,1) );
				glm::vec3 cp = tgt + tgtDir * (-(
					//1000-640*rat
					260.f
					));
				if (lockOnPo->flyingToSbNode)
				tgt = glm::mix(  tgt,lockOnPo->flyingToSbNode->getIfRbPos() * MMD_SABA_SCALE ,  glm::smoothstep( 0.99f,1.1f,rato)) ;

				cam->setFOV(Ctx->gd.CamRtt->getFOV() / (1.f + (Ctx->gd.timeMul == BULLETTIMEMUL?2.f:0.5f)* glm::smoothstep(0.99f, 1.1f, rato)));
				cam->setTarget(rat<0.1f?tgt:
					glm::mix(glm::vec3(cam->getTarget()), 
						tgt, std::clamp(0.3f- rat*0.3f,0.1f,0.2f)));
				if (flyingToSb) cam->setPosition(rat < 0.1f ? cp : glm::mix(glm::vec3(cam->getPosition()), cp, std::clamp(0.1f - rat * 0.2f, 0.05f, 0.1f)));
				cam->updateAbsolutePosition(); 
				cam->updateMatrices();
#endif
			}

		}
		else  if (Ctx->gd.CamFpv )
		{
			Ctx->gd.CamFpv->setPosition(glm::mix(vec3(Ctx->gd.CamFpv->getPosition()), vec3(Ctx->gd.CamRtt->getPosition()), 0.1f));
			Ctx->gd.CamFpv->updateAbsolutePosition();
			Ctx->gd.CamFpv->updateMatrices();
		}
		SceneManager->getActiveCamera()->OnAnimate(0);
 
		// SceneManager->getActiveCamera()->render();
	}
}


void gStopRecord() {
	gAppMain->stopRecord();
}



void AppMainAMP::testTextFw(bool show, std::wstring txt, u32 flag)
{
#define VFG_USE_CAM_MAT			1
#define VFG_CONNECT_MODE		0  //1 each  2 full each, 3 ground
	static int cc = -1; cc++;
	ualib::SubtitleData sd;

	// txt = std::to_wstring(hdrBit) + L" bit";
	sd.ws = txt.size() ? ualib::wcsReplaceAll(txt, L"\r\n", L"\n") : L"♪";// L"大威天龍";//L"镜花水月";

	sd.fsize = Eqv->getTfd()->fontSize;
	sd.startMs = 0;

	ualib::ass::AcTextRange rg;
	Eqv->dmFtTxtPm.charDurMs = 1250;
	int ofsMs = Eqv->dmFtTxtPm.beginWaitMs, timeDur = Eqv->dmFtTxtPm.charDurMs;
	if ((flag & 0x10))
	{
		rg.txt = sd.ws;
		rg.len = sd.ws.length();	rg.start = 0;
		rg.timeOfs = 0;
		rg.timeDur = timeDur;
		sd.ex.ranges.push_back(rg);
	}
	else
	for (int i = 0; i < sd.ws.length(); i++)
	{
		auto ch = sd.ws[i];
		if (ch == L' ' || ch == L'\n') continue;
		rg.txt = ch;
		rg.len = 1;	rg.start = i;
		rg.timeOfs = ofsMs;
		rg.timeDur = timeDur;
		ofsMs += timeDur;

		sd.ex.ranges.push_back(rg);
	}
	sd.sbid = mmd->sabas.size()? cc % mmd->sabas.size():0;
	sd.endMs = ofsMs;
	sd.fontId = Eqv->dmFtTxtPm.fontId; 
	sd.fsize = 300;
	sd.vfg.fwSpecular = 1;
	sd.vfg.hitCvt = 0; sd.vfg.baseMass = 1.f; sd.vfg.hitVelChgLimit = 100.f;  
	sd.vfg.antiCvtLimit = 1; sd.vfg.movHitLimit = 1;
	sd.vfg.flag = 2;
	sd.vfg.srcVelMode = MMD_ACTION_ANIM ? 1 : 1;
	sd.vfg.velOfs = float3(0, 0.17f, 0);
	sd.vfg.useCamMat = VFG_USE_CAM_MAT;
	sd.vfg.connectMode =   VFG_CONNECT_MODE;
	sd.vfg.baseNode = 0;// sb0->ndUpper2;;
	if (arRoot->curArSn && curChar()) Eqv->setFwMmdSrc(curChar());
	if (flag & 1) sd.hasMatrix = TEST_FW_MATRIX;
	if (flag & 2) {
		static float hue = 0; hue += 15;
		sd.color0 = SColorHSL(hue + 0, 100, 50).toSColor().color;
		sd.color1 = SColorHSL(hue + 15, 100, 50).toSColor().color;
	}
	Eqv->launchTextFw(sd, show, 1);

}

std::wstring AppNameSpace::AppMainAMP::reloadScene()
{
	std::string filePath;
	if (itScene) {
		itScene->remove();
	}
	if (!MMD_ZERO_FRICTION) {

		if (HAS_FLUID) {
			itSns[0] = itScene = loadSabaModel({ .filePath = "D:/MMD/ITEMS/0/boxLandS.pmx",//bigbowlB1.pmx",//swimPool.pmx",//guo.pmx",//glassCup.pmx",//
				//"D:/MMD/PMX/Y/akf/swjSkt.pmx",
			// .modelScale = true,.modelScaleVec3 = vec3(0.75),
			 .massMul = 1.f }, false, 0x101), itScene->sb->ndRoot->SetAnimationTranslate({ 0,0,0 });
			//itSns[1] = loadSabaModel({.filePath = "D:/MMD/ITEMS/0/guochanS.pmx", .modelScale = true,.modelScaleVec3 = vec3(0.75),
			//	.massMul = 1.f}, false, 0x1101), itScene->sb->ndRoot->SetAnimationTranslate({0,0,0});itSns[1]->sb->setAllDynRbActive(true); itSns[1]->sb->setPhyAnim(0, true, true,5);
			//itSns[1] = loadSabaModel({ .filePath = "D:/MMD/ITEMS/0/bigbowlB1.pmx", //.modelScale = true,.modelScaleVec3 = vec3(0.5),
		//.massMul = 1.f }, false, 0x101), itScene->sb->ndRoot->SetAnimationTranslate({ 0,0,0 });

		//	loadSabaModelScale({ .filePath = "D:/MMD/ITEMS/0/LOUDOU.pmx",			.massMul = 1.f }, 0.3f, false, 0x101)->sb->ndRoot->SetAnimationTranslate({ 0,39,0 });


		//	itSns[0]->sb->canSaveVmd = true; itSns[1]->sb->canSaveVmd = true;
		}
	}
	if (HAS_FLUID) {
		if (!mmd->fluidNode)	mmd->createFluid();
		//if (itScene) mmd->fluidNode->mmdNode= itScene->sb->ndRbRoot;
		mmd->fluidNode->setPosition({ 0, 0, 0 });
	}

	if (0)
	{
		saba::PMXFileCreateParam cp{ .filePath =  "D:/MMD/ITEMS/Toy/motianlun.pmx",//
	//"D:/MMD/ITEMS/Toy/waterMillFix.pmx",
	.massMul = 1.f };
		int N = 2; float sc = 1.5f;
		cp.massMul = 10;
		cp.modelScale = 1; cp.modelScaleVec3 = vec3(sc);
		cp.duplicateAndRotatePass = 1;
		cp.copyCount = N - 1;
		cp.copyTrsInc = vec3(0, 0, 0);
		cp.copyRttInc = vec3(360.f / N, 0, 0);// vec3(0, 360 / (cp.copyCount + 1), 0);				
		cp.skipMaterialCopy = 1;
		cp.skipFirstNbones = -1;
		cp.copyUvOfs = 1;
		itScene = loadSabaModel(cp, false, 0x1101);  itScene->sb->ndRoot->SetAnimationTranslate({ 0,0,0 });
	}
#if 0
	filePath = "D:/MMD/PMX/Y/wls/hb.pmx";// "D:/MMD/ITEMS/petal/petalCopyBase.pmx";
	PMXFileCreateParam cp(filePath); 
	//cp.initPos = vec3(0, 5, 0);
	cp.duplicateAndRotatePass = 1;
	cp.copyCount = 2;
	cp.copyTrsInc = vec3(0, 0.02, 0);
	cp.copyRttInc = vec3(2, 60,  0);// vec3(0, 360 / (cp.copyCount + 1), 0);
	cp.copyScaleInc = vec3(-0.01);
	cp.skipFirstNbones = 1;
	itScene = loadSabaModelScale(cp, MMD_SCENE_SCALE, false, 0x101);
	//itScene->sb->Pmx->rt1Tr = vec3(0, 5, 0);
#endif
#define  MULTI_SCENE 0
	if (MMD_ZERO_FRICTION)
	{
		

		filePath =   "D:/MMD/ITEMS/0/slidePool"//slideBarLine" //slideHigh1"//slideBar"//bowl"//	slideTeleport1"	//	
			".pmx";
		PMXFileCreateParam cp(filePath);
		cp.createPhyMesh = true;
		cp.fixFriction = true; cp.setFixFriction = 0.01f;
		cp.frictionMul = 0;
		//cp.modelScale = 1; cp.modelScaleVec3 = vec3(0.8);
		if (!itScene) {
			//loadSabaModelScale({ .filePath = "D:/MMD/ITEMS/0/lodoHu.pmx",.massMul = 1.f }, 0.3f, false, 0x101)->sb->ndRoot->SetAnimationTranslate({ 0,2,0 });
			//loadSabaModelScale({ .filePath = "D:/MMD/ITEMS/0/LOUDOU.pmx",	.massMul = 1.f }, 0.3f, false, 0x101)->sb->ndRoot->SetAnimationTranslate({ 0,39,0 });
			//loadSabaModel({ .filePath = "D:/MMD/items/0//slideBarLineSCene.pmx",.massMul = 1.f }, false, 0x101)->sb->ndRoot->SetAnimationTranslate({ 0,0,0 });
			//mmd->sbTracker = loadSabaModelScale({ .filePath = "D:/MMD/items/0/slideBarSkirt.pmx",.massMul = 20.f }, 1, false, 0x101)->sb;
			////loadSabaModelScale({ .filePath = "D:/MMD/items/wan/0.pmx",.massMul = 2.f }, 3, false, 0x101)->sb->ndRoot->SetAnimationTranslate({ 0,0,0 });

		}
		{
			//itScene = loadSabaModelScale({ MMD_SCENE_FILE }, 2, false, 0x101);
			//sbScene = itScene->sb;
			
 
		}
		int count = MULTI_SCENE ? mmd->sabas.size() : 1; 
		{
			for (int i = 0; i < count; i++)
			{
				cp.forceReload = i == 0;
				if (itSns[i])
					itSns[i]->remove();
				itSns[i] = loadSabaModel(cp, false, 0x101);
				itSns[i]->sb->Pmx->rt1Rt = vec3(0, i * 2 * piFloat / count, 0);
				itSns[i]->sb->OnAnimate(0);
				if (!itScene) itScene = itSns[i];
			}
			for (size_t i = 0; i < mmd->sabas.size(); i++) {
				auto sb = itSns[MULTI_SCENE?i:0]->sb;
				sb->ndRbRoot->rb0->SetActivation(false);
#if 0
				initpsBasePos = mmd->sabas[i]->Pmx->rt1Tr = sb->findNode(L"startPos0")->getGlobalPos();
				auto ofs = sb->findNode(L"startPos0")->pmxBone->m_positionOffset;
				if (glm::length2(ofs) > 0.0001f)	initpsBaseRtt = mmd->sabas[i]->Pmx->rt1Rt = glm::eulerAngles(glh::directionToRotation(ofs));
				else initpsBaseRtt = mmd->sabas[i]->Pmx->rt1Rt = vec3(0, 90, 0) * core::DEGTORAD;
#endif
			}
		}
#ifndef _DEBUG
		cp.filePath = "D:/MMD/items/slide2jtree.pmx";
		cp.createPhyMesh = false; cp.fixFriction = false;
		// sb = loadSabaModel(cp, false, 0x101)->sb;
#endif
		auto t1 = "d:/mmd/scene/daxi/t/t.pmx";

		//auto tsb=loadPmxItem(t1, initpsBasePos + vec3{3,-20,0	}, {0,0,0},1,1.2);
		// loadPmxItem(t1, initpsBasePos + vec3{ -6,-34,-100 }, { 60,10,0 },1,2);
		//loadPmxItem("D:/mmd/scene/st/cht.pmx", initpsBasePos + vec3{ 0,-36,-100 }, { 0,180,0 },0,1.5);
		// loadPmxItem("d:/mmd/scene/daxi/t/t2.pmx", initpsBasePos + vec3{ 0,-69,-100 }, { 0,0,0 },1,1);
	}		
	else if (MMD_HAS_SCENE)
	{


		//auto t1 = "d:/mmd/scene/daxi/t/tbowl.pmx"; loadPmxItem(t1, { 0,0,0 }, { 0,0,0 });
		//auto t1 = "d:/mmd/scene/daxi/t/t.pmx";

		//loadPmxItem(t1, {-10,0,0	}, {0,0,0});		loadPmxItem(t1, { 0,1,	6	}, {-60,0,0});		loadPmxItem(t1, { 10,0,0	}, {0,180,0 });
		//loadPmxItem("D:/Tmp/sumo/platform.pmx", { 0,0,0 }, { 0,0,0 })->loadAnimation("D:/Tmp/sumo/platform.vmd");
		//loadPmxItem("d:/mmd/scene/daxi/t/t1.pmx", { 10,0,-60 }, { 0,180,0 });
		//loadPmxItem(t1, { -3,0,-40 }, { 0,0,0 });
		//loadPmxItem(t1, { 0,1,	30 }, { 60,0,0 });
		//loadPmxItem(t1, {3,0,-79 }, { 0,180,0 });

		//auto sb=loadPmxItem(t1, {0,0,0 }, { 0,0,0 });	
		//sb->Pmx->connectRb(sb0->ndHandL->rb0, sb->getRb0(), true, {}, true, {});
		//sb = loadPmxItem(t1, { 0,0,0 }, { 0,0,0 }); 
		//sb->Pmx->connectRb(sb0->ndHandR->rb0, sb->getRb0(), true, {}, true, {});

		//MAIN SCENE
#if 1
		filePath = MMD_SCENE_FILE;
		PMXFileCreateParam cp(filePath); cp.forceReload = 1;
		itScene = loadSabaModelScale(cp, MMD_SCENE_SCALE, false, 0x101);
		sbScene = itScene->sb;
#else
		{
			using namespace glm;
			PMXFileCreateParam pm{ filePath = "D:/MMD/PMX/Y/ntKqn/zdj.pmx" };

			itScene = loadSabaModel(pm, false, 0x101);
			auto sb = sbScene = itScene->sb;
			sb->Pmx->moveAllOffset(float3(0, 2, 0));
			auto nd = sb->findNode(L"floor");
			sb0->Pmx->connectRb(nd->rb0, sb0->ndHandL->rb0, true, { 2,8,0 }, false);
			sb0->Pmx->connectRb(nd->rb0, sb0->ndHandR->rb0, true, { -2,8,0 }, false);
			PMXJoint jt{};
			jt.limitMinT = vec3(-3, -3, -3);
			jt.limitMaxT = vec3(3, 3, 3);
			jt.setLocalPos = true;
			jt.springT = vec3(1.f);
			jt.springR = vec3(1.f);
			jt.dampingT = vec3(1.f);
			jt.dampingR = vec3(1.f);
			sb0->Pmx->connectRb(nd->rb0, sb0->ndCtr->rb0, false, false, jt);

			//sb0->Pmx->connectRb(sb0->ndHandR->rb0, sb->findNode(L"floor")->rb0, true, { -1,6,0 }, true);
		}
#endif
		//for (int x = 0; x < 5; x++)for (int y = 0; y < 5; y++) {
		//	auto s = loadSabaModelScale({ "d:/mmd/items/windmill/1.pmx" }, 2.f, false, 0x101)->sb;
		//	s->Pmx->GetNodeManager()->getRootNode()->SetAnimationTranslate({-60+ x * 37, 0, 25+x+y*27 });
		//}

		//auto s = loadSabaModel({ ITEM_BOX_OPEN }, false, 0x101);
		//s->sb->ndRoot->setAnimGlobalPos(float3(0, 0, 0));
		//s->sb->ndFrameRtt = s->sb->findNode(L"center");

		//loadPmxItem("D:/mmd/scene/st/cht.pmx", {0,0,1}, {0,0,0});
		//auto pool = loadPmxItem("D:/mmd/ITEMS/poolRB.pmx", { 0,0,0 }, { 0,0,0 }); 
		//loadPmxItem("D:/MMD/PMX/puren/yz.pmx", {0,0,0}, {0,0,0});
 
	}
	arRoot->setCurSn(it0);
	return ualib::AnsiToWchar(filePath);
}

 



void AppMainAMP::StageOnBackBufferResized(bool first)
{
#if MMD_CONTROL_SD
	InitCameraByBuffSize(first ? CAM_FOV : -1.f, 0.01f * MMD_SABA_SCALE, 1000 * MMD_SABA_SCALE);
#else
	InitCameraByBuffSize(first ? CAM_FOV : 1.f,0.1f * MMD_SABA_SCALE,CAMFAR);
#endif


}

void AppMainAMP::processVideo()
{

	if (editMode == 1 && vp.stage() != 0 && !stageExiting && vp.working && vp.ivpm.w>0)
	{


		//CPU_COUNT_B(GET);
		//if (vp.toSkip >= 0) throw;//to check vp.toSkip--;
		if (vp.vFrames.size() < 10 && (!vp.paused || vp.pausedPlayOneFrame)) {
			vp.pausedPlayOneFrame = 0;
			if (vp.mediaListId >= 0 && vp.hasVideo && !vp.mFF->getOneFrame(vp.mediaTime + 0.001f))
			{ //dec finished
				if (vp.vFrames.size() == 0)
				{
					if (editMode == 1 ||
						(editMode == 2 && vp.recTimeS > vp.curVFrame.timeS + vp.mFF->getMeidaFrameTime(0))
						)
					{
						vp.mediaListId = -1; stopRecord();
					}
				}
			}
		}

	}

	if (vp.stage() == 1) {

		if (vp.ivpm.w > 0 ) //  UPDATE
		{
			bool texUpdated = false, firstRecFrame = false;
			//while (vp.mediaTime + 0.001 > vp.recTimeS) ualib::UaYield();
			if (vp.mediaListId >= 0)
			{
				if (vp.renderId < 0)
				{
					firstRecFrame = true;
					vp.renderId = 0;
				}
				while (vp.stage() == 1)
				{
					while (vp.vFrames.size())
					{
						UP_LOCK_GUARD(vp.vfLock);
						auto& vf = vp.vFrames.front();
						if (vp.mediaTime + 0.001f > vf.timeS)
						{
							if (vp.curVFrame.tex) vp.vFramesToFree.push_back(vp.curVFrame);
							else vp.startVFrame = vf;
							vp.curVFrame = vf;

							vp.vFrames.pop_front();
							texUpdated = true;

						}
						else break;
					}
					//DP(("vp %d", vp.vFrames.size()));

					if (editMode != 2)
						break;

					if (vp.curVFrame.tex &&
						((vp.vFrames.size() && vp.mediaTime < vp.vFrames.front().timeS)
							|| (vp.curVFrameNeedToRender(frameTimeS))
							)
						)
						break;

					ualib::UaYield();
				}

			}
			Ctx->gd.texBg = vp.curVFrame.tex;



#if USE_AR_DATA

			static int lastid = -1;
			if (texUpdated && Eqv->MatRec->getCount() > 0) {

				if (!mediaStartHandled) {
					assert(vp.curVFrame.vfIdx == 0);
					mediaStartHandled = true;
					arRoot->onMediaStarted(editMode);
				}
				vp.curFrameIdx = vp.curVFrame.vfIdx;
				//DP(("CFI %d", vp.curFrameIdx));
				if (lastid == vp.curFrameIdx)
				{
					assert(0);
				}
				lastid = vp.curFrameIdx;
				if (firstRecFrame) {
					vp.endFrameIdx = Eqv->ARCamUpdate(-11, 0, 99999);
					vp.startFrameIdx = vp.curFrameIdx;
				}
				vp.arFrameIdx = Eqv->ARCamUpdate(
					Eqv->isColmapVideo ? -1 :  //  interpolate  by time
					//Eqv->MatRec->FullFrame?
					vp.curVFrame.timeS * Eqv->MatRec->fps * Eqv->timeDiv + 0.5f
					//:vp.curFrameIdx//warning: seek will not reset

					,
					0, vp.curVFrame.timeS + (COLMAP_MODEL ? Eqv->MatRec->timeOfs : 0),
					MMD_FORCE_VIDEOTIME || editMode == 2);



			}

			if (AR_PATH_ON_STEP)
				updateArPath();
			else if (USE_AR_DATA) {
				//updateArPath();
				{ //Run at Front

					arRoot->forEachArChild([=](SnArItem* sn) {
						if (!sn->sb || sn->sb->rootLocked || !sn->sb->onArPath || !Eqv->MatRec->rootNode) return;



						const MatRecData* a, * b; float ratio;
						int rid = Eqv->MatRec->getDataPtrUs(//(double(vp.curFrameIdx) / Eqv->MatRec->fps + std::max(0.1f,sn->mArTimeOfs )) 
							(vp.mediaTime + sn->mArTimeOfs)
							* 1000000,
							a, b, ratio);// ratio = core::clamp(double(int(ratio * Eqv->MatRec->fps+0.00001)) / Eqv->MatRec->fps,0.0, 1.0);
						if (rid < 0) return;
						DP(("ratio %f", ratio));
						core::matrix4 mt = a->mat, m, mr180;
						mt = mt.interpolateTR(b->mat, ratio);

						m.setTranslation(mt.getTranslation());
						m.setRotationDegrees(mt.getRotationDegrees());
						//sn->setMatrixTR(m);
						//mr180.setRotationDegrees({ 0, 180, 0 });
						m = sn->sb->mmdBaseInv * Eqv->MatRec->rootNode->getAbsoluteTransformation() * m;
						auto tr = m.getTranslation();
						auto rt = m.getRotationDegrees();
						auto pmx = sn->sb->Pmx;

						pmx->rootTr = tr;
						pmx->rootRt = glm::quat(rt * core::DEGTORAD);
						//DP(("Rd %d %f,%f,%f", rid,tr.X,tr.Y,tr.Z));
						//m.setScale(0.01);
						//sn->setMatrix(core::IdentityMatrix);		

						// sn->saba->Pmx->mmdRootMat=   m;

						});
					//SceneManager->getActiveCamera()->setParent(Ctx->gd.RootSn);

				}

			}

#endif

		}


		//SUBTITLE
		if ((vp.ivpm.w > 0 || vp.recMode==9) && vp.mediaTime * 1000 < vp.durationMs)
		{

			ualib::SubtitleData sd;
			const auto& curFw = Eqv->getCurPtrFw();
			while (vp.mFF->getSubtitle(int((vp.renderTime +/* ADD_SUB_OFS*/+0.0005f - curFw->tfp.timeOffset - SBT_DELAY + START_OFS) * 1000), sd))
			{
#if SBT_USE_STYLE_ID_ONLY 
				Eqv->setCurPtrFwId(sd.style - 1, 0);
#else
				if (sd.fwIdStr.size() > 0)
					Eqv->setPtrFwByIdStr(0, sd.fwIdStr);
#endif			
				//if (sd.fcStr.size() > 0)					onDamakuCmd(std::string("null|#") + sd.fcStr);

				sd.draw = ((sd.flag & SDF_IsMouth) == 0);

				sd.vfg.sbAtkMode = MMD_GRAB_CONNECT;
				sd.vfg.velOfs = float3(0, 0.27f, 0);
				sd.vfg.useCamMat = SBT_CAM_THROW;
				sd.vfg.srcVelMode = SBT_CAM_THROW;
				sd.vfg.connectMode = VFG_CONNECT_MODE;
				sd.vfg.baseNode = 0;// sb0->ndUpper2;;
				sd.vfg.fwSpecular = 1;
				sd.vfg.hitCvt = 0; sd.vfg.baseMass = 0.1f; sd.vfg.hitVelChgLimit = 100.f;  
				sd.vfg.antiCvtLimit = 1; sd.vfg.movHitLimit = 1;
				static int scc = 0;
				//sd.s.zOffset = (scc++ % 3)*50;
				//sd.vfg.flag = 2;
				if (arRoot->curArSn && curChar()) Eqv->setFwMmdSrc(curChar());
				sd.hasMatrix = TEST_FW_MATRIX;


				Eqv->launchTextFw(sd, true, 1);


			}

		}
#if PLAY_AUDIO && PLAY_AUDIO_SYNC
		static float lastT = Ctx->gd.time;
		if (Ctx->audioMan && Ctx->gd.time - lastT > 0.1f) {
			lastT = Ctx->gd.time;
			Ctx->audioMan->setFrequencyRatio(Ctx->gd.fpsReal / 60.f);
			Ctx->audioMan->playAt(vp.renderTime + 6.f / APP_FPS, 0.f / APP_FPS);
		}
#endif
	}
	else
	{
#if PLAY_AUDIO && PLAY_AUDIO_SYNC
		if (Ctx->audioMan && Ctx->gd.frameCount % 30 == 0) Ctx->audioMan->playAt(vp.mediaTime - 1.f / APP_FPS, 0.f / APP_FPS);
#endif
	} //(vp.stage() == 1) else

}

void AppMainAMP::drawBgVideo(irr::video::IVideoDriver* driver, irr::video::ITexture* tex, irr::video::SColor sc)
{
	if (SphereCamShot>=0) return;
#if HAS_DEPTHONLY_PASS
	SceneManager->drawPassType(IrrPassType_OnlyDepth);
#endif

	auto texbk = tex;
#if DRAW_BG_IMG
	texbk = bgImg;
#endif
	if (!texbk) texbk = vp.curVFrame.tex;// texVideoBk[0];
	if (vp.colMapId >= 0) {
		ColMapImage& cmi = vp.cmArr[vp.colMapId];
		texbk = cmi.tex;
		if (!texbk) cmi.tex = texbk = driver->getTexture(cmi.fn.c_str());
	}
	if (!vp.working && vp.fvAR.texVideo){
		auto t=vp.fvAR.texVideo;
		int w=t->getSize().Width;
		int h=t->getSize().Height;
		core::recti rc(0,0,h,w);
		//rc.fitAspectRatioInside(core::recti(0,0,Ctx->gd.scrWidth,Ctx->gd.scrHeight));
		driver->draw2DImageRect(t,rc,t->getRectI(), nullptr,nullptr,false,90);
	}
 
	//if (vp.writeFrameToFile && texbk && vp.recording && vp.renderId%vp.writeFrameToFileEveryNFrames ==0 && !drawBG) {
	//	UP_LOCK_GUARD(DrvOfs->dsd.driverLock);
	//	driver->saveTexture(texbk, ualib::strFmt("r:/imgbg/img%03d.jpg", vp.renderId/vp.writeFrameToFileEveryNFrames).c_str());
	//}
	//else 
	if (texbk && (vp.colMapId >= 0 || vp.stage() == 1 || DRAW_BG_IMG)) //Video Back
	{
		core::recti rc = texbk->getRectI(), trc{ {0,0}, Ctx->mainViewSize() };
		//Driver->saveTexture(texbk, "r:/1.png");
		const video::SColor temp[4] = { sc,			sc,			sc,			sc };
		//if (bool(Eqv->ScrRotation) != bool(Eqv->ARCamRotation))			rc.fitAspectRatioInside(trc);
		if (Eqv->ARCamRotation == 90 || Eqv->ARCamRotation == 270) std::swap(trc.LowerRightCorner.X, trc.LowerRightCorner.Y);
		float rx =  trc.getWidth() / 2;
		float ry =  trc.getHeight() / 2;
		trc.UpperLeftCorner = core::vector2di( Ctx->mainViewSize().Width / 2-rx,  Ctx->mainViewSize().Height / 2-ry);
		trc.LowerRightCorner = core::vector2di( Ctx->mainViewSize().Width / 2 + rx,  Ctx->mainViewSize().Height / 2 + ry);

		if (Eqv->ARCamRotation) {
		}
		else
			rc.fitAspectRatioInside(trc);
		driver->draw2DImageRect(texbk, trc, rc, 0, temp, true, -Eqv->ARCamRotation);


	}
 

	if (texWaterMark && drawWaterMarkCC >= 0 && drawWaterMarkCC < 90) {
		float ratio = drawWaterMarkCC / 90.f;
		core::recti rc = texWaterMark->getRectI(), rct;
		int sw = Ctx->gd.scrWidth, sh = Ctx->gd.scrHeight;
		rct.UpperLeftCorner.set((sw - rc.getWidth()) / 2, sh - 2 * rc.getHeight());
		rct.LowerRightCorner.set((sw + rc.getWidth()) / 2, sh - rc.getHeight());
		SColor sc = 0xFFFFFFFF; sc.setAlpha(sin(ratio * core::PI) * 255 + 0.5f);
		const video::SColor temp[4] = { sc,			sc,			sc,			sc };
		driver->draw2DImageRect(texWaterMark, rct, texWaterMark->getRectI(), 0, temp, true);
		drawWaterMarkCC++;
	}

}

void AppMainAMP::sendMsgTolib(int msg, int64_t pm1, int64_t pm2, int64_t pm3)
{
	switch (msg)
	{
	case 1001:
	{

	}
	break;
	default:
		break;
	}
}

bool AppMainAMP::StageOnPointerEvent(const irr::SEvent::SPointInput& pe)
{
	bool canIgnore = pe.act != EPA_Up  ;// pe.act == EPA_Down || pe.act == EPA_MouseWheel || (pe.ButtonStates & 1);
#if USE_IMGUI
	if (ImGui::GetIO().WantCaptureMouse && canIgnore && (pe.act!=EPA_Move ||  arRoot->snPickMap.find(0) == arRoot->snPickMap.end()) 
		)
		return true;
#endif
	//DP(("pe %d  %.0f,%.0f",pe.act, pe.X, pe.Y));
	float x = Ctx->gd.screenX + pe.X, y = Ctx->gd.screenY - pe.Y;
	//bool isLeftCtrlDown = Ctx->getEvtRcv()->IsKeyDown(irr::KEY_LCONTROL);
	//bool isLeftAltDown = Ctx->getEvtRcv()->IsKeyDown(irr::KEY_LMENU);
	bool isLeftShiftDown = Ctx->getEvtRcv()->IsKeyDown(irr::KEY_LSHIFT);
 
	bool ret = false;
	//if (!Ctx->scenePaused)
	ret = mmd->onPointerEvent(pe);
	if (ret && canIgnore) return true;

	ret = arRoot->onPointerEvent(pe);
	if (Eqv->ptrGrassMode) {
		Eqv->StageOnPointerEvent(pe);
		return true;
	}
	else if (Eqv->ptrControlHand) {
		if (pe.act == EPA_Move || pe.act == EPA_HoverMove)
		{
			if (it1 ) {
				auto sb = sb1;
				static int id = sb->Pmx->GetNodeManager()->FindNodeIndex(L"右ひじ");
				float dx = (pe.X - pe.lastX);
				it1->setAddRttNode(id, -vector3df(pe.Y - pe.lastY, pe.Shift ? dx : 0, pe.Shift ? 0 : (pe.X - pe.lastX)) / 10, false);
				return true;
			}

		}
	}
#ifdef _WIN32
	if (pe.act == EPA_MouseWheel)
	{
		curPtR += pe.Wheel / 12;
		//snPtBall->setScale(curPtR);
	}
	else if (pe.act == EPA_Down)
	{

	}
	else if (pe.act == EPA_Move)
	{

		if (vp.cmArr.size() > 0 && pe.isMouseBtnDownWhenMove(EMBSM_LEFT)) {

			 
				auto rtt = vp.cmRoot->getRotation();
				if (isLeftShiftDown) {
					rtt.Z += (pe.X - pe.lastX) / 10;
				}
				else {
					rtt.Y += (pe.X - pe.lastX) / 10;
				}

				rtt.X += -(pe.Y - pe.lastY) / 10;
				vp.cmRoot->setRotation(rtt);
				if (vp.colMapId >= 0)	vp.cmArr[vp.colMapId].snCam->updateTransform();
				DP(("RTT X %f", rtt.X));
			
			
		}
	}
	if (snWater && pe.btnId == 0 && pe.act == irr::EPA_Down)
		snWater->addWaveSource(Ctx->gd.screenX + pe.X, -Ctx->gd.screenY + pe.Y, 1);
#endif
	if (Ctx->getEvtRcv()->curPointerCount() > 1) 
		return true;//do not send to default receiver ( Scene Node Animators )
	return ret;
}


void AppMainAMP::onKeyUpdate()
{
	
	auto er = Ctx->getEvtRcv();
	auto ctrl = er->IsKeyDown(KEY_LCONTROL);
	auto shift = er->IsKeyDown(KEY_LSHIFT);
	//auto alt = er->IsKeyDown(KEY_LMENU);  //always false?
	arRoot->forEachArChild([&,this](SnArItem* sn) {
		auto sb = sn->sb;
		if (!er->IsKeyDown(KEY_LCONTROL) && sn->sb != curChar()) return;
		if (er->IsKeyDown(KEY_NUMPAD0)) {
			sb->ndUpper2->scaleVel(0.1f, 3, 6); sb->centerForceMul = 1.f;
			glm::mat4 mTurn(1);
			//sb->ndCtr->rb0->setRotateTo(mTurn);
		}
#if SBATK_CATCHER
		bool chS, chD;
		bool isS = er->IsKeyDown(KEY_KEY_S, &chS);
		bool isD = er->IsKeyDown(KEY_KEY_D, &chD);
		if ( isS || isD) {
			if (isS && chS) sb0->actVoiceFx(0, 10, 60,L"'a");
			if (isD && chD)
				sb0->actVoiceFx(0, 11, 60,L"ha aaaaa");
			if (sb->charAtk.legGrab)
			{
				sb->charAtk.legGrabRat = std::clamp(sb->charAtk.legGrabRat + (!isD ? 0.5 : -0.5), 0.0, 1.0);
				sb->Pm.mmd->mdplr.aouStart(sb->getItemIdx(),10, UaRand(3), false);
			}
			else
			{
				sb->springMode = -1;
				//if (er->IsKeyDown(KEY_LSHIFT)) sb->ndCtr->rb0->addLinearVel({ 0,-30,0 });
				if (isD) { sb->springMode = sb->springDefMode; sb->springMul = -springMul; }
				else {
					sb->springMode = sb->springDefMode; sb->springMul = springMul;
				};
			}
		}
#endif
		});

	  {
		bool c7, c9;
		bool is7 = er->IsKeyDown(KEY_NUMPAD7, &c7);
		if (is7 || er->IsKeyDown(KEY_NUMPAD9, &c9))
		{
			mmd->curCtrlSb([=](auto* sb) {
				if (is7 && c7 || !is7 && c9) {
					mmd->mdplr.aouStart(0, 0, 0, false);
				}
				//auto sb = curSaba();// if (!sb->isAiCharacter()) sb = sb0;
				MMDNode* node = sb->ndRoot;// findNode(L"头饰_1");
				if (Ctx->getEvtRcv()->isMouseKeyDown(0) && arRoot->curPickNode.getSbNode()) node = arRoot->curPickNode.getSbNode();

				double sc = node->GetScale().x;
				sc *= is7 ? 1 / 1.05 : 1.05;
				sc = std::clamp(sc, 0.1, 20.0);
				node->SetScale({ sc,sc,sc }); if (sc > 1.f) snLight->setPosition(Ctx->gd.baseLightPos * MMD_SABA_SCALE * sc);
				if (ctrl) {
					sb->ndUpper->SetScale(vec3(1 / pow(sc,0.3))); sb->ndUpper2->SetScale(vec3(1 / pow( sc, 0.7)));
					//sb->ndNeck->SetScale(vec3( pow(sc, 0.3))); sb->ndHead->SetScale(vec3( pow(sc, 0.7)));
				}
				});

		}
	}
	bool c1, c3;
	bool is1 = er->IsKeyDown(KEY_NUMPAD1, &c1);
	if (is1 || er->IsKeyDown(KEY_NUMPAD3, &c3))
	{
		 sb0->handFwSpeed = core::clamp(sb0->handFwSpeed*(!is1 ? 1.1f : 1/1.1f),1.f,1000.f);
		 
	}
 
	if (er->IsKeyDown(KEY_KEY_F))
	{

		//sb0->ndHandR->rb0->addLinearVel({ 0,10,0 });
		arRoot->forEachArChild([=](SnArItem* sn) {			
			if (sn->sb->objId) return;
			//sn->sb->phyObjForceAttack({ ctrl,  float3(0,2,0), objCenterForceMul });
			if (sn->sb->tsb) {
				sn->sb->ndArm1L->rb0->addLinearVel({ 0,20,0 });
				sn->sb->ndArm1R->rb0->addLinearVel({ 0,20,0 });
			}
			else if (ctrl || shift) if (sn->sb->Pmx->isCharacter){
				auto m = (shift?sn->sb->ndHead->rb0:sn->sb->ndLower->rb0)->GetTransform();
				vec3 v(0, shift?1:-1, 0); v = glh::matRotateVec(m, v*2.f);
				if (ctrl && shift) {
					v = vec3(0, 10, 0);
					if (MMD_LOOKAT_SB0 && sn == it0) return;
				}
				//if (MMD_HAS_WHEEL) sn->sb->Rb0()->addLinearVel(v*10.f);		else 
					sn->sb->Pmx->addBodyVel(v, 1);
			}
			//	else sn->sb->Pmx->addBodyVel({ 0,20,0 },1);							
			}, 0x11);			
	}
	if (lockIt && lockOnSb ) {
		bool W = er->IsKeyDown(KEY_KEY_W), S = er->IsKeyDown(KEY_KEY_S), A = er->IsKeyDown(KEY_KEY_A), D = er->IsKeyDown(KEY_KEY_D);
		if (W || S || A || D) {
			auto m = ctrl?lockOnSb->ndUpper->rb0->GetTransform(): mmd->camMat;
			
			 
			if (ctrl)
			{
				float v = shift ? 2 : 1;
				auto rb = lockOnSb->ndCtr->rb0;
				vec3 axis = glm::fastNormalize(rb->getPosition()-mmd->camPos); 
				vec3 torque = { 0,0,0 };
				if (A || D) 		torque = vec3(0, 0, (A ? 100 : -100) * v);				
				if (W||S) torque = vec3((S ? 100 : -100) * v, 0, 0);
				rb->addTorqueOnMatRtt(glm::mat4(glh::directionToRotation(axis, vec3(0, 1, 0))), torque, 1);
				lockOnSb->ndUpper2->rb0->addTorqueOnMatRtt(glm::mat4(glh::directionToRotation(axis, vec3(0, 1, 0))), torque, 1);
				lockOnSb->ndLower->rb0->addTorqueOnMatRtt(glm::mat4(glh::directionToRotation(axis, vec3(0, 1, 0))), torque, 1);
			}
			 
			{
				float v = shift ? 10 : 2;
				vec3 vel(A ? -v : D ? v : 0, 0, W ? v : S ? -v : 0);
				vel = glh::matRotateVec(m, vel);
				lockOnSb->Pmx->addBodyVel(vel, 1);
			}
		}
	}
}


bool AppMainAMP::StageOnKeyEvent(bool pressed, const irr::SEvent::SKeyInput& ke)
{
	irr::SEvent evo{ EET_CMD_INPUT_EVENT };
	auto evtRcv = Ctx->getEvtRcv();
	bool shiftR=false, ctrlR=false, altR=false;
	if (ke.Shift) shiftR = evtRcv->IsKeyDown(KEY_RSHIFT);
	if (ke.Control) ctrlR = evtRcv->IsKeyDown(KEY_RCONTROL);
	if (ke.Alt) altR = evtRcv->IsKeyDown(KEY_RMENU);

	DP(("KE %X d:%d ld:%d",ke.Key,ke.PressedDown,ke.LastPressDown));

#if USE_IMGUI
	ImGuiIO& io = ImGui::GetIO();
	
		//const ImGuiKey key = vks::ImGui_ImplWin32_KeyEventToImGuiKey(ke.Key, 0);
		//io.AddKeyEvent(ImGuiKey(key), pressed);
	if (io.WantTextInput) {
		return true;
	}
#endif
	if (mmd && mmd->onKeyEvent(pressed,ke)) {
		return true;
	}
	if (pressed)
	{
		switch (ke.Key)
		{
		case irr::KEY_LMENU: {
			arRoot->onKeyEvent(ke);
			return true;
		}break;
		case irr::KEY_KEY_0:case irr::KEY_KEY_1:case irr::KEY_KEY_2:case irr::KEY_KEY_3:case irr::KEY_KEY_4:
		case irr::KEY_KEY_5:case irr::KEY_KEY_6:case irr::KEY_KEY_7:case irr::KEY_KEY_8:case irr::KEY_KEY_9:
		{
			int id = ke.Key == KEY_KEY_0 ? 9 : ke.Key - irr::KEY_KEY_0 - 1;
#if TEST_FW_OVER_TEXT
			ptf.id = id;
#elif 1
			DP(("KEY %d", id));
			if (ke.Alt && ke.Control && ke.Shift) {
				float sc = (id+1)*0.2f;	
				scaleMmdNode(curChar(),curChar()->ndRoot, sc);
			}

			auto attachObj = [=](auto* sb, int id) {
				addMmdObjParam pm = Ctx->gd.apm;
				pm.flag = 1;
				pm.flag |= 0x10;
				pm.setMmdIdx = id;
				pm.tgtSb = sb;
				addMmdObj(pm);
				};

			if (ke.Control && ke.Alt) {
				if (NUM_ATTACH_WINGS) {
					for (auto& sb : mmd->sabas) {
						attachObj(sb, id);
					}
				} 
			}
			else if (ke.Shift)
			{
#if HAS_PIANO
				Piano->setFwIdx(id);
#else
				Eqv->setCurPtrFwId(id, 1);
#endif
			}
			else if (ke.Control) {
				curChar()->fwStrokeIdx = id % Eqv->fwStrokes.size();
			}
			else if (ke.Alt) {
				if (arRoot->vtxFwSn) arRoot->vtxFwSn->sb->fwLch->vtxFwId = id;
				else Eqv->setPenPtrFwId(id);
			}
			else if (NUM_ATTACH_WINGS)
			{
				attachObj(curAiChar(), id);
			}
			if (MMD_ATTACK_OBJ && 0)
			{
				ballFwId = id;
			}
			else if (!MMD_HAND_FW_MODE)
			{
				if (SVG_MMD_WRITE ) {
					genStkText(true,id);
				}
				if (numKeyMode == 1) {
					sb0->setAdAnim(id, 1);
					addMmdObj({ .flag=0,.pm1=id+1 });
				}
				else 
				{
					Eqv->dmFtTxtPm.beginWaitMs = 500;// 1000;
					Eqv->dmFtTxtPm.charDurMs = 500;// 1000; 
					Eqv->setCurPtrFwId(id);

					if (!ke.Control) {
						testTextFw(true, FW_TEXT, 0);
					}
					else Eqv->Sap.timer = 0.01f;
				}
			}
			else
				mmd->loadPoseId(ke.Key - irr::KEY_KEY_0, APP_FPS);
#else			
			eqvLoadFwStyle(id);
#endif
		}
		break;		
		case KEY_TAB:
		{
			
			SceneManager->WSADMode = ISceneManager::EWSADModes((SceneManager->WSADMode+1) % 2);
		};
		break;
		case irr::KEY_OEM_1: case irr::KEY_OEM_7:		//_; _'
		{
			int inc = ke.Key == irr::KEY_OEM_7 ? 1 : -1;

			if (ke.Alt && SVG_MMD_RB) {
				auto sb = sb0;// curChar();
				sb->stkSpringMul = core::clamp(sb->stkSpringMul + inc * (ke.Control ? 1.f : 0.1f), 0.f, 1.f);
				sb->updateJoints();
			}
			else if (ke.Shift)
			arRoot->curArSn->sb->modelAlpha = core::clamp(arRoot->curArSn->sb->modelAlpha*(inc > 0 ? 1.1f : 1 / 1.1f),0.1f,1.f);
			else {
				float* a = (float*)&arRoot->curArSn->sb->Pmx->GetMaterials()[arRoot->curArSn->sb->pickSubMesh].alphaMul;
				*a = core::clamp(*a + (inc *0.1f), 0.f, 1.f);
			}
 
		}break;
		case irr::KEY_OEM_3: {  //_` _~
			Eqv->dmFtTxtPm.charDurMs = 1000;
			if (ke.Shift)
			{
				Eqv->setCurPtrFwId(Eqv->getCurPtrFwId(1) + 1, 1);
			}
			else if (ke.Control && ke.Alt)
			{
				curChar()->fwStrokeIdx = (curChar()->fwStrokeIdx + 1) % Eqv->fwStrokes.size();
			}
			else if (ke.Control)
			{
				numKeyMode = (numKeyMode + 1) % 2;
			}
			else if (ke.Alt)
			{
				if (arRoot->vtxFwSn) arRoot->vtxFwSn->sb->fwLch->vtxFwId = (arRoot->vtxFwSn->sb->fwLch->vtxFwId + 1) % 12;
			}
			else				if (numKeyMode == 1) {
				sb0->setAdAnim(UaRand(sb0->Pmx->vmdAddAnims.size()), 1);
				addMmdObj({ .flag = 0 });
			}
			else
			{
				Eqv->setCurPtrFwId(Eqv->getCurPtrFwId() + 1);

				testTextFw(true, FW_TEXT);

			}
		}
						   break;


		case irr::KEY_OEM_4:case irr::KEY_OEM_6: {// KEY_[		]
			int inc = ke.Key == irr::KEY_OEM_6 ? 1 : -1;
			if (ke.Alt && ke.Control) {
				arRoot->setCurSn(it0);
			}
			else if (ke.Shift && ke.Alt) {
				float dis = wallRb[0]->getPosition().z; if (dis < 12 && inc<0) break;
				float d = inc * 10;
				wallRb[0]->addTranslateAni({ 0,0,d },	0.1f,uu::Ebf::easeInOutCubic);
				wallRb[1]->addTranslateAni({ 0,0,-d },	0.1f, uu::Ebf::easeInOutCubic);
				wallRb[2]->addTranslateAni({ d,0,0 },	0.1f, uu::Ebf::easeInOutCubic);
				wallRb[3]->addTranslateAni({ -d,0,0 },	0.1f, uu::Ebf::easeInOutCubic);

			}
			else if (ke.Alt) {
				//find closest sb
				arRoot->selectClosetItemOfs(inc, 0x10);
				switchItemTime = gGameTime;
			}
			else {
				arRoot->selectItemOfs(inc, ke.Control?0x110:0x10);
				switchItemTime = gGameTime;
				//if (ke.Control) sendKeyEvent(KEY_KEY_V);
			}

		}
		break;
		case irr::KEY_COMMA:case irr::KEY_PERIOD: {// KEY_,. _< >
			int inc = ke.Key == irr::KEY_PERIOD ? 1 : -1;
			if (ke.Control && ke.Alt) mmd->shdpm.toonMulCount = core::clamp(mmd->shdpm.toonMulCount +   inc, 1, 10);
			else if (ke.Shift && ke.Alt) mmd->shdpm.NdotEyeRat = core::clamp(mmd->shdpm.NdotEyeRat + 0.1 * inc, -1.0, 1.0);
			else if (ke.Control) mmd->shdpm.shadowRat = core::clamp(mmd->shdpm.shadowRat + 0.1 * inc, -1.0, 1.0);
			else if (ke.Alt) mmd->shdpm.whiteRat = core::clamp(mmd->shdpm.whiteRat + 0.1 * inc, -1.0, 1.0);
			else if (ke.Shift) mmd->shdpm.NdotLRat = core::clamp(mmd->shdpm.NdotLRat + 0.1 * inc, -1.0, 1.0);
			else {
				mmd->shdpm.shadowRat = core::clamp(mmd->shdpm.shadowRat + 0.1 * inc, -1.0, 1.1);
				mmd->shdpm.whiteRat = mmd->shdpm.shadowRat / 2.5f;

			}
		}
		break;
		case irr::KEY_OEM_2:  {// KEY_/?
			if (ke.Control) {
				mmd->shdpm.shadowRat = 0;
				mmd->shdpm.whiteRat = 1.0;
				mmd->shdpm.NdotLRat = 0;
				mmd->shdpm.NdotEyeRat = 0;
				mmd->shdpm.toonMulCount = 1;
			}
			else if (ke.Shift) {
				mmd->shdpm.shadowRat = 0.0;
				mmd->shdpm.whiteRat = 0.7;
				mmd->shdpm.NdotLRat = 0.1;
				mmd->shdpm.NdotEyeRat = 0.1;
				mmd->shdpm.toonMulCount = 2;
			}
			else
			{
				mmd->shdpm.shadowRat = 0.2;
				mmd->shdpm.whiteRat = 0.7;
				mmd->shdpm.NdotLRat = 0.1;
				mmd->shdpm.NdotEyeRat = 0.1;
				mmd->shdpm.toonMulCount = 2;
			}
		}
		break;


		case irr::KEY_SPACE: case irr::KEY_PAUSE: {

			if (ke.Control && ke.Alt)
			{
				if (IFppt) IFppt->togglePause(vp.paused);
			}
			else   {

				Ctx->scenePaused = !Ctx->scenePaused;
#if PAUSE_VIDEO_PROCESS_WITH_SCENE
				
				if (PAUSE_REC_TOUCHCAMANIM) {
					auto anm = Ctx->getCamTCAnimator();
					if (Ctx->scenePaused) {
						vp.paused = Ctx->scenePaused;
						anm->saveTLD();
						camTcAnimRecStr = R"~({"tlsList":[{"timelines":[)~";
						camTcAnimRecStr += anm->getCurStateTxt(0, 0); camTcAnimuid = 1;
					}
					else if (camTcAnimuid>0) {
						bool contChgSpd = (ke.Shift//|| evtRcv->isMouseKeyDown(1)
							)&& !ke.Alt && !ke.Control;
						if (contChgSpd) sendKeyEvent(KEY_KEY_X);
						vp.paused = Ctx->scenePaused;
						camTcAnimRecStr += R"~(]}]})~";
						copyTextToClipboard(ualib::Utf8toWcs(camTcAnimRecStr));
#if 1
						Eqv->CTL.loadTimeLines(anm, "",camTcAnimRecStr);
						Eqv->CTL.setTlNum(0, nullptr);
						anm->aniTl_start();
						anm->restoreTLD();		if (snCam) snCam->updateTransform();
						if (contChgSpd) changeSpeed(bulletTimeSpd =   BULLETTIMEMUL);
						anm->onStageChanged = [this, contChgSpd](int stage) {
							if (stage == -1) {
								
								if (contChgSpd) 
									sendKeyEvent(KEY_SPACE);
								else 
									changeSpeed(-1);
							}
						};
#else
						camTimelineStart("eqv/mf/1camtl copy.json", 0, true);
#endif
					}
				}
#endif
			}
			//if (MMD_ATTACK_OBJ && Ctx->scenePaused)		morphToFace(1);
			
		}	break;


		case irr::KEY_KEY_A://NO SHIFT  FOR WSAD
			//evo.CmdInput.cmdId = 20100;			StageOnCmdEvent(evo.CmdInput);
			//toSaveHLBase = 1;
		if (SceneManager->getActiveCamera() == Ctx->gd.CamRtt) {
			//if (frameFGenCount) break;			frameFGenCount++;		


			if (ke.Alt) {//shift
				camTimelineStart("eqv/mf/1camtlReset.json", ke.Shift ? 0 : 1);
			}
			else
			if (ke.Alt && ke.Control)
			{
				bool ie = Ctx->gd.CamRtt->isInputReceiverEnabled();
				//Ctx->gd.CamRtt->setInputReceiverEnabled(!ie);
				camTimelineStart("eqv/mf/1camtl.json", 0, true); 
#if HAS_MMD
				//	if (saba) saba->resetVMD(CTL.MmdStartTIme);
#endif
			}
			else if (ke.Control) {
#if SVG_MMD_WRITE
				if (mmd) mmd->sabas[0]->saveCam(SceneManager->getActiveCamera());
				sendKeyEvent(KEY_KEY_T, 1, 1, 0, 0);
#else
				auto anm = ((irr::scene::CSceneNodeAnimatorCameraTouchControl*)*Ctx->gd.CamRtt->getAnimators().begin());
				auto s = anm->getCurStateTxt();
				ualib::copyTextToClipboard(ualib::Utf8toWcs(s));
#endif
			}
			 
		}	 
		break;
		case irr::KEY_KEY_B: {
			if (AVOID_MISS_HIT && !ke.Shift && !ke.Control) break;// avoid space bar mis hit
			if (0 && ke.Control) {
				//drawBG = !drawBG; 
				vp.writeFrameToFile = !vp.writeFrameToFile;
				break;
			}
			if (cloth) cloth->setClothTex(nullptr);
			if (!ke.Control && !mmd->mdplr.isRecording())				
				mmd->mdplr.recordStart(2);
			
			//IFppt->ResetParticles(); 
			if (!ke.Control)
			arRoot->forEachArChild([=](SnArItem* sn) {
				if (sn->sb) {
					sn->sb->setVtxFw(1);
					sn->sb->resetAnimation(0, MMD_REVERSE_PLAY, 0);
					sn->sb->setPlaying(true);
				}
				},0x1);
			beatMan.resetBeat();
#if 0
			auto rf = Ctx->getFileSystem()->createAndOpenFile(Ctx->gd.scrWidth < Ctx->gd.scrHeight ? "d:/tmp/ar/v2rtt90.mp4" : "d:/tmp/ar/v2rtt0.mp4");
			inVFilePtr = (uint8_t*)malloc(rf->getSize());
			inVFileSize = (size_t)rf->getSize();
			rf->read(inVFilePtr, inVFileSize);
			rf->drop();
#endif
			if (Ctx->audioMan && (PLAY_AUDIO || customAudioLoaded)) Ctx->audioMan->stopPlay();
			if (Ctx->audioMan && (PLAY_AUDIO || customAudioLoaded)) Ctx->audioMan->playSound(PLAY_AUDIO_FILE.c_str(), PLAY_AUDIO_SYNC?false:true);
 
			
#ifdef _WIN32
			//if (ke.Shift)
			vp.saveVFIntval = SAVE_FRAME_INTVAL;
#endif
			
#if MMD_VIRTUAL_SINGER
			 
			for (int i=0;i<8;i++) g_timeline[i].clear(); //3d AUDIO
#endif
			
			//IFppt->ResetParticles();
			if (COLMAP_MODEL) vp.testColmapPly(TEST_VIDEO);
			startDecVideo(ke.Shift ? 2 : 1, START_OFS);
			
			
			//snGrid->setVisible(!ke.Shift);


		}
		break;
		case irr::KEY_KEY_C: {
			Eqv->clearFws();
			if (curSaba() != curChar()) arRoot->setCurSn(it0); // !char will be clear 			

			if (ke.Control && ke.Alt && ke.Shift) { 
				arRoot->connectConns(0, 1, { 0,-CONN_RB_SIZE * 1.f,0 }); 
				arRoot->connectConns(2, 3, { 0,-CONN_RB_SIZE * 1.f,0 });	
				memset(curAiChar()->lastConRb, 0, sizeof(curAiChar()->lastConRb));
			}
			else if (ke.Control && ke.Alt) arRoot->connectConnToPick(curAiChar()->lastConnId,{ 0,1,0 });
			else if (ke.Alt) arRoot->connectSbs(ke.Control,{ 0,0.5,0 });
			else if (ke.Control) 		arRoot->Cs.pickingVtxHold = 1;
			else if (ke.Shift) {
				auto sb = curChar(); static int cc = 0;
				sb->Pmx->connectRb(camRb, sb->ndUpper2->rb0, 1, { -4 + cc++ * 8,0,12 }, 1, float3(0, 180, 0) * core::DEGTORAD);
				sb->CenterForce = 0;
			}			
			else {
				if (arRoot->curArSn && curChar()) {
					curChar()->Pmx->clearVtxPickNodes();
					curChar()->lastLookObjId = 0;
				}
				if (sb0) {
					//if (curChar()->objId > 0)			arRoot->setCurSn(it0);

					sb0->charAtk.reset();

					arRoot->forEachArChild([=](SnArItem* sn) {
						sn->sb->onClear();
						});
					for (auto sn : tmpSns) sn->remove();
					tmpSns.clear();
					Pom->removeAllObjs();
					for (auto p : Plates) {
						p->clearBalls(); p->recreateCubes(0,0);
					}
					 
				}

				arRoot->forEachArChild([=](SnArItem* sn) {
					if (sn->sb && (ke.Alt || sn == arRoot->curArSn)) {
						sn->sb->phyRtt = {};
					}
					}, 0x1);
			}
		}break;
 
		case irr::KEY_KEY_E:
		{
			if (ke.Control) {
				mmd->mdplr.recordStart(1);
				if (cloth) cloth->setVisible(false);
				drawMirror = false;
				sb0->setPhysics(false);
				return true;
			}
			else if (ke.Alt) {
				bool recScr = !ke.Shift;
				if (vp.working && (recScr ? 9 : 2) == vp.recMode) stopRecord();
				else {
					if (recScr)  if (Ctx->audioMan && (PLAY_AUDIO || customAudioLoaded)) Ctx->audioMan->playSound(PLAY_AUDIO_FILE.c_str(), true);
					startRecord(recScr ? 9 : 2);
#if MMD_VIRTUAL_SINGER
					if (!ke.Control && !mmd->mdplr.isRecording()) mmd->mdplr.recordStart(2);
#endif
				}
				arRoot->forEachArChild([=](SnArItem* sn) {
					sn->sb->resetAnimation(-sn->sb->getItemIdx() * 0, false, 0);
					sn->sb->setPlaying(true);
					});
			}
		}	break;
		case irr::KEY_KEY_F: {
			if (mmd->fluidNode) {
				if (ke.Control)
					mmd->fluidNode->changePtcState = 0x20;
 
				{
					

#if 0				// from mmd
					{						
						vec3 pos =   sb0->ndCtr->rb0->rbTransformVec(vec3{ 0,-2,-0.1 }*sb0->ndRbRoot->absScale.x);
						vec3 dir =   sb0->ndCtr->rb0->getLinearVel(); (pos - sb0->ndCtr->rb0->pos) * 1.f;
						int r = ke.Shift ? 10 : 3;
						addFluidParticlesGrid(r,r,r, .5, .5, .5, pos, dir*1.f);
						//sb0->ndUpper->rb0->addLinearVel(-dir * 1.f);
					}

#elif 1		//from camera
					auto cam = SceneManager->getActiveCamera();
					if (cam) {
						auto cp = cam->getPosition() ;
						vec3 pos = (cp )/MMD_SABA_SCALE +vec3(0,-6,0);
						vec3 dir = cam->getRotation().rotationToDirection().normalize();// curAiChar()->ndUpper2->rb0->pos + vec3(0, 0, 0) - pos;
						pos += dir * 3.f;
						addFluidParticlesGrid(8,8, (ke.Shift ? 10 : 1)*8, .5,.5,.5, pos, dir *100.f,cam->getRotation()*core::DEGTORAD);
					}
#else
					//fix pos
					vec3 pos = { 0,ke.Shift ? 30 : 10,0 };
					//pos = itScene->sb->ndRbRoot->transformVec(pos);  
					 addFluidParticlesGrid(10, 10 * (ke.Shift ? 10 : 1), 10, -1,-1,-1, pos, { 0,0,0 });
#endif
					 
				}
			}
			//if (ke.Alt)morphToFace(0.5f*Ctx->gd.deltaTime*60);
			//if (ke.Shift)
			//	curSaba()->bursting ^= 1;
			//else 
			 {
				//Eqv->setCurPtrFwId(Eqv->findPtrFwIdx(1, "soundFwVM"), 1);
				//sb0->vtxFrameFw = 0;//sb0->lauchAllVtxFw = ke.Shift?2:1; 
			}
			if (ke.Alt)
				objCenterForce = !objCenterForce;
			else if (ke.Control){
			
				//sb0->ndHandL->rb0->jtsToParent[0]->rotateTowards(mmd->camPos, 1000);
				//sb0->ndArm1L->rb0->jtsToParent[0]->rotateTowards(mmd->camPos, 1000);
				

			}
			
			// 
			//Eqv->LaunchFw3D({ 0,0,0 }, Eqv->getFwIdxByFwIdStr(1, "hovNode"), { 0,0,0 }, SColorf(0xCFFF2020));

			//static int cc = 0;
			//auto& p = arRoot->Cs.icp;
			//p.itemType = 2;
			//p.itemSubType = 2;
			//p.posType = 1;
			//p.posFrontDistance = cc * 1000;;
			//evo.CmdInput.cmdId = 0x10000000 | cceItemNew;
			//StageOnCmdEvent(evo.CmdInput);
			//cc++;

		}break;
		case irr::KEY_KEY_G: {
			if (ke.Shift) {
				Ctx->setCameraId(1);
				if (arRoot->curArSn) {
					(*Ctx->gd.CamRtt->getAnimators().begin())->setTarget(arRoot->curArSn->getModelAbsCenter().getTranslation());
				}
			}
			else if (ke.Alt) { snGrid->setVisible(arRoot->Cs.showGroundPlane = !arRoot->Cs.showGroundPlane); }
			else if (ke.Control) {
				SceneManager->addCameraSceneNodeFPS(0,1,5);
				Ctx->gd.fpsMode = true;
				//if (Ctx->gd.CamRtt != SceneManager->getActiveCamera()) {
				//	Ctx->gd.CamNormal->copyDataTo(Ctx->gd.CamRtt);					
				//	Ctx->setCameraId(1);
				//}
			}
			else if (Ctx->gd.CamRtt!=SceneManager->getActiveCamera()) Ctx->setCameraId(1);	//	snLight->setParent(Ctx->gd.CamNormal);		snLight->setPosition(0, 10, -10);
			else Ctx->setCameraId(0);
		}break;

		case irr::KEY_KEY_I:
			if (ke.Shift && ke.Control) {
				int cc = 0, lpa=0;
				arRoot->forEachArChild([&](SnArItem* sn) {
					if (auto sb=sn->sb) {
						if (!sb->isAiCharacter())
						{
							if (cc++ == 0) lpa = sb->localPhyAnim = 1;// !sb->localPhyAnim;
							else sb->localPhyAnim = lpa;
							sb->resetAnimation();
							sb->loadAnimation(1?L"D:/mmd/vmd/runFroppy.vmd":L"D:/Tmp/2ph/1.vmd"); sb->setPlaying(true); sb->animCycleStart = 0;
							sb->setPhyAnim(sb->ndCtr, true, true);
							sb->setPhyAnim(sb->ndCtr, 0, 0);
						}
					}
					}, 1);
			}
			else if (ke.Shift) {
				mmd->curCtrlSb([=](IrrSaba* sb) {
					sb->localPhyAnim = !sb->localPhyAnim;

					});

			}
			else if (!ke.Alt) mmd->syncMovNode = !mmd->syncMovNode;
			else {
				mmd->ikVisable = !mmd->ikVisable;
				//arRoot->Cs.pickPhysics = !mmd->ikVisable;
			}
			
			
			break;
		case irr::KEY_KEY_J:

		{
			
#if 0//USE_LEAP
			//auto sb = sb0;	auto rbht = sb->Pmx->getRb(sb->Pmx->headTgtRbId); sb->ndHeadIK->EnableDeformAfterPhysics(false);
			//leap->connectNodeWithRb(0, 1, 3, ke.Shift?nullptr:rbht); 
#else
			if (ke.Shift) depthRenderFw = !depthRenderFw;
			else  drawDepth = !drawDepth;	copySScd = 3;
#endif
			 
		}
			break;
		case irr::KEY_KEY_H:
#if USE_LEAP 
 
			if (MMD_PMX_HAND && leap && !leap->sbL) {
				PMXFileCreateParam cp;
				cp.filePath = "d:/mmd/pmx/leaphands/l3a.pmx";
				cp.modelScale = true;

				cp.dynJointFree = 1;
				cp.massMul = 100;
				if (itScene)
					cp.collisionId = itScene->sb->modelCollisionId;
				float sc =   0.57f;
				cp.modelScaleVec3 = { sc,sc,sc }; 
				//mmd->Pm.spm.allowGPU = false;
				leap->sbL = loadSabaModel(cp, false,0x1101)->sb;		//0x1000: allowAllNodePhysics

				if (cloth) cloth->handLSb = leap->sbL;
				cp.filePath = "d:/mmd/pmx/leaphands/r3a.pmx";
				leap->sbR = loadSabaModel(cp, false, 0x1101)->sb;				
				//itSns[1]->sb->Pmx->connectRb(leap->sbR->Rb0(), itSns[1]->sb->Rb0(), 1, {0,0,30}, 1, float3(0, 0, 0)* core::DEGTORAD);
				//mmd->Pm.spm.allowGPU = true;
#if 0
				leap->sbL->forcePhyAnim = true;	leap->sbL->setAllDynRbActive(true); leap->sbL->setPhyAnim(leap->sbL->ndRbRoot, true, true);
				leap->sbL->ndRbRoot->forEachSubNodes([=](saba::MMDNode* nd) {
					if (nd->rb0 && nd->rb0->dynRbType)
						nd->phyAnimRatR = nd->phyAnimRatT = 2; 
					}, true);
				leap->sbL->rd.scRatioMin = 0.5f;				//leap->sbL->localPhyAnim = 1; 

				leap->sbR->forcePhyAnim = true;	leap->sbR->setAllDynRbActive(true); leap->sbR->setPhyAnim(leap->sbR->ndRbRoot, true, true);
				leap->sbR->ndRbRoot->forEachSubNodes([=](saba::MMDNode* nd) {
					if (nd->rb0 && nd->rb0->dynRbType)
						nd->phyAnimRatR = nd->phyAnimRatT = 2;
					}, true);
				leap->sbR->rd.scRatioMin = 0.5f;
 
				
#endif					
			}
			else 
				if (leap) leap->toggleVisible();
#else
			if (ke.Control) sb0->Pmx->saveState();
			else sb0->Pmx->restoreState(ke.Alt?-1:0, 0);
			//sb0->charAtk.lauchAllVtxFw();
#endif
			//arRoot->Cs.psTrsSpace = (arRoot->Cs.psTrsSpace+1)%3;			DP(("SPACE %d", arRoot->Cs.psTrsSpace));
			//arRoot->rotateSubPickNodes((ke.Shift ? -1 : 1) * core::DEGTORAD);
			
			break;
		case irr::KEY_KEY_K:
			if (ke.Control) {
				curChar()->toggleIKEnable();
			}
			else 			if (!ke.Shift) {
				auto bc = curChar()->canLookAtCam;
				mmd->curCtrlSb([=](auto* s) {
					s->canLookAtCam = !bc;
					});
				
			}
			else
			{
				static bool tmpIK = false; tmpIK = !tmpIK;
				evo.CmdInput.cmdId = 0x10000000 | cceSetTmpIkMode; evo.CmdInput.pm1 = tmpIK ? 1 : 0;
				StageOnCmdEvent(evo.CmdInput);
			}break;
		case irr::KEY_KEY_L:
		{
#if COLMAP_MODEL 
			if (ke.Control) {
				vp.testColmapPly(TEST_VIDEO, true);
				break;
			}

			vp.testColmap(arRoot);
			vp.testColmapPly(TEST_VIDEO);

#else
			int mode = (ke.Alt) ? 2 : (ke.Control) ? 1 : 0;
			arRoot->forEachArChild([=](SnArItem* sn) {
				if (sn->sb) sn->sb->toggleLnv(mode,ke.Shift?-1:1);
				});
#endif
		}break;
		case irr::KEY_RETURN: {
			//reloadScene();
			auto it = arRoot->curArSn;
			arRoot->onModelUploaded(mmd, nullptr, it->modelFilePath, arRoot->curArSn, false,true);
			//if (it == it0) sb0 = it->sb;
		}break;

		case irr::KEY_KEY_M:
		{
			if (ke.Control && ke.Alt && ke.Shift) {
				auto it=loadSabaModel({ "D:/MMD/PMX/Y/lxzfix/s.pmx" },false, 0x1001);
				it->sb->isCamerer = true;
				camRb->setScale(vec3(0.5f));

			}

			//else if (ke.Shift) {
			//	{
			//		PMXFileCreateParam cp = defaultFcp; cp.filePath = ITEM_PMX1;
			//		cp.massMul = 0.1f;
			//		//cp.modelScale = true; 			float sc = .75;		cp.modelScaleVec3 = float3{ sc,sc,sc };				
			//		auto s = loadSabaModel(cp, false, 0x1001);
			//		s->sb->ndFrameRtt = s->sb->findNode(L"center");

			//	}
			//}
 			else {
				auto fcp = defaultFcp;
				if (ke.Shift) {
					fcp.initPos = lastCntSb->ndCtr->rb0->getPosition() + glm::vec3(0, 30, -10);
					float msc = 1;
					fcp.massMul = msc * 0.975f; fcp.modelScale=true; fcp.modelScaleVec3 = vec3( 0.9);
				}
				loadSabaModel(fcp, ke.Control);
				if (PRELOAD_VMD.length() > 3) {
					auto it = arRoot->curChar() ;
					it->loadMotion(PRELOAD_VMD.c_str());// it->sb->setPlaying(true); //it->sb->animCycleStart = 0;
				}
 
				 //sendKeyEvent(KEY_F1);
				for (auto p : Plates) if (p->Pm.actMode==1){
					p->clearBalls(); p->recreateCubes(0, 1);
				}
 
			}

			//evo.CmdInput.cmdId = 10101;
			//StageOnCmdEvent(evo.CmdInput);

		}	break;
		case irr::KEY_KEY_N:

			//nextVmd();
			if (ke.Control) MMDPhysics::paused = !MMDPhysics::paused;
			if (ke.Alt) mmd->shaderNoColor = !mmd->shaderNoColor;
			else if (ke.Shift) {
				if (mmd->outNormalMap == 0) mmd->outNormalMap = ke.Shift ? 2 : 1;
				else mmd->outNormalMap = 0;
				Ctx->gd.bgColor = mmd->outNormalMap ? 0xFFFF7F7F : 0;
				copySScd = 3;
			} else  Driver->dsd.forceUpdateCD = 1;
#if MMD_SAVE_CAMVMD
			toSaveCamVmd = true;
#endif
			break;
		case irr::KEY_KEY_O:
			if (ke.Control && ke.Alt && ke.Shift) 
				Driver->dsd.oitOn ^= 1;
			else if (ke.Control && ke.Alt) {
				curArSn()->sb->useOIT = !curArSn()->sb->useOIT;
			}else if (ke.Shift) {
				curChar()->Pmx->GetPhysicsManager()->GetMMDPhysics()->mForceUpdateDyn ^= 1;
			}
			
			else {
				//arRoot->curArSn->saba->loadPose("d:/mmd/poses/sitarms.vpd", 0);
				auto sb = curAiChar();
				 
					if (arRoot->curPickNode.getSbNode() && ke.Alt)sb->setPhyAnim(ke.Alt && arRoot->curPickNode.getSbNode()->GetParent() ? arRoot->curPickNode.getSbNode()->GetParent() : arRoot->curPickNode.getSbNode(), !ke.Shift, true);
					else if (sb->isHumanModel()) {
						if (!sb->ndCtr->phyAnim) {
							if (ke.Alt) {
								//sb->ndCtr->rb0->setMassMul(10);
								sb->ndUpper->EnableDeformAfterPhysics(true); sb->setPhyAnim(sb->ndUpper, true, true);
								//sb->ndLower->EnableDeformAfterPhysics(true);sb->setBonePhsAnime(sb->ndLower, true, true);

								//sb->ndArmL->EnableDeformAfterPhysics(true); sb->setBonePhsAnime(sb->ndArmL, true, true);
								//sb->ndArmR->EnableDeformAfterPhysics(true); sb->setBonePhsAnime(sb->ndArmR, true, true);
								sb->Pmx->phy2ndPass = true;
							}
							else {
								//sb->ndCtr->EnableDeformAfterPhysics(false);
								sb->setPhyAnim(sb->ndCtr, true, true);

								//sb->setBonePhsAnime(sb->ndArmL, false, true);	sb->setBonePhsAnime(sb->ndArmR, false, true);
								sb->Pmx->phy2ndPass = false;
							}
							sb->startRagDoll({ 0,1 });
							//sb->ndFootL->phyAnimVelMul = 0.f;

							//sb->setBonePhsAnime(sb->ndUpper, false, true);sb->setBonePhsAnime(sb->ndLower, false, true);
#if IS_SIT_VMD
						//sb->setBonePhsAnime(sb->findNode(L"左ひざD"), false, true);sb->setBonePhsAnime(sb->findNode(L"右ひざD"), false, true);
							sb->ndHandL->GetParent()->GetParent()->EnableDeformAfterPhysics(true);
							sb->ndHandR->GetParent()->GetParent()->EnableDeformAfterPhysics(true);
#endif
							//	sb->setBonePhsAnime(sb->ndHead->GetParent(), false, true);
								//sb->setBonePhsAnime(sb->ndLower->GetParent(), false, true);
								//sb->ndHandL->phyAnimVelMul = 0.2f;	sb->ndHandR->phyAnimVelMul = 0.2f;
						//	sb->ndFootL->phyAnimRatR = 0.0f;	sb->ndFootR->phyAnimRatR = 0.0f;
						}
						else sb->setPhyAnim(sb->ndCtr, false, true);

						mmd->curCtrlSb([=](auto* s) {
							bool phyAnim = sb->ndCtr->phyAnim;
							if (s != sb) {
								s->setPhyAnim(s->ndCtr, phyAnim, true);
								if (phyAnim) {
									s->Pmx->phy2ndPass = false;
									s->startRagDoll({ 0,1 });
								}
							}
							});
					}

				
			}
			break;
		case irr::KEY_KEY_P: {
			static bool dynActive = true;
			//vp.testColmapPly(TEST_VIDEO);
			auto sb = curChar();
			if (!sb->isHumanModel() && !sb->Pmx->pmx.allDynRb)
				sb = sb0;
			if (ke.Control) {
				if (arRoot->curPickNode.getSbNode()) curChar()->setBonePhsActive(
					ke.Alt && arRoot->curPickNode.getSbNode()->GetParent() ? arRoot->curPickNode.getSbNode()->GetParent() : arRoot->curPickNode.getSbNode(), !ke.Shift);
				//arRoot->curArSn->Cei->previewPhysics = !arRoot->curArSn->Cei->previewPhysics; 
			}
			else {
				if (ke.Alt && sb->Pmx->getDynRbActive())
					sb->globlToBaseAnime();
				bool active = !sb->Pmx->getDynRbActive();
 
				mmd->curCtrlSb([=](auto* s) {
					 {
						 
#if PHY_GPU
						if (active) {
							s->setAllDynRbActive(1, 0);
							FRAMEWAITER_CALL_B(2){ s->setAllDynRbActive(0, 1); });
						}
						else 
#endif
						s->Pmx->moveAllRbToAnim();
						s->setAllDynRbActive(active,0);
						s->Pmx->UpdateNodeAnimation(false); //s->Pmx->UpdateNodeAnimation(1);
						s->Pmx->resetRigidBodies(active ? 1 : 0);
						s->Pmx->GetMMDPhysics()->clearForces();
						 
					}
					});
			}


			//arRoot->curArSn->saba->animCycleStart = 0;			
		}break;
		case irr::KEY_KEY_Q: {
			if (ke.Control) {
				Ctx->vrOn = !Ctx->vrOn;
				Ctx->gd.CamRtt->setAspectRatioOnRTSize();
				Ctx->gd.CamRtt->setFovX(piFloat);
			}
			 
		}break;
		case irr::KEY_KEY_R:
		{
			if (frameMmd) break;
			frameMmd++;
			//static int cc = 107; 	DP(("FWINST %d", cc));		Ctx->Midi.setFwNoteInst(0, cc); cc = (cc + 1) % 128;
			//if (!ke.Control && sb0 && sb0->ndUpper2 && !sb0->ndUpper2->rb0->GetActivation()) sendKeyEvent(KEY_F9, true);
			arRoot->forEachArChild([=](SnArItem* sn) {
				if (sn->sb) {
					 
				}
				});
			//if (ke.Control && ke.Alt) {				
			//	PMXFileCreateParam cp(POT_PMX);
			//	cp.createPhyMesh = true;
			//	auto s = loadSabaModel(cp, false, 0x101);
			//	s->sb->ndRbRoot->rb0->SetActivation(false);
			//}
			//else 
			{
				bool retFlag;
				bool retVal = shotR(ke, retFlag);
				if (retFlag) return retVal;

			} 
				//if (ke.Shift) if (sb0->camSave) sb0->camSave = nullptr; else  sb0->saveCam(SceneManager->getActiveCamera());
			//if (!mmd->mdplr.onNoteCb) mmd->mdplr.onNoteCb = [this](int act) {		arRoot->launchPhyObj(2, 20);}			else mmd->mdplr.onNoteCb = nullptr;
		}
		break;
		case irr::KEY_KEY_S: //NO SHIFT  FOR WSAD
			  {
				if (ke.Alt && SceneManager->getActiveCamera() == Ctx->gd.CamRtt) {
					//
					//mmd->mdplr.aouStart(0, UaRand(5) + 2, 0, false);
					//double st = 0.2;			mediaSeekToPercent(st);			DP(("SEEK !"));
				}
		} break;
		case irr::KEY_KEY_T: {
#if MMD_HAND_OXR
			{
				evo.CmdInput.cmdId = 0x10000000 | cceSetTmpIkMode; evo.CmdInput.pm1 = 1;
				StageOnCmdEvent(evo.CmdInput);
			}
			MMDNode* node = it0->saba->findNode(L"左手首");
			it0->saba->setTmpIK(node, it0->saba->findNode(L"左腕"));
#else
			//it0->saba->camTgt = arRoot->curArSn; 

#if MMD_ACTION_ANIM
			static int cc = -1; cc++;
			std::wstring ws = FW_TEXT;
			ws = ws.substr(cc % ws.length(), 1);
			 
			testTextFw(true,ws, 1 | (ke.Control ? 2 : 0));
#else
			if (!SVG_MMD_WRITE) testTextFw(true, FW_TEXT, 1|(ke.Control?2:0));
			else genStkText(0,ke.Control&&ke.Alt?-1:0,ke.Control?10.f:1.f);
#endif

#endif
			if (cloth) cloth->bindNodes("2p");

	 
		}break;
		case irr::KEY_KEY_U:
		{
			sb0->createInflate();
			//mmd->mdplr.aouId++;
			break;

		}
			arRoot->forEachArChild([=](SnArItem* sn) {
				if (sn->sb) {					
					sn->sb->Pmx->resetRigidBodies();
					if (!ke.Control) return;
					sn->sb->Pmx->ResetPhysics(sn->sb->Pmx->getDynRbActive());		
					sn->sb->Pmx->InitializeAnimation(); sn->sb->Pmx->syncPhyToFrame(30);
				}
				});
			if (snSkyDome) snSkyDome->toggleVisible(); 
 
			//evo.CmdInput.cmdId = 0x10000000 | ccePickSetBonePhyActive;			StageOnCmdEvent(evo.CmdInput);

			//if (snBox->getParent() == Ctx->gd.RootSn)				Ctx->gd.RootSn->setScale(0.1f);
			//Ctx->gd.CamNormal->setParent(SceneManager->getRootSceneNode());
			//snBox->updateTransform();
			//Ctx->gd.RootSn->setScale(0.5f);

			break;
		case irr::KEY_KEY_V:if (AVOID_MISS_HIT && !ke.Shift && !ke.Control) break;// avoid space bar mis hit
			//arRoot->curArSn->loadMotion("D:\\AProj\\VkUpApp\\data\\temp\\output.vpd");
			
			if (ke.Control && ke.Alt && ke.Shift) {
				ualib::stringSaveToFile(curChar()->Pmx->getPoseVPD(), "r:/output.vpd", false);
				arRoot->curChar()->sb->saveVmd("r:/output.vmd");				arRoot->Cs.vpdSaveString = 0;				arRoot->Cs.vpdSaveStringLength = 0;
			}
			else if (ke.Control && ke.Alt) {
				mmd->curCtrlSb([=](auto* sb) {
					sb->clearAnimation(); sb->resetAnimation();  sb->setPlaying(true);
					});
			} //IFppt->setVisible(!IFppt->isVisible());
			else if (ke.Alt)  curSaba()->setVisible(!curSaba()->isVisible());
			else {
				
				loadVmd(ke.Shift ? -1 : 1, !(ke.Alt && ke.Shift));

			}
			break;
		case irr::KEY_KEY_W:			//NO SHIFT  FOR WSAD
			{	
			 
				drawWaterMarkCC = 0;
				if (!ke.Alt) break;				
			  
				if (ke.Alt)			morphToFace(0.5f * Ctx->gd.deltaTime * 60);
				//else if (SceneManager->getActiveCamera() == Ctx->gd.CamNormal && sb0 == curSaba()) sb0->loadCamera(MMD_CAM_FILE, false); sb0->Pm.activeNodeCam = true;
			}
			break;
		case irr::KEY_KEY_Z:case irr::KEY_KEY_X:
		{
 

			if (ke.Shift) {
#if 0
#define CTL_STAGE0_DURATION 5
			auto anm = ((irr::scene::CSceneNodeAnimatorCameraTouchControl*)*Ctx->gd.CamRtt->getAnimators().begin());
			anm->aniTL_stop();
			if (ke.Control)
				Eqv->CTL.strStage1 = anm->getCurStateTxt(1, 1);
			else {
				Eqv->CTL.strStage0 = anm->getCurStateTxt(0, CTL_STAGE0_DURATION);
				//if (it0) it0->saba->saveCam(SceneManager->getActiveCamera());
				//if (it1) it1->saba->saveCam(SceneManager->getActiveCamera());
				//if (it2) it2->saba->saveCam(SceneManager->getActiveCamera());
#if SVG_MMD_WRITE_SAVE_VMD
				it0->saba->setPlaying(true); it0->saba->vmdWriteFile->recordingAllNode = true;
#endif
				sendKeyEvent(KEY_KEY_1, pressed);
			}
			//saveCam(SceneManager->getActiveCamera());
#elif  !MMD_CONTROL_SD
			arRoot->forEachArChild([=](SnArItem* sn) {
				if (sn->sb && (ke.Alt || sn== arRoot->curArSn)) {
					float add = ke.Key==KEY_KEY_Z? 0.1: -0.1;
					if (ke.Control) sn->sb->phyRtt.x += add; 
					else sn->sb->phyRtt.y += add;
				}
				},0x1 );

#else
			//OPENPOSE
			if (!vp.working)
			{
			if (!opRT) opRT=  Driver->addRenderTargetTexture( Ctx->mainViewSize(), "<RGBA>");
			Driver->setRenderTarget(opRT, 1, 1, 0x00000000);
			drawOpenPoseFrames = 60;// 
			arRoot->forEachArChild([=](SnArItem* sn) {
				if (sn->sb) sn->sb->openPoseDraw(opRT); // 
				});
			Driver->setRenderTarget(0, 0, 0, 0);
			IImage* img = Driver->createImageTextureCopy(opRT);
			copyBitmapToClipboard((char*)img->lock(), img->getDimension().Width, img->getDimension().Height);
			img->unlock();		img->drop();}
#endif
			}   
			else {

				if (ke.Key == KEY_KEY_X) {
					if (ke.Control || vp.paused)
					{
						auto anm = Ctx->getCamTCAnimator();
						auto s = anm->getCurStateTxt(0, 1);
						ualib::copyTextToClipboard(ualib::Utf8toWcs(s));
						if (!vp.paused) Eqv->CTL.strStage0 = anm->getCurStateTxt(0, 2);
						else {
							float tm = BULLETTIMEMUL;

							if (anm->getLockSn()) anm->settingLock = false;;
							if (ke.Control && sb0->Pom->getLastObj()) {

								anm->setLockSn(/*anm->getLockSn()?nullptr:*/sb0->Pom->getLastObj()->pm.sn);
							}
							if (ke.Shift) tm *= 0.5f;
							Eqv->CTL.strStage0 = "";
							camTcAnimRecStr += anm->getCurStateTxt(0, camTcAnimuid == 1 ? 1.f * tm : tm, CAM_DYN_RB ? "none" : "esIOSin"); camTcAnimuid++;
						}
					}
					else //non
					{
						mmd->mdplr.aouWords(0, { L"@",L"@",L"a",L"a",L"a",L"a",L"a",L"a",L"a",L"a",L"a",L"a",L"a" });
					}
				}
				else if (ke.Key == KEY_KEY_Z) {
					mmd->curCtrlSb([=](auto sb) { sb->setPlaying(!arRoot->curChar()->sb->getPlaying());sb->setAllDynRbActive(false, 1); });
					
				}
			}
		}						
		break;
		case irr::KEY_KEY_Y:
		{ 		

			if (ke.Shift && ke.Alt) {
				auto anm = ((irr::scene::CSceneNodeAnimatorCameraTouchControl*)*Ctx->gd.CamRtt->getAnimators().begin());
				anm->setMinRttY(!ke.Alt, -360.f);
			}else if (ke.Control && ke.Alt) {

				SphereCamShot = 0; 
				Ctx->scenePaused = vp.paused = true;
				IFppt->togglePause(vp.paused);
				//arRoot->forEachArChild([=](SnArItem* sn) {	if (sn->saba) sn->saba->toggleLnv(2);	});
			}
			 
			else if (cloth) cloth->snCloth->drawLine ^= 1;
			else {

				auto sb = curChar();
				//if (auto nd = sb->findNode(L"PT_F_Watch"))
				for (int i = 10; i < 15; i++) Ctx->Midi.setFwNoteInst(i, 127);// 112);
				
				if (0&&ke.Shift)
				{
					//define
					int xc = 2, yc = 20, zc = 38; float3 boxSc(1, 1, 1);
					float3 space(12, 0, 0);// (8.2, 2, 8.2);
					//tmp
					static int cc = 1;
					float xr = 3.2 * boxSc.x, yr = 2.44 * boxSc.y, zr = 2.66 * boxSc.z;
					float xl = xr + space.x, yl = yr + space.y, zl = zr + space.z;
					const float sc = 1.0f;
					float3 size(xr, yr, zr), lvlSc = float3(1.f);// / float3(1.1f, 1 / 1.0f, 1.1f);

					for (int y = 0; y < yc; y++) {
						size *= lvlSc; boxSc *= lvlSc;
						for (int x = 0; x < xc; x++)for (int z = 0; z < zc; z++)
						{
#if 1
							PhyObjParam pm{ 32 ,1.f, size * sc,float3(x * xl - xl * (xc - 1) / 2,z * 0 + (y + 3.5) * yl, z * zl - zl * (zc - 1) / 2) * sc,{},{ 0,0,0 }, };
							pm.operation = 1;   pm.timer = 600.f;
							pm.pmxrb.m_repulsion = 0.f;
							pm.pmxrb.m_friction = 1.f;
							pm.pmxrb.m_translateDimmer = 1.f; pm.pmxrb.m_rotateDimmer = 0.f;
							pm.pmxrb.m_collideMask32 = -1; pm.tag = 0;
							//pm.pmxrb.massCtr = float3(0, -2, 0);
							static auto sms = SceneManager->getMesh("data/kd.obj"); //bangbu.obj");// kd.obj");
							pm.meshScale = float3(1.6) * sc * boxSc;
							pm.mesh = sms;
							SColor sc = SColorHSL(y * 15, 50, 50).toSColor();
							pm.color = sc.color;// 0xFF808080;
							//pm.pmxrb.frameChgMask = 1; pm.pmxrb.frameAddLinearVel = { 0,2,0 };
							auto rb = sb0->Pom->addObj(pm)->rb;
							rb->resetRbCd = y * 3;
#else
							saba::PMXFileCreateParam fcp{ "D:/MMD/PMX/Y/qill2/box.pmx" }; fcp.massMul = cc;

							auto sb = loadSabaModel(fcp, false, 0)->sb;
							sb->Pmx->rt1Tr = float3(x * xl - xl * (xc - 1) / 2, y * yl, z * zl - zl * (zc - 1) / 2);
							sb->Pmx->InitializeAnimationUpdate();
							sb->Pmx->UpdateNodeAnimation(false);
							sb->Pmx->ResetPhysics(true);
							auto rb = sb->getRb0(); tmpSns.push_back(sb);
#endif
							rb->usrDat.callHitCb = true; rb->cbHit = [&](saba::PhysicsEngineObjectUserData* hit) {
								static int cc = 0;
								float implen = glm::length(hit->hitImp) / Ctx->gd.timeMul;
								if (implen < 0.01f) return;
								DP(("imp %f", implen));
								Ctx->Midi.playNote(Driver->dsd.time, 9, core::s32_clamp(34 + UaRand(16), 0, 100)
									//* (1.f + gTimeMul) / 2
									, glm::clamp(implen * 2.f, 0.1f, 1.f), 0
								);
								};

						}
					}
					cc *= 2;
				}
				else if (ke.Alt)
				{
					const int count = 10;
					//for (int i=0;i<count;i++) attachObjToMmd(sb,2);
					if (Plates.size()) {
						static int cc = -1; cc++; auto pl = Plates[cc % Plates.size()];
						if (!pl->onExtBall) pl->onExtBall = [=]() {
							attachObjToMmd(pl->Pm.sb0, 2);
							};
					}
					else attachObjToMmd(arRoot->curAiSb(), 2);

				}
#if 1
				else {
					vmsnId = (vmsnId + 1) % VOX_NUM;
					if (!vmsn[vmsnId]) {
						vmsn[vmsnId] = new CVoxelMeshSceneNode(L"data/mesh/cubeRS.obj", mmd->sb0, SceneManager, mmd); vmsn[vmsnId]->drop();
						vmsn[vmsnId]->loadVox("data/vox/" //store8.vox",//12.vox", 
							//"melonS.vox", { 0, 00,0 }, 1, 1,1
							//"eggs.vox", { 0,6,0 }, 1, 1, 0.5f
							//"menmoH.vox", { -60, 0, -60 }, 1, 0, 1.f
							//"mjyDishS.vox", { 0, 10, 0 }, 1, 0, 0.95f
							//"jmmdS.vox",{ -30,0,-30 },1,1,1
							//"sea.vox", { -30,20,-30 }, 1, 1,1.f
							//"th.vox",{ -60, 0, -60 },1,1,0.25f
							//"t.vox", { -10, 0,  -10 },1,false,1
							//"wall.vox", { -60, 0, -60 }, 1, 0, 0.5f
							//"semitruck-us.vox", { 0, 16, 0 }, 1, 1, 0.5f
							"muTong.vox", { 0, 3, 0 }, 1, 0, 0.5f
							//"12.vox", { -60, 0, -60 }, 1, 1,1
						);
					}
					else vmsn[vmsnId]->resetScene(ke.Shift);
				}
#else
				else if (CCubeGridSceneNode::Walls.size()==0  ) {
					BrickWallParams bwp{};
					bwp.pos = glm::vec3(0, 10, 2);
					bwp.rtt = glm::vec3(0);
					bwp.grid = glm::ivec3(30,60, 1);
					bwp.brickSize = glm::vec3(1.f,1, 1.f)*0.35f;
					bwp.brickSpace = glm::vec3(0);
					bwp.density = 100.f; //100
					bwp.restitution = 1.0f;
					bwp.friction = 0.1f;
					bwp.color = glm::vec4(1.f);
					bwp.voxPath = L"data/vox/1.vox";
					//bwp.connect = 1;
					createCubes(bwp);
				}
				else if (CCubeGridSceneNode::curCube()) CCubeGridSceneNode::curCube()->resetCubes(ke.Control);
#endif
			}
			//Ctx->pym->runScript(1);
			//processHandFrames();
		}
		break;
		
 
		case irr::KEY_F5: {
			//VkDrv->UseOIT = VkDrv->UseOIT == 1 ? 2 : 1; break;
			//sendKeyEvent(KEY_KEY_W);
			//scMmdFrame = 0;
			//break;
#if 0
#elif 0
			sendKeyEvent(KEY_KEY_V); sendKeyEvent(KEY_KEY_V); sendKeyEvent(KEY_KEY_M,1,0,1,0);
#elif 0
			{//sand hammer
				auto s0 = curChar(); if (!s0->Rb0()->GetActivation()) s0->setAllDynRbActive(true);
				s0->Pmx->moveAllOffset(float3(0, 20, 0));
				PMXFileCreateParam pm{ "D:/MMD/PMX/Y/ntMln/scL.pmx" };
				auto s1 = loadSabaModel(pm, false, 0x101)->sb;
				s1->Pmx->moveAllOffset(float3(0, 5, 0));
				s1->ctrForce = 3.f; s1->rcvEqvData = 1;
				auto nd = s1->findNode(L"root");				if (!nd) break;

				//s0->Pmx->connectRb(nd->rb0, s0->ndHandL->rb0, 1, { 2,13.75,-3 }, 1, { 0,1.5,-0 });
				//s0->Pmx->connectRb(nd->rb0, s0->ndHandR->rb0, 1, { -2,13.75,-3 }, 1, { 0,-1.5,-0 });

				//s0->Pmx->connectRb(nd->rb0, s0->ndFootL->rb0, 1, { 2,2.5,0 },	1);
				//s0->Pmx->connectRb(nd->rb0, s0->ndFootR->rb0, 1, { -2,2.5,0 },	1);

				PMXJoint jt{};
				jt.translate = vec3(0, 12.7, 3.96);
				jt.limitMinT = vec3(-1, -3, -1);
				jt.limitMaxT = vec3(1, 3, 1);
				jt.setLocalPos = true;
				jt.springT = vec3(1000.f);
				jt.springR = vec3(1000.f);
				jt.dampingT = vec3(1.f);
				jt.dampingR = vec3(1.f);
				s0->Pmx->connectRb(nd->rb0, s0->ndCtr->rb0, false, false, jt);
				jt.translate.y += s0->ndUpper2->mGlobalInit[3].y - s0->ndCtr->mGlobalInit[3].y;
				s0->Pmx->connectRb(nd->rb0, s0->ndUpper2->rb0, false, false, jt);
				jt.t2B = vec3(0, 0, 0);
				jt.springT = vec3(1000.f); jt.springR = vec3(0.f);
				jt.translate = vec3(2, 10,1);
				s0->Pmx->connectRb(nd->rb0, s0->ndLegL->rb0, false, false, jt);
				jt.translate = vec3(-2, 10, 1);
				s0->Pmx->connectRb(nd->rb0, s0->ndLegR->rb0, false, false, jt);  
			}
#elif 0
			{//shark
				auto s0 = curChar(); if (!s0->Rb0()->GetActivation()) s0->setAllDynRbActive(true);
				s0->Pmx->moveAllOffset(float3(0, 20, 0));
				PMXFileCreateParam pm{ "D:/MMD/PMX/Y/ntKqn/zdj.pmx", };
				auto s1 = loadSabaModel(pm, false, 0x101)->sb;
				s1->Pmx->moveAllOffset(float3(0, 5, 0));
				s1->ctrForce = 3.f; s1->rcvEqvData = 1;
				auto nd = s1->findNode(L"root");				if (!nd) break;

				if (s0->getItemIdx() ==2) s0->Pmx->connectRb(nd->rb0, s0->ndHandL->rb0, 1, {1.5,3.75,-1}, 1, {0,1.5,-0});
				if (s0->getItemIdx() == 4) s0->Pmx->connectRb(nd->rb0, s0->ndHandR->rb0, 1, { -1.5,3.75,-1 }, 1, { 0,-1.5,-0 });
				
				//s0->Pmx->connectRb(nd->rb0, s0->ndFootL->rb0, 1, { 2,2.5,0 },	1);
				//s0->Pmx->connectRb(nd->rb0, s0->ndFootR->rb0, 1, { -2,2.5,0 },	1);

				PMXJoint jt{};
				jt.translate = vec3(0, 5, 3.9);
				jt.limitMinT = vec3(-1, -1, -1);
				jt.limitMaxT = vec3(1, 2, 1);
				jt.setLocalPos = true;
				jt.springT = vec3(10.f);
				jt.springR = vec3(1000.f);
				jt.dampingT = vec3(1.f);
				jt.dampingR = vec3(1.f);
				s0->Pmx->connectRb(nd->rb0, s0->ndCtr->rb0, false, false, jt);
				jt.translate = vec3(0, 12, 3);
				//s0->Pmx->connectRb(nd->rb0, s0->ndUpper2->rb0, false, false, jt);
				jt.t2B = vec3(0, 0, 0);
				jt.translate = vec3(2, 3, 1);
				s0->Pmx->connectRb(nd->rb0, s0->ndLegL->rb0, false, false, jt);
				jt.translate = vec3(-2, 3, 1);
				s0->Pmx->connectRb(nd->rb0, s0->ndLegR->rb0, false, false, jt); 
		}
#elif 1
#if GAMESCENE_PLATE
			if (!ke.Control) {
				sendKeyEvent(KEY_MEDIA_PLAY_PAUSE,true,false,ke.Shift,ke.Alt); 		
				//PMXFileCreateParam pm{ "D:/MMD/PMX/Y/ntMln/shk.pmx", };
				//auto s1 = loadSabaModel(pm, false, 0x101)->sb;
			}
			else 
#endif
			{
				FrameWaiter waiter;
				static saba::MMDJoint* jt = 0;
				if (jt) {
					sb0->ndHead->rb0->addTorque(vec3(-100, 0, 0));
					sb0->appendLipMorph("o", 0.3f);
					jt->getRb(1)->addLinearVel(sb0->ndHead->transformVec({ 0,70,-200 }));
					jt->destroy(); jt = 0;
				}
				else {
					jt = sb0->Pmx->connectRb(sb0->ndHead->rb0, sb0->findNode(L"tieHead")->rb0, 1, { 0,-1.2,-0.96 }, 1, { -2.6,0,0 });
					waiter.waitNframeAndRun(1, [=](FWTask& t) {
						sb0->ndHead->rb0->addTorque(vec3(200, 0, 0));
						}, 6, 1).waitNframeAndRun(30, [=](FWTask& t) {
							if (jt)
							jt->setBreakThreshold(2000.f);
							});
				}
				return 0;
				IrrSaba* s0 = curChar() ;
				struct SD {
					IrrSaba* s1=nullptr;
				};
				auto sd=std::make_shared<SD>();  //local var (s1) in lambda out of scope

				s0->onSvNoteCb = [=](int ch, SvNoteData& note) {
					DP(("onSvNoteCb %d %d  my %d", ch, note.key, Ctx->gd.mouseY / 10));
					s0->ndHead->rb0->addTorque(vec3(note.key - 36, 0,  0) * (-Ctx->gd.mouseY / 1000));
					//sd->s1->findNode(L"stick")->rb0->addLinearVel(vec3(0, note.key - 36, 0) * (Ctx->gd.mouseY));
					};

				
				s0->loadAnimation(L"d:/mmd/vmd/sitOnZdj.vmd");
				DP(("Callback frame0 %d", Ctx->gd.frameCount));
				waiter.waitNframeAndRun(2, [=](FWTask& t) {
					DP(("Callback frame1 %d", Ctx->gd.frameCount));
					if (!s0->Rb0()->GetActivation()) s0->setAllDynRbActive(true);

					PMXFileCreateParam pm{ "D:/MMD/PMX/Y/ntKqn/zdjO.pmx", };
					IrrSaba* s1 = loadSabaModel(pm, false, 0x101)->sb; 
					sd->s1 = s1;
					s1->Pmx->moveAllOffset(s0->ndRoot->getGlobalPos()+float3(0, 1, 0));
					s1->ctrForce = 1.f; s1->rcvEqvData = 1;
					auto nd = s1->findNode(L"stick");
					if (!nd) return;
					//s0->Pmx->connectRb(nd->rb0, s0->ndHandL->rb0, 1,	{ 2,3.7,0 }, 1, {0,1.5,0});
					// s0->Pmx->connectRb(nd->rb0, s0->ndHandR->rb0, 1,	{ -2,3.7,0 }, 1, { 0,-1.5,0 });
					//s0->Pmx->connectRb(nd->rb0, s0->ndFootL->rb0, 1, { 2,2.5,0 },	1);
					//s0->Pmx->connectRb(nd->rb0, s0->ndFootR->rb0, 1, { -2,2.5,0 },	1);
					  
					PMXJoint jt{};
					
					jt.limitMinT = vec3(-1, -3, -1);
					jt.limitMaxT = vec3(1, 3, 1);
					jt.setLocalPos = true;
					jt.springT = vec3(200.f);
					jt.springR = vec3(1000.f);
					jt.dampingT = vec3(1.f);
					jt.dampingR = vec3(1.f);
					
	
#if 1
					jt.translate = vec3(0, 7, -0.33);
					s0->Pmx->connectRb(nd->rb0, s0->ndCtr->rb0, 1, 1, jt);
					jt.translate = vec3(0, 12, -0.33);
					s0->Pmx->connectRb(nd->rb0, s0->ndUpper2->rb0, false, false, jt);
					jt.springT = vec3(2000.f);
					jt.translate = vec3(1, -2.3, 1);
					s0->Pmx->connectRb(nd->rb0, s0->ndFootL->rb0, 1, 1, jt);
					jt.translate = vec3(-1,-2.3, 1);
					s0->Pmx->connectRb(nd->rb0, s0->ndFootR->rb0, 1, 1, jt);
#else
					s0->Pmx->connectRb(nd->rb0, s0->ndCtr->rb0, false, false, jt);
					jt.translate = vec3(0, 5, 2);
					s0->Pmx->connectRb(nd->rb0, s0->ndUpper2->rb0, false, false, jt);
					//jt.t2B = vec3(0, 1, 1); jt.springT = vec3(2000.f);
					jt.translate = vec3(1, 1, -1);
					s0->Pmx->connectRb(nd->rb0, s0->ndLegL->rb0, false, false, jt);
					jt.translate = vec3(-1, 1, -1);
					s0->Pmx->connectRb(nd->rb0, s0->ndLegR->rb0, false, false, jt);
#endif


					auto tn = s1->findNode(L"stick");
					//s0->ndLegL->rb0->addLinearVel(tn->GetGlobalTransform()* vec4(0, 600, -1000, 1));
				//	s0->ndLegR->rb0->addLinearVel(tn->GetGlobalTransform()* vec4(0, 600, -1000, 1));

					s1->findNode(L"WingL")->SetScale(vec3(2.f));
					s1->findNode(L"WingR")->SetScale(vec3(2.f));
					 //std::function<void(int ch, SvNoteData& note)> onSvNoteCb;


					}
				).waitNframeAndRun(1, [=](FWTask& t) {
						DP(("Callback frame3 %d", Ctx->gd.frameCount));
						sd->s1->Pmx->addBodyVel(vec3(0, 10, 2));

					}, 10, 1
				);

			}
#elif 0
			sendKeyEvent(KEY_KEY_P); sendKeyEvent(KEY_KEY_O);   sendKeyEvent(KEY_F3); sendKeyEvent(KEY_KEY_W, 1, 1, 0, 0); sendKeyEvent(KEY_KEY_K);
#elif 1
			sendKeyEvent(KEY_KEY_W, 1, 1, 0, 0); sendKeyEvent(KEY_KEY_K);
			for (int i = 0; i < 10; i++) sendKeyEvent(KEY_KEY_Y);
			sendKeyEvent(KEY_KEY_M); sendKeyEvent(KEY_KEY_O); sendKeyEvent(KEY_KEY_P); sendKeyEvent(KEY_KEY_W, 1, 1, 0, 0); sendKeyEvent(KEY_KEY_W, 1, 1, 0, 1); sendKeyEvent(KEY_NEXT);  sendKeyEvent(KEY_NEXT); sendKeyEvent(KEY_NEXT);
			for (int i = 0; i < 10; i++) sendKeyEvent(KEY_KEY_Y);
			sendKeyEvent(KEY_KEY_M); sendKeyEvent(KEY_KEY_O); sendKeyEvent(KEY_KEY_P); sendKeyEvent(KEY_KEY_W, 1, 1, 0, 0); sendKeyEvent(KEY_KEY_W, 1, 1, 0, 1); sendKeyEvent(KEY_NEXT);  sendKeyEvent(KEY_NEXT); sendKeyEvent(KEY_NEXT);
			for (int i = 0; i < 10; i++) sendKeyEvent(KEY_KEY_Y);

#elif 1
			sendKeyEvent(KEY_KEY_P); sendKeyEvent(KEY_KEY_M,1,0,1,0); sendKeyEvent(KEY_F3, 1, 0, 0, 1);
#elif 1
			ModelCreateCD = 5; sendKeyEvent(KEY_KEY_R);
#elif 1	
			sendKeyEvent(KEY_KEY_P); sendKeyEvent(KEY_KEY_A); sendKeyEvent(KEY_KEY_A);
#elif 1	
			sendKeyEvent(KEY_F9); sendKeyEvent(KEY_F3);
			sendKeyEvent(KEY_KEY_G);
			sendKeyEvent(KEY_KEY_T); sendKeyEvent(KEY_KEY_T); sendKeyEvent(KEY_KEY_T); sendKeyEvent(KEY_KEY_T); sendKeyEvent(KEY_KEY_T);
			sendKeyEvent(KEY_END); sendKeyEvent(KEY_END); sendKeyEvent(KEY_END); sendKeyEvent(KEY_END); sendKeyEvent(KEY_END); sendKeyEvent(KEY_END); sendKeyEvent(KEY_END); sendKeyEvent(KEY_END); sendKeyEvent(KEY_END); sendKeyEvent(KEY_END);
			sendKeyEvent(KEY_KEY_F,1,0,0,1);
#elif 1	
			sendKeyEvent(KEY_F9);
			sendKeyEvent(KEY_KEY_E,1,0,1,0);
			sendKeyEvent(KEY_KEY_R);
#elif 1	
			arRoot->forEachArChild([=](SnArItem* sn) {
				sn->sb->setBonePhsActive(sn->sb->ndFootL->GetParent(), ke.Shift);
				sn->sb->setBonePhsActive(sn->sb->ndFootR->GetParent(), ke.Shift);
				}); 			
#elif 1	
			if (!it1) {
				sendKeyEvent(KEY_KEY_V);
				sendKeyEvent(KEY_KEY_M);
				sendKeyEvent(KEY_KEY_P);
				//sb0->Pmx->connectRb(sb0->ndHandL->rb0, sb1->ndHandL->rb0,float3(0,1,0),false,1);
				//sb0->Pmx->connectRb(sb0->ndHandL->rb0, sb1->ndCtr->rb0,float3(0,3,0),false,0);
			}
			sb0->setAllRbActive(false);
			//it0->saba->resetAnimation(0,0,30);
			//it0->saba->setBonePhsActive(it0->saba->ndHead, true);
			scMmdFrame = 0;
			sb1->setAllRbActive(true);
			//scaleMmdNode(it1->saba->ndCtr, 0.001f, 1);
			//it0->saba->Pmx->UpdateAllAnimation(0, 0, 1/60.f);

			//it0->saba->Pmx->UpdateAllAnimation(0, 0, 1/60.f);
			for (int i = 0; i < 1; i++) {
		 
				//it1->saba->ndCtr->rb0->SetCoMTranslate((it0->saba->ndHandR->getGlobalPos() + it0->saba->ndHandL->getGlobalPos())/2.f + float3(0, 9 - i, (9 - i) - 2));
				//it1->saba->Pmx->UpdatePhysicsBodiesToNodes();	
				//it1->saba->Pmx->resetRigidBodies();
				
			}
			
			
		 	sb0->resetAnimation(0,0,0);
			sb0->setPlaying(true);
#else
#if HAS_MIDI
#if FW_TO_MIDI
			Ctx->Midi.recordBegin();
#else
			if (Ctx->Midi.isPlaying()) Ctx->Midi.stopPlay();
			else {
				arRoot->forEachArChild([=](SnArItem* sn) {
					auto saba = sn->saba;;
					//saba->setAdAnim(0, 0.2f); saba->setAdAnim(1, 0.2f);
					//saba->loadAppendAnimation("d:/mmd/vmd/BACKUP/guqin/danceL.vmd");
					//saba->loadAppendAnimation("d:/mmd/vmd/BACKUP/guqin/danceR.vmd"); 
					});
				Ctx->Midi.startPlay(int(Ctx->gd.time * 1000), 1, curMidiFile, Eqv->mdTrackData);
#if GEN_MIDI_ASS
				extern const char* g_pszAssFmt;
				Ctx->Midi.midiToSubtitle(midiToSbtText, g_pszAssFmt);
#endif 
				
			}
#endif
#else
#endif
#endif
		}
						break;

		case irr::KEY_F2:
		{
#if MMD_SAVE_VMD
			if (sb0 && sb0->Pmx->saveVMD) sb0->recordVmdEnd();
			for (int i=0;i<2;i++)
			if (itSns[i] && itSns[i]->sb->Pmx->saveVMD ) itSns[i]->sb->recordVmdEnd();

#endif		
			stopRecord();

			

#if SVG_MMD_WRITE
			if (it0) { it0->sb->setWriting(false);  it0->sb->Drawing = (false); }
#endif
			//restartAnimation();
			if (ke.Control)			vp.openOutDir(1);
		}
			//curChar()->charAtk.adjForceOnTime = !curChar()->charAtk.adjForceOnTime;
			break;
		case irr::KEY_F3:
			//if (!ke.Alt && !ke.Control && !ke.Shift)
			if (ke.Alt)
			{
				Ctx->gd.usingCamRbMat = false;
				if (snCam) snCam->setVisible(true);
				//lockIt = 0x11;
				auto ss = Driver->getScreenSize();
				if (!Ctx->gd.CamFpv) {
					auto cam = Ctx->gd.CamFpv = SceneManager->addCameraSceneNode();
					Ctx->gd.CamFpv->setName("CamFpv");
					Ctx->gd.CamFpv->supportPhysics = false;
					cam->bindTargetAndRotation(true);
					cam->setFOV(CAM_DYN_RB?Ctx->gd.CamRtt->getFOV():105 * core::DEGTORAD);
					if (CAM_DYN_RB) mmd->camSb = nullptr;
					cam->setNearValue(0.01f * MMD_SABA_SCALE); cam->setFarValue(CAMFAR );

					 
				 
				}
				else {
					camRb->moveToPos(Ctx->gd.CamRtt->getPosition()/MMD_SABA_SCALE, 0, 1);
				}

				auto vi = MULTI_VIEW_COUNT < 2 ? 0 : lockItViewId;
				if (MULTI_VIEW_COUNT >= 2) {
					if (Ctx->getViewCamera(vi) == Ctx->gd.CamFpv) {
												Ctx->setViewCamera(Ctx->gd.CamRtt, vi);
					}
					else {
						Ctx->setViewCamera(Ctx->gd.CamFpv, vi);
					}
				}
				//lockItViewId = 0; Ctx->mainViewId = 1;
				//Ctx->setViewState(0, 1, {0,0,0,0},ss.Width,	ss.Height *1/4);
				if (MULTI_VIEW_COUNT >= 2) {
					bool portait = ss.Width < ss.Height;
					Ctx->setViewState(ev_fpv, 1, { 0,0,0,0 }, portait? ss.Width:ss.Width/2, portait?ss.Height * 1 / 2:ss.Height);
				}
				if (lockIt)
					lockIt = 0;
				else
					lockIt = ke.Control ? 2 : 1;
				//InitCameraByBuffSize(60);
			}
			else if (  ke.Shift || Ctx->getCameraId() == 0 && lockIt) {
				if (Ctx->getCameraId() == 0) {
					camDisLock = !camDisLock;
				}
				else {
					Ctx->gd.CamNormal->setPosition(Ctx->gd.CamRtt->getPosition());
					Ctx->gd.CamNormal->setTarget(Ctx->gd.CamRtt->getTarget());
					Ctx->setCameraId(0);
					
					lockIt = 1;
				}
			}
			else if (lockIt)
				lockIt = 0;			 
			else 
				lockIt = ke.Control?2:1;

			


			//Eqv->ptrGrassMode = !Eqv->ptrGrassMode;
			//Eqv->ptrControlHand = !Eqv->ptrControlHand;
			break;
		case irr::KEY_F4:
			//curChar()->Pmx->moveAllOffset({ 0,10,0 });
			//toSaveScrShot = true;
		{
			if (ke.Control)
			{
				mmd->connectSabas(0,-1);
				break;
			}
			lastFollowCamQR = quat(1, 0, 0, 0);
			Ctx->gd.camRttOnSbVel = !Ctx->gd.camRttOnSbVel;
			
			//sb0->loadBaseAnimation(1, L"data/mmd/base1vmd/10Lani.vmd"); sb0->ndCtr->setTreePhyAnimRat(1);
			//frameAddObj = frameAddObj^1;
		}
			break;
		case irr::KEY_F1:
			if (ke.Shift && ke.Alt) 	mirrorLR = !mirrorLR;
			else if (ke.Control && ke.Alt)	mMrNeedRefresh = 1;
			else if (ke.Control)
			{
				Eqv->LaunchFw3D(   vec3(0,3000,0),Eqv->getPtrFw(1,0)->gfd.FwId, vec3(1000));
				//mmd->MPA->loadFromFile("data/motion/MPA/MPA1.json");
			}
			else {
#if 1				

				auto ssb0 = itSns[0]->sb, ssb1 = itSns[2]->sb;
				 ssb0->loadAnimation(L"data/mmd/recMotion_guo.vmd");
				//ssb1->loadAnimation(L"R:/recMotion_New MMD Model.vmd");
#if 1
				sb0->setAllDynRbActive(true); sb0->setPhyAnim(0, false, true);
				sb0->ndRbRoot->SetScale(vec3(2));
				PMXJoint jt{};
				jt.limitMinT = vec3(-1);
				jt.limitMaxT = vec3(1);
				jt.setLocalPos = true;
				jt.springT = vec3(10.f);
				jt.springR = vec3(10.f);
				jt.dampingT = vec3(1.f);
				jt.dampingR = vec3(1.f);
				jt.translate = vec3(0, 19, -10);
				jt.rotate = glm::vec3( 0,piFloat,0 );
				sb0->Pmx->connectRb(ssb0->Rb0(), sb0->Rb0(), false,false,jt );
				jt.translate = vec3(0, 25, -10);
				sb0->Pmx->connectRb(ssb0->Rb0(), sb0->ndUpper2->rb0, false, false, jt);
				jt.translate = vec3{ -3,12,-6 };
				sb0->Pmx->connectRb(ssb0->Rb0(), sb0->ndHandL->rb0, false, false, jt);  
				jt.translate = vec3{ 3,12,-6 };
				sb0->Pmx->connectRb(ssb0->Rb0(), sb0->ndHandR->rb0, false, false, jt);
				//sb0->Pmx->connectRb(itScene->sb->Rb0(), sb0->ndFootL->rb0, true, { 6,5,-3 }, 0);
				//sb0->Pmx->connectRb(itScene->sb->Rb0(), sb0->ndFootR->rb0, true, { -6,5,-3 }, 0);

				//sb1->ndRbRoot->SetScale(vec3(2));
				//sb1->Pmx->connectRb(ssb1->Rb0(), sb1->ndHandR->rb0, true, {-6,16,20}, 0);
				//sb1->Pmx->connectRb(ssb1->Rb0(), sb1->ndHandL->rb0, true, {6,16,20 }, 0);
#endif
				

#elif 0
				 saba::PMXFileCreateParam cp{ .filePath = //"D:/MMD/ITEMS/Toy/motianlun.pmx",//
					 "D:/MMD/ITEMS/Toy/waterMill1.pmx",
					 
					 .massMul = 1.f};
				 int N = 12; float sc = 0.57f;
				 cp.modelScale = 1; cp.modelScaleVec3 = vec3(sc);
				 cp.duplicateAndRotatePass = 1;
				 cp.copyCount = N - 1;
				 cp.copyTrsInc = vec3(0, 0, 0);
				 cp.copyRttInc = vec3(360.f / N,0, 0);// vec3(0, 360 / (cp.copyCount + 1), 0);				
				 cp.skipMaterialCopy = 1;
				 cp.skipFirstNbones = -1;
				 cp.copyUvOfs = 1;
				 itScene = loadSabaModel(cp, false, 0x1101);  itScene->sb->ndRoot->SetAnimationTranslate({ 0,17,0});
#if 1//hang
				 sb0->pinOnRoot(glm::translate(mat4(1), vec3(0, -30, 0))); sendKeyEvent(KEY_KEY_I, 1, 0, 1, 0);
				 sb0->ndArmL->forEachSubNodes([=](saba::MMDNode* nd) {	 nd->phyAnimRatT = nd->phyAnimRatR = 0;},true);
				 sb0->ndArmR->forEachSubNodes([=](saba::MMDNode* nd) { nd->phyAnimRatT = nd->phyAnimRatR = 0; },true);
#endif
				// PMXWriter writer; writer.writeFile(&itScene->sb->Pmx->pmx, "R:/save.pmx");
#elif 0
 
				sendKeyEvent(KEY_F3, 1, 0, 0, 1); sendKeyEvent(KEY_F3);  				
				sendKeyEvent(KEY_F5); sendKeyEvent(KEY_KEY_M); sendKeyEvent(KEY_F5); sendKeyEvent(KEY_KEY_M); sendKeyEvent(KEY_F5);
				sendKeyEvent(KEY_KEY_I); sendKeyEvent(KEY_NEXT, 1, 0, 0, 0); sendKeyEvent(KEY_NEXT, 1, 0, 0, 0); sendKeyEvent(KEY_NEXT, 1, 0, 0, 0); sendKeyEvent(KEY_NEXT, 1, 0, 0, 0); sendKeyEvent(KEY_NEXT, 1, 0, 0, 0); sendKeyEvent(KEY_NEXT, 1, 0, 0, 0);

#elif 0
 
				if (MMD_HAS_WHEEL) {
					for (auto sb : mmd->sabas) {

						if (ke.Shift || !sb->wheel) {
							if (!sb->wheel) {
							PMXFileCreateParam fcp1("D:/MMD/ITEMS/wheel/wheelFlw.pmx"); fcp1.collisionId = sb->modelCollisionId;
							auto sbw = sb->wheel = loadSabaModel(fcp1, false, 0x101)->sb;
							 
							//sb->setPhyAnim(sbw->ndCtr, false, true);
							sb->rd.phyAniMul = 0.f;
							
							arRoot->setCurSn(it0);
							float r = 6.2f;
#if 0
							PMXJoint jt{};
							jt.limitMinT = vec3(-0);
							jt.limitMaxT = vec3(0);
							jt.rotate = vec3(piFloat/2, 0, 0);
							jt.setLocalPos = true;
							jt.springT = vec3(20000.f);
							jt.springR = vec3(10000.f);
							jt.dampingT = vec3(100.f);
							jt.dampingR = vec3(100.f);
							sb->Pmx->connectRb(sb->ndUpper->rb0, sbw->Rb0(), false, false, jt);
#else
					 
							sb->Pmx->connectRb(sbw->Rb0(), sb->ndHandR->rb0, 0, { 0, r * 0.9f, -r*0.9f }, 0);
							sb->Pmx->connectRb(sbw->Rb0(), sb->ndHandL->rb0, 0, { 0, r * 0.9f, r * 0.9f }, 0);
							sb->Pmx->connectRb(sbw->Rb0(), sb->ndFootR->rb0, 0, { 0, -r,-r }, 0);
							sb->Pmx->connectRb(sbw->Rb0(), sb->ndFootL->rb0, 0, { 0, -r,r }, 0);
#endif
							}
							sb->ndRoot->SetAnimationTranslate(vec3(0, 22, 0)); 		
							sb->setAllDynRbActive(false, 1);
							sb->wheel->Rb0()->SetCoMTransform(sb->ndRoot->mGlobalAnim * glm::translate(mat4(1),vec3(0,17,0)));
						}
						else {
							auto rb=sb->wheel->Rb0();
							rb->addTorque(vec3(1, 0, 0));
						}
					}


				}
				 
					 
				 
#elif 1
				int testPmxGen();
				testPmxGen();
				PMXFileCreateParam fc(
					//"chain_model.pmx"
					"brick_wall_model.pmx"
				); 
				fc.forceReload = true;
				loadSabaModel(fc);
				//sb0->startWalking({ 0,0,-1 });
		 

#elif 1
				SceneManager->WSADMode = ISceneManager::eRig;
				sendKeyEvent(KEY_KEY_E);
				SceneManager->WSADMode = ISceneManager::eMove;
				sendKeyEvent(KEY_F3); sendKeyEvent(KEY_F3, 1, 0, 0, 1); sendKeyEvent(KEY_F4); sendKeyEvent(KEY_F10);
				sendKeyEvent(KEY_KEY_O);
				sendKeyEvent(KEY_KEY_K);
 
				for (int i = 0; i < 9; i++) {
					
					sendKeyEvent(KEY_KEY_M); sendKeyEvent(KEY_KEY_O); 
					
					auto sb = curSaba();
					FRAMEWAITER_CALL_BI(a, 1 + i * 10) {
						sb->lookAt_mmdLookAt = true;
						sb->ndRoot->SetScale(vec3(5.f));
						sb->Pmx->moveAllRbTo(vec3(0, 30+i*30, 0));
					});
				}
 				arRoot->setCurSn(it0,1);
#else
				static int cc = -1; cc++;
				int c = mmd->sabas.size();
				if (cc == 0)	for (int i = 0; i < 1; i++) {
					SceneManager->WSADMode = ISceneManager::eRig;
					mmd->catcherCount = c;
					newCombineDance(mmd->sabas[i]);
				}
				else {
					sb0->startSbMPA({ "data/rigFrSave.json" });
				}
				if (mmd->MPA) mmd->MPA->resetSabas();
#endif
 
			}
			//arRoot->saveItems();
			break;
		case irr::KEY_F6:
			if (ke.Control)
			{
				mFwBlendId = (mFwBlendId + 1) % 4;	mMrNeedRefresh = 1;	DP(("Blend %d", mFwBlendId));
			}
			else { 

#if  MA_POLE_DANCE
				if (auto ndUp = sbScene->findNode(L"StickA"))
				{
					if (!curChar()->Rb0()->GetActivation()) {
						sendKeyEvent(irr::KEY_KEY_P); sendKeyEvent(irr::KEY_KEY_O); sendKeyEvent(irr::KEY_NEXT); sendKeyEvent(irr::KEY_NEXT); sendKeyEvent(irr::KEY_NEXT); sendKeyEvent(irr::KEY_NEXT); sendKeyEvent(irr::KEY_NEXT); sendKeyEvent(irr::KEY_NEXT); sendKeyEvent(irr::KEY_NEXT); sendKeyEvent(irr::KEY_NEXT); sendKeyEvent(irr::KEY_NEXT);

					}
					poleDance = 1;
				//	curChar()->Pmx->connectRb(curChar()->ndHandR->rb0, ndUp->rb0, true, {1 ,1 + curChar()->getItemIdx() * 3,1}, false, {});
					static int watchGen = 0;
					if (!watchGen) {
						watchGen = 1;
						for (int i = 0; i < 10; i++) {
							//mmd->Pm.spm.collisionId = sb0->modelCollisionId;
							auto sbw = loadSabaModelScale({ "D:/MMD/PMX/JQL/dog/watchNLink.pmx" }, 1.5f, false, 0x0)->sb;
							//mmd->Pm.spm.collisionId = -1;
							sbw->Pmx->connectRb(ndUp->rb0, sbw->findNode(L"root")->rb0, true, { 0,0,0 }, false, {});
							//sbw->Pmx->connectRb(sbw->findNode(L"watch")->rb0,sb0->ndHandR->rb0,  true, { 3.27f,0,0 }, false, {});
						}
					}
				}
#else
				{

					//int c = mmd->sabas.size();
					//for (int i = 0; i < c; i++) 		if (mmd->sabas[i]->tsb) mmd->sabas[i]->cat.catchEnd();
					mmd->catcherId = (mmd->catcherId + 1) % mmd->catcherCount;
					mmd->sbLastCatcher = mmd->sbCatcher;
					mmd->sbCatcher = curChar()->tsb? curChar(): mmd->sbLastCatcher;
					auto tsb = mmd->sbLastCatcher->tsb;
					mmd->sbLastCatcher->cat.catchEnd();
					static int cc = -1; cc++;
					mmd->sbCatcher->cat.catchStart(tsb,
						20
						//(cc%2)*10+10
					);
				}
#endif
			}
			//copyEqvdef();
			//Eqv->recreateOnUpdate();
			break;
		case irr::KEY_F7:
			 
			if (ke.Alt) Driver->setShadowOn(!Driver->getShadowOn());
			else if(ke.Control)	IFppt->debugFw = !IFppt->debugFw;		 
			else if (ke.Shift) Ctx->useMidRT = !Ctx->useMidRT;
			else { sb0->togglePhyDebugVisual(); arRoot->Cs.pickPhysics = true; }
			
			break;
		case irr::KEY_F12:


			if (ke.Control)
			{
				arRoot->saveItems();
			}
			else
			{
				static auto str=ualib::stringLoadFromFile("aritems.json");
				//arRoot->loadItems(str.c_str(), 0);
				evo.CmdInput.pm1 = (int64_t)str.c_str();
				evo.CmdInput.cmdId = 0x10000000 | cceLoadItemsStart;
				StageOnCmdEvent(evo.CmdInput);
			}
			break;
		case irr::KEY_F11:
		{
			if (mmd) mmd->resetFluid();

			//PMXWriter writer;	writer.writeFile(&curSaba()->Pmx->pmx, "r:/pmx.pmx");

			//sb0->Pmx->A_CopyBoneWeightFromBtoC("D:/MMD/Blender/New Folder/jql_bns/2.pmx"
			//	, "D:/MMD/Blender/New Folder/jql_bns/1rbqup.pmx", "D:/MMD/Blender/New Folder/jql_bns/wfWeighted.pmx"
			//	, 10.f
			//);
			////sb0->Pmx->A_CopyBoneWeightFromBtoC("D:/MMD/Blender/New Folder/jql_bns/tmp.pmx"
			////	, "D:/MMD/Blender/New Folder/jql_bns/fpx.pmx", "D:/MMD/Blender/New Folder/jql_bns/wfWeighted.pmx"
			////	, 0.1f
			////);
			break;

			mmd->mdplr.recordStart(); mmd->mdplr.recordEnd(); //read file
			for (int i = -1; i <= 1; i++)	saveMidiWithLyric(mmd->mdplr.bpm, i * 20);
		}
		case irr::KEY_F10:
		{
			VkDrv->toggleUI(); 
#ifdef _WIN32
			//SceneManager->WSADMode = ISceneManager::EWSADModes(Driver->renderUI ? 1 : 0);
			if (Driver->renderUI)
			{
				while (ShowCursor(FALSE) >= 0);
			}
			else while (ShowCursor(TRUE) < 0);
#endif
		} 
			break;
		case irr::KEY_F8:
			if (ke.Shift)
			 vp.snPly->setVisible(!vp.snPly->isVisible());
			else if (ke.Control) {
				core::matrix4 im, idm = sb0->getAbsoluteTransformation()  ;
				idm.getInverse(im); core::matrix4 mmdSc; mmdSc.setScale(0.1f);
				vp.colmapWriteObj( mmdSc*  core::matrix4(glm::rotate(glm::mat4(1), core::PI, glm::vec3(0, 1, 0)))*im * vp.snPly->getAbsoluteTransformation() );
				core::matrix4 ipm;
				if (vp.snPly->getParent()->getAbsoluteTransformation().getInverse(ipm))
				vp.snPly->setMatrix( ipm * idm * core::matrix4(glm::rotate(glm::mat4(1), core::PI, glm::vec3(0, 1, 0))));
			}
			//else if (it2) {
			//	sb0->Pmx->connectRb(it2->sb->ndHandL->rb0, sb0->ndHandR->rb0);
			//	sb1->Pmx->connectRb(it2->sb->ndHandR->rb0, sb1->ndHandL->rb0);
			//	it2->sb->ndRoot->SetAnimationTranslate({0,30,0});
			//}
			else {
#if 1
				static MMDNode* targetingNode = {};
				if (mmd->lastPickNode) {
					if (mmd->lastPickNode->model->saba->Pmx->tgtNode && mmd->lastPickNode->pmxBone->anim && mmd->lastPickNode->pmxBone->anim)
						targetingNode = mmd->lastPickNode;
					else if (mmd->lastPickNode->rb0->dynRbType)
						targetingNode->model->saba->Pmx->tgtNode = mmd->lastPickNode; targetingNode->model->saba->Pmx->tgtNodeChgTime = gSceneTime;
				}
#elif 1
				reloadScene();
#else
				auto rbL = sb0->Pom->addAnchor({ 6, 25, 0 });
				auto rbR = sb0->Pom->addAnchor({ -6, 25, 0 });
				sb0->Pmx->connectRb(rbL, sb0->ndHandL->rb0,true, { 0,1.0,0 });
				sb0->Pmx->connectRb(rbR, sb0->ndHandR->rb0, true, { 0,1.0,0 });
#endif
			}
			//else vp.snPly->passTypeFlags = (vp.snPly->passTypeFlags&IrrPassType_OnlyDepth)? IrrPassType_Normal: IrrPassType_OnlyDepth;
			break;
		case irr::KEY_F9:
		{
			auto sb = curChar();
			if (sbScene && sbScene->Pmx->anchorRBs.size()) {
				static int cc=-1; cc++;
				int id = cc % sbScene->Pmx->anchorRBs.size();
				//sb->Pmx->connectRb(sbScene->Pmx->anchorRBs[id], sb->ndHandL->rb0, true, { -1,0,0 }, false, {});
				sb->Pmx->connectRb(sbScene->Pmx->anchorRBs[id], sb->ndHandR->rb0, true, { 0,0,0 }, false, {});
				break;
			}
 
			
			if (ke.Control) 
				sb->to_rootLockMat = !sb->to_rootLockMat;
			else {
#if SVG_MMD_WRITE
				//sendKeyEvent(KEY_KEY_F, pressed);
				arRoot->forEachArChild([=](SnArItem* sn) {
					auto sb = sn->sb;
					sb->Pmx->setDynRbActive(!sb->Pmx->getDynRbActive());
					sb->Pmx->resetRigidBodies();
					sb->pinWriteNodes();
					});

#else
				arRoot->forEachArChild([=](SnArItem* sn) {					
					auto sb = sn->sb;			//	if (sb->itemIdx == 0) return;
					//sn->saba->resetAnimation(0, MMD_REVERSE_PLAY, 30);
					if (sb->Pmx->GetNodeManager()->GetNodeCount() < 20) return;
					sb->setAllDynRbActive(true); 
					sb->pinOnRoot(sb->Pmx->GetNodeManager()->getRootNode()->GetGlobalTransform());
					if (ke.Shift)		sb->ndCtr->rb0->SetActivation(false);
					
					});
			//	arRoot->setCurSn(it0);				
#endif
			}
 
		}
			//mmd->paraRender = !mmd->paraRender;
			break;

		case irr::KEY_ADD:case irr::KEY_SUBTRACT://numadd numplus numpadplus
		{
#if DRAW_SHADER_TOY
auto& tc = Eqv->SdToy->stc;
if (tc.iv.x == 0)  tc.iv.x = 16;
if (ke.Key == irr::KEY_ADD) { if (tc.iv.x < 128)tc.iv.x *= 2; }
else if (tc.iv.x > 1) tc.iv.x /= 2;
#else
			if (ke.Shift)
				//Ctx->setFixFrameTime(true, Ctx->getFixFrameTime() * (ke.Key == irr::KEY_ADD ? 2.0:0.5));
				arRoot->forEachArChild([=](SnArItem* sn) {
				if (sn->sb) sn->sb->setAnimationSpeed(sn->sb->getAnimationSpeed() * (ke.Key == irr::KEY_ADD ? 2 : 0.5));
					},1);
			else if (ke.Control) { 
				changeSpeed(Ctx->gd.timeMul*(ke.Key == irr::KEY_ADD ? 2 : 0.5)*(ke.Alt ? (ke.Key == irr::KEY_ADD ? 8 : 1.0f / 8) : 1));
				DP(("TimeMul: %f,  PhyMul: %f" , Ctx->gd.timeMul, MMDPhysics::phyTimeMul)); 
			}
			else {		
				int c = (ke.Alt ? 5 : 1);
				for (int i=0;i<c;i++) MMDPhysics::phyTimeMul *= (ke.Key == irr::KEY_ADD ? 2.0 : 0.5);
			}
#endif
		}
		break;
		case irr::KEY_MULTIPLY://numpad_*
		{
			MMDPhysics::phyTimeMul = 1;
		}
		break;
		case irr::KEY_PLUS:case irr::KEY_MINUS: {  //_- _=
			int inc = (ke.Key == irr::KEY_PLUS ? 1 : -1);
			auto cam = SceneManager->getActiveCamera();

#if DRAW_SHADER_TOY
if (ke.Control) {
	if (ke.Key == irr::KEY_PLUS)  Eqv->SdToy->stc.fv1.y += 0.1f;
	else  Eqv->SdToy->stc.fv1.y -= 0.1f;
}
else
if (ke.Key == irr::KEY_PLUS)  Eqv->SdToy->stc.fv1.z += 0.1f;
else  Eqv->SdToy->stc.fv1.z -= 0.1f;
#else
			if (0) {}
			else if (ke.Shift && ke.Alt && ke.Control) {
				Driver->dsd.OitCb.minAlpha = core::clamp(Driver->dsd.OitCb.minAlpha + inc*0.01f, 0.0f, 1.f);
				DP(("OIT alpha %d", Driver->dsd.OitCb.minAlpha)); 
				//depthOutMul += inc * .1f;// 	cam->setNearValue(cam->getNearValue() + inc * 100);
			}
			else if (ke.Shift && ke.Alt) {
				Driver->dsd.OitCb.uMaxCount= core::clamp(Driver->dsd.OitCb.uMaxCount + inc*4, 1u, 32u);
				DP(("OIT Max %d", Driver->dsd.OitCb.uMaxCount));
				//depthOutMul += inc * .1f;// 	cam->setNearValue(cam->getNearValue() + inc * 100);
			}
			else if (ke.Alt && ke.Control) {
				//frameAddObjSpdMul *= (inc > 0 ? 1.1f : 1 / 1.1f);
				Ctx->gd.CamRtt->IPD = core::clamp(Ctx->gd.CamRtt->IPD * (inc > 0 ? 1.1f : 1 / 1.1f), 0.1f * 0.064f * 1000.f, 10.f * 0.064f * 1000.f);
			}
			else {
				if (MMD_CONTROL_SD) {
					camDepFar += inc * (ke.Control ? MMD_SCALE_OLD : MMD_SABA_SCALE);
					camDepFar = std::max(camDepFar, MMD_SABA_SCALE);	cam->setFarValue(camDepFar);
				}
				//else if (ke.Control) {
				//	objCenterForceMul = core::clamp(objCenterForceMul *(inc>0?1.5f:1/1.5f), 0.1f, 10.f);
				//}
				else if(ke.Alt) {
					//sb0->Pom->ballTime = core::clamp(sb0->Pom->ballTime * (inc > 0 ? 1.1f : 1 / 1.1f), 0.2f, 5.f);					
				}
				else if ( Ctx->gd.CamFpv && !ke.Control)
				{
					auto cam =  Ctx->gd.CamFpv;// SceneManager->getActiveCamera();
					cam->setFOV(std::clamp(cam->getFOV() * (inc<0?1.05f:1/1.05f), DEGTORAD*1, DEGTORAD*170));
				}
				else {
					
						if (!Ctx->gd.CamRtt) break;
						double mul = (ke.Key == irr::KEY_PLUS) ? 1.02 : 1.0 / 1.02;
						double fov = SceneManager->getActiveCamera()->getFOV();
						if (SceneManager->getActiveCamera() == Ctx->gd.CamNormal) {
							Ctx->gd.CamNormal->setFOV(fov * mul);
							break;
						}
						auto anm = ((irr::scene::CSceneNodeAnimatorCameraTouchControl*)*Ctx->gd.CamRtt->getAnimators().begin());
						double ft = anm->tld.fovyMul * mul;
						if (ft < 0.1 || ft>10) break;
						anm->tld.fovyMul *= mul;
						
						
					
				}
			}
#endif
		}
		break;

		case irr::KEY_LEFT:	case irr::KEY_RIGHT:
		{
			int inc = (ke.Key == irr::KEY_RIGHT ? 1 : -1);
			if (evtRcv->IsKeyDown(KEY_KEY_S))
			{
				curSaba()->springDefMode =  (curSaba()->springDefMode+inc) % SPRING_MODE_COUNT;
				return true;
			}
#if COLMAP_MODEL
			else if (vp.cmArr.size() > 0) {
				if (!ke.Control)
				{
					auto trs = vp.cmCamMatMul.getTranslation();  //vp.cmRoot->getRotation();
					trs.X += inc * (ke.Shift ? 100 : 10);
					vp.cmCamMatMul.setTranslation(trs);//vp.cmRoot->setRotation(rtt);					
					core::matrix4 m;	( vp.cmArr[vp.opCamId].omat *	vp.cmCamMatMul).getInverse(m);
					vp.cmRoot->setTransformTR(m);					
				}
				else {
					auto rtt = vp.cmCamMatMul.getRotationDegrees(); //vp.cmRoot->getRotation();
					if (ke.Shift) rtt.Y += inc; 				
					else rtt.Z -= inc;
					vp.cmCamMatMul.setRotationDegrees(rtt);//vp.cmRoot->setRotation(rtt);
					{
						core::matrix4 m;
						(vp.cmArr[vp.opCamId].omat *
							vp.cmCamMatMul).getInverse(m);
						vp.cmRoot->setTransformTR(m);
					}
					if (vp.colMapId >= 0) vp.cmArr[vp.colMapId].snCam->updateTransform();

				}
			}
#endif
			else   {
				auto sn = arRoot;// : (ISceneNode*)arRoot->curArSn->saba;//
				float ofs = inc * (!ke.Alt ? 10 : 0.1);
				if (!curChar()  ) 
					sn->setPosition(sn->getPosition() + vector3df(ofs * 100, 0,  0));
				else if (ke.Control && ke.Alt) {
					arRoot->setPosition(arRoot->getPosition() + vector3df{ ofs * 10, 0, 0 });
				}
				else if (ke.Control && ke.Shift) {
					float a = (ke.Key == irr::KEY_RIGHT) ? 1 : -2;

					arRoot->forEachArChild([=](SnArItem* sn) {
						float t = a;
						if (Ctx->scenePaused) {							
							t += sn->sb->getCurTime();
							loadVmd(0, true);
						}
						if (sn->sb) { sn->sb->animeTimeAdd(t ); }
						});
				}
				else {
					//auto n = p->GetNodeManager()->getRootNode();
					//n->SetAnimationTranslate(n->GetAnimationTranslate() + glm::vec3( 0, ofs, 0));
					mmd->curCtrlSb([=](irr::scene::IrrSaba* sb) {
						auto p = sb->Pmx;
						core::vector3df vec(ofs, 0, 0);
						if (ke.Shift) translateInActiveCamView(vec);
						else if (!ke.Control) sb->ndRoot->SetAnimationTranslate(sb->ndRoot->GetAnimationTranslate() + glm::vec3(vec * sb->ndRoot->GetScale().x/10));
						else p->rt1Tr += glm::vec3(vec * sb->ndRoot->GetScale().x / 10);
						});

				}
			}
			 
		}break;
		case irr::KEY_DOWN:	case irr::KEY_UP:
		{
			int inc = (ke.Key == irr::KEY_UP ? 1 : -1);

			if (evtRcv->IsKeyDown(KEY_KEY_S))
			{
				springMul += inc; return true;
			}
#if COLMAP_MODEL
			else if (vp.cmArr.size() > 0) {
				if (!ke.Control)
				{
					auto trs = vp.cmCamMatMul.getTranslation();  //vp.cmRoot->getRotation();
					trs.Y += inc * (ke.Shift ? 100 : 10);
					vp.cmCamMatMul.setTranslation(trs);//vp.cmRoot->setRotation(rtt);
					{
						core::matrix4 m;
						( vp.cmArr[vp.opCamId].omat *
							vp.cmCamMatMul).getInverse(m);
						vp.cmRoot->setTransformTR(m);
					}
				}
				else
				{
					auto rtt = vp.cmCamMatMul.getRotationDegrees();  //vp.cmRoot->getRotation();
					rtt.X += inc*(ke.Shift?10:1);
					vp.cmCamMatMul.setRotationDegrees(rtt);//vp.cmRoot->setRotation(rtt);
					DP(("RTT X %f", rtt.X));
					{
						core::matrix4 m;
						(vp.cmArr[vp.opCamId].omat *
							vp.cmCamMatMul).getInverse(m);
						vp.cmRoot->setTransformTR(m);
					}
				}
				if (vp.colMapId >= 0) vp.cmArr[vp.colMapId].snCam->updateTransform();
				break;
			}
#endif
			{
				auto sn = arRoot;// : (ISceneNode*)arRoot->curArSn->saba;//
				float ofs = inc * (ke.Alt ? (ke.Shift ? 1 : 100) : 10);  // Alt+Shift=1, Alt=100, else 10
				auto sb = curSaba();
				if (!sb )
					sn->setPosition(sn->getPosition() + vector3df(0, ofs * 100, 0));
				else if (ke.Control && ke.Alt) {
					//arRoot->setPosition(arRoot->getPosition() + vector3df{0, ofs*10, 0});
					mmd->curCtrlSb([=](irr::scene::IrrSaba* sb) {
						sb->mFloatY += ofs * 0.1f; ;
						});
				} 			
				else {
					mmd->curCtrlSb([=](irr::scene::IrrSaba* sb) {
						auto p = sb->Pmx;
						core::vector3df vec(0, ofs, 0);
						if (ke.Shift && !ke.Alt) translateInActiveCamView(vec);
						else 
							if (!ke.Control)
							{
								sb->ndRoot->SetAnimationTranslate(sb->ndRoot->GetAnimationTranslate() + glm::vec3(vec * sb->ndRoot->GetScale().x / 10));
								
							}
							else {
								p->rt1Tr += glm::vec3(vec * sb->ndRoot->GetScale().x / 10);
								
							}
							
						
						});

				}
			}
			 

		}break;
		case irr::KEY_HOME:	case irr::KEY_END:
		{
			int inc = (ke.Key == irr::KEY_HOME ? 1 : -1) * (!ke.Alt ? 10 : 1);
#if COLMAP_MODEL
			if (vp.cmArr.size() > 0) {
				auto rtt = vp.cmCamMatMul.getTranslation(); // vp.cmRoot->getRotation();
				 rtt.Z += inc*10;
				 
				vp.cmCamMatMul.setTranslation(rtt);//vp.cmRoot->setRotation(rtt);
				DP(("POS Z %f", rtt.Z));
				{
					core::matrix4 m;
					if (ke.Control) {
						
						vp.cmCamMatMul.setTranslation({ 0,0,0 }); 
						vp.cmCamMatMul.setRotationDegrees({ 0,0,0 });
						auto sc = vp.cmRoot->getScale();
						vp.cmCamMatMul.setScale({1/sc.X,1/sc.Y,1/sc.Z});
						vp.opCamId = vp.colMapId;
					}
					( vp.cmArr[vp.opCamId].omat * vp.cmCamMatMul).getInverse(m);
					vp.cmRoot->setMatrix(m);
				}		
				if (vp.colMapId >= 0) vp.cmArr[vp.colMapId].snCam->updateTransform();
				break;
			}	
#endif
			{
				auto sn = arRoot;// : (ISceneNode*)arRoot->curArSn->saba;//
				float ofs = inc  ;
				if (ke.Control && ke.Alt) {
					arRoot->setPosition(arRoot->getPosition() + vector3df{ 0, 0, ofs*10 });
				}
				else if (!curChar() || ke.Alt) sn->setPosition(sn->getPosition() + vector3df(0, 0,ofs*100)); else {

					mmd->curCtrlSb([=](irr::scene::IrrSaba* sb) {
						auto p = sb->Pmx;
						core::vector3df vec(0, 0, ofs);
						if (ke.Shift) translateInActiveCamView(vec);
						else if (!ke.Control) sb->ndRoot->SetAnimationTranslate(sb->ndRoot->GetAnimationTranslate() + glm::vec3(vec * sb->ndRoot->GetScale().x / 10));
						else	p->rt1Tr += glm::vec3(vec * sb->ndRoot->GetScale().x / 10);
						});



				}
			}
		}break;
		case irr::KEY_PRIOR:case irr::KEY_NEXT: //_page_up 
		{
			int inc = (ke.Key == irr::KEY_PRIOR ? 1 : -1);
			if (COLMAP_MODEL && vp.cmArr.size() > 0)
			{
				DP(("vp.colMapId %d", vp.colMapId));
				vp.colMapId = (vp.colMapId + vp.cmArr.size() + inc) % vp.cmArr.size();
				break;
			}
			if (ke.Control && ke.Alt) curChar()->phyMatAniTreeLevel = std::clamp(curChar()->phyMatAniTreeLevel + inc, 0, 10);
			
			else if (ke.Control && ke.Shift)
			{
				curChar()->rd.phyVelScaleOnAnim = std::clamp(curChar()->rd.phyVelScaleOnAnim * (inc > 0 ? 1.05f : 1 / 1.05f), 0.0f, 2.f);
			}
			else if (ke.Alt && ke.Shift)
			{
				mmd->curCtrlSb([=](irr::scene::IrrSaba* sb) {
					//auto sb = curChar();
					auto &sc = sb->Pmx->runTimeMassMulOnScale;	
					sc = std::clamp(sc * (inc > 0 ? 2.f : 1 / 2.f), 1/32.f, 1024.f);

					sb->Pmx->GetPhysicsManager()->updateAllRbMass();

					});
			}
			else if (ke.Alt)
			{
				static float gm = 1.f;
				float3 g(0, -9.8 * PHY_GRAVITY_MUL, 0);

				gm = gm * (pow(2, float(inc)));
				g = g * gm; gGravity = g;
				//sb0->rd.phyAniMul /= (pow(4, float(inc)));
				IFppt->SetGravityMul(gm);
				sb0->mmdPhysics->setGravity(g);
				sb0->ndFootL->rb0->addLinearVel({ 0,200,300 });
				sb0->ndFootR->rb0->addLinearVel({ 0,200,300 });
				sb0->ndUpper2->rb0->addLinearVel({ 0,0,-600 });

				Eqv->setPtrFwByIdStr(0, "label");
				testTextFw(true, ualib::wstrFmt(L"%.0fg", gm), 0x10);
			}
			else if (ke.Shift)
				mmd->curCtrlSb([=](irr::scene::IrrSaba* sb) {
				//auto sb = curChar();
				auto pn = sb->curPickingNode;
				if (!pn) pn = sb->ndUpper;
				pn->forEachSubNodes([=](saba::MMDNode* nd) {
					if (nd->rb0 && nd->rb0->dynRbType)
						nd->phyAnimRatR = nd->phyAnimRatT = std::clamp(nd->phyAnimRatT * (inc > 0 ? 1.5f : 1 / 1.5f), 0.0f, 2.f);
					}, true);
				pn = sb->ndLower;
				pn->forEachSubNodes([=](saba::MMDNode* nd) {
					if (nd->rb0 && nd->rb0->dynRbType)
						nd->phyAnimRatR = nd->phyAnimRatT = std::clamp(nd->phyAnimRatT * (inc > 0 ? 1.5f : 1 / 1.5f), 0.0f, 2.f);
					}, true);
				sb->ndUpper2->phyAnimRatR = sb->ndUpper->phyAnimRatR = sb->ndLower->phyAnimRatR = sb->ndCtr->phyAnimRatR = 0;
				sb->ndUpper2->phyAnimRatT = sb->ndUpper->phyAnimRatT = sb->ndLower->phyAnimRatT = sb->ndCtr->phyAnimRatT = 1;
					});
			else
			{
				mmd->curCtrlSb([=](irr::scene::IrrSaba* sb) {
					float m = ke.Control ? 8.f : 2.f;
					sb->rd.phyAniMul = std::clamp(sb->rd.phyAniMul * (inc > 0 ? m : 1.0f / m), 0.001f, 1.f);
					});

				
				return true;
				//if (ke.Control) 			arTimeOfsAutoIncUs += inc*33333;
				//else {
				//	arRoot->curArSn->mArTimeOfs = arRoot->curArSn->mArTimeOfs + inc*(ke.Shift ? 1.f : ke.Alt?0.01f:0.1f);//if (arRoot->curArSn->mArTimeOfs < 0) arRoot->curArSn->mArTimeOfs = 0;
				//}
			}
		}
		break;
		case irr::KEY_NUMPAD8:	case irr::KEY_NUMPAD5:
		{
			int inc = (ke.Key == irr::KEY_NUMPAD8) ? 1 : -1;
			if (COLMAP_MODEL && vp.cmArr.size() > 0) {
				arRoot->setScale(arRoot->getScale() * (10.f + inc) / 10);
				break;
			}
			else if (ke.Alt)
			{
				Ctx->gd.baseLightPos.y += inc * MMD_SABA_SCALE;
				Ctx->gd.lightPosChanged = true;
			}
			else {
				Eqv->arCamPosMul = Eqv->arCamPosMul * (10.f + inc) / 10;
				IFppt->setPtScale(Eqv->arCamPosMul);
			}
 
			break;
		}
		case irr::KEY_NUMPAD7:	case irr::KEY_NUMPAD9:
		{
			float a = (ke.Key == irr::KEY_NUMPAD9) ? 1 : -1;
			//if (ke.Control)
			//{
			//	auto sb = curChar();
			//	sb->Pmx->runTimeMassMulOnScale *= a>0? (1.1):1.0/1.1;
			//	sb->ndRoot->SetScale(sb->ndRoot->GetScale());
			//}
 
			break;
		}
		case irr::KEY_NUMPAD4:	case irr::KEY_NUMPAD6:
		{
			float inc = (ke.Key == irr::KEY_NUMPAD6 ? 1 : -1);
			if (ke.Control && ke.Alt) {
				
				if (snSkyDome) {
					snSkyDome->setRotation(snSkyDome->getRotation() + vector3df(0, inc * 10, 0));
					if (snSkyDomeM) snSkyDomeM->setRotation(snSkyDome->getRotation() );
					arRoot->Cs.skyDomeRttY = snSkyDome->getRotation().y;
				} 
			}
			else if (ke.Alt) {
				arRoot->Cs.lightRttYdeg += inc * 10;
				Ctx->gd.lightPosChanged = true;
			}
			else if (ke.Control)
			{
				arRoot->forEachArChild([=](SnArItem* sn) {
					if (sn->sb->Rb0()->GetActivation()) return;
					sn->sb->frameRtt = true;
					if (ke.Alt)
						sn->sb->frameRttVec += glm::vec3(0, 0, -inc * core::DEGTORAD);
					else
						sn->sb->frameRttVec += glm::vec3(0, -inc * core::DEGTORAD, 0);
					}, 0x1000);

			}
 
			else {
				//auto sn = ke.Alt ? arRoot : (ISceneNode*)arRoot->curArSn->saba;
				//sn->setRotation(sn->getRotation() + vector3df(0, inc, 0));
				mmd->curCtrlSb([=](irr::scene::IrrSaba* sb) {
					auto p = sb->Pmx;
					p->rt1Rt += glm::vec3(0, inc * (ke.Control ? 90 : 10) * core::DEGTORAD*gTimeMul, 0);
					});
			}

			break;
		}
		case irr::KEY_NUMPAD2:
		{
			if (vp.cmArr.size() > 0 && (vp.colMapId >= 0)) {
				vp.cmCamMatMul.setTranslation({ 0,0,100 });//vp.cmRoot->setRotation(rtt);
				core::matrix4 m;
				(vp.cmArr[vp.colMapId].omat * vp.cmCamMatMul).getInverse(m);
				vp.cmRoot->setRotation(m.getRotationDegrees());
				if (ke.Control) 
					vp.cmRoot->setPosition(m.getTranslation());				
			}

		}break;
		case irr::KEY_MEDIA_STOP:
			frameSleep = frameSleep?0:100;
			break;
		case irr::KEY_MEDIA_PLAY_PAUSE:
		{
#define MIDIPLATE_MULTI_SB 1
#define MIDIPLATE_SHOUPENG 1  //f5 m f5 m f5 ...
#define MIDIPLATE_NODE_BALL 1
			CMidiPlateSceneNode::Param pm{};

			pm.is0 = Plates.size()==0; 
			if (!dropInMidiFile)
				curMidiFile = "D:/Tmp/AIMUSIC/ganyuC2.mid";//"data/midi/yoasobi1.mid";//"data/midi/国际歌.mid";//bee.mid";//sddlm.mid"; //				//"D:/Tmp/MIDI_Show/AI/tzl.mid";
				//"D:/Tmp/MIDI_Show/MORE_TODO/Flower Dance.mid";				
				//	"D:/Tmp/MIDI_Show/!PianoEQV/Lemon.mid";//hanabi.mid"; //[B]Xmas.mid";//  "aLIEz T7.mid";// 
 
			pm.sb0 = curAiChar();
			
			if (pm.sb0)
			{
				auto sb0 = MIDIPLATE_MULTI_SB ? pm.sb0 : mmd->sb0;
				sb0->canLookAtCam = 1;
#define POD42 0
#if POD42
				PMXFileCreateParam spm{ "D:/MMD/PMX/n/2B/na_pod042_0101h/pod042_0102.pmx", };
				IrrSaba* s1 = loadSabaModelScale(spm, 1.0f, false, 0x101)->sb;// sb0;// ;
#endif
				
				pm.path = ualib::AnsiToWchar(curMidiFile);
				bool foot = ke.Alt;
#if MIDIPLATE_SHOUPENG

				static int subcc = 0;
				if (foot) {
					pm.sb0 = sb0 = curChar();

					pm.subAct = 1;
					static int tiv[] = { 0,1,2,3,4,5,6,7,8,9 };
					static int tiR[] = { 1,0,1,0,1,0,1,0,1,0 };
					pm.trackInv = tiv[subcc];
					pm.subActR = tiR[subcc++];
					(pm.subActR ? sb0->ndLegR : sb0->ndLegL)->setNodePhyAnimRat(0.01f, 1, false);
				}
				else {
					static int tma[] = { 7,5,3,1,2,4,6, // 7sb
						//9,7,5,3,1,2,4,6,8, //9
						1,1,1,1,1,1,1,1,1
					};
					//pm.track = tma[sb0->getItemIdx()];
				}
				pm.actMode = 1;
				vec3 ofs = sb0->ndUpper2->mGlobalInit[3] - sb0->ndCenter->mGlobalInit[3];
				if (!foot) {
					pm.pos = ofs; pm.pos.x = 0;  pm.pos.z = -sb0->Pmx->yaoPos.y * 0.3f;
					pm.movPowMul = { 0,3,0 }; pm.movPowAdd = { 0,sb0->getItemIdx() >= BASE_CHAR_NUM ? -3 - BASE_CHAR_DY : -3,0 };//pm.movPowMul = { 0,9,0 }; pm.movPowAdd = { 0,-9,0 };
				}
				if (foot) { pm.pos = ofs; pm.pos.x = 0; pm.pos.y = -2.f; pm.pos.z = -sb0->Pmx->yaoPos.y * 0.35f; }
				pm.nbIsSb = 1;
				pm.mass = 1000 ;
				pm.ballRadiusX2 = pm.nbIsSb?1.0f:3.f;//3.f;
				pm.ballVisible = !MIDIPLATE_NODE_BALL;				pm.noteBall = MIDIPLATE_NODE_BALL;  CMidiPlateSceneNode::nbp.extraFlyTime = 1.f;
				pm.ballStartOffsetY = 1.f;// 30.f;
				pm.brickSize = vec3(2, 0.2f, 2) * 0.2f;
				pm.circleR = 3.5f;
				pm.fixPosOnHit = 1;
				 		pm.dustColor = 0x80C0C0FF;
				pm.plateOnSb = MIDIPLATE_MULTI_SB;

				if (!foot) {
					if (HAS_PIANO && curMidiFile.size() > 1) {

						Ctx->Midi.startPlay(int64_t(Ctx->gd.time * 1000), 1, curMidiFile, &Eqv->mdTrackData);
						pm.path = ualib::AnsiToWchar(curMidiFile);
						pm.midiDelay = MIDI_DELAY;
						pm.extenalMidi = 1;
					}
					//sendKeyEvent(KEY_HOME); sendKeyEvent(KEY_HOME); sendKeyEvent(KEY_HOME); 
					if (sb0->getItemIdx() >= PLATE_PLAYER_SKIP) {
						sb0->loadAnimation(sb0->getItemIdx() >= BASE_CHAR_NUM ? //"data/mmd/shouPeng_knee.vmd"
							"data/mmd/shouPeng_sitGround.vmd"
							: "data/mmd/shouPeng_legOp.vmd");
						sb0->animCycleStart = -1; sb0->setPlaying(1);//sb0->updateAnimationToFrame(1);//					 sb0->animCycleStart = 0;
						if (!sb0->Rb0()->GetActivation()) {
							sb0->setAllDynRbActive(true); if (!sb0->ndCtr->phyAnim) sendKeyEvent(KEY_KEY_O);  //  sb0->ndRoot->SetScale(vec3(0.5f));
						}
						sb0->rd.phyAniMul = 0.25f;
						//sb0->ndCtr->setNodePhyAnimRat(20.0f, false, false);
						//sb0->ndUpper->setNodePhyAnimRat(20.0f, false, false);
						//sb0->ndUpper2->setNodePhyAnimRat(20.0f, false, false);
						sb0->ndArmL->setNodePhyAnimRat(0.5f, 1, false);	sb0->ndArmR->setNodePhyAnimRat(0.5f, 1, false);
						sb0->ndHandL->setNodePhyAnimRat(0.25f, 1, 1);	sb0->ndHandR->setNodePhyAnimRat(0.25f, 1, 1);
					}
				}
#else
				//pm.actMode = 2;
				pm.pos = { 0,19,0 };
				pm.mass = 10000;
				pm.ballRadiusX2 = 3.6f;
				pm.ballStartOffsetY = 30.f;
				pm.brickSize = vec3(2, 0.1f, 2) * 1.f;
				pm.circleR = 10.f;
				pm.fixPosOnHit = 1;
				if (HAS_PIANO && curMidiFile.size() > 1) {
					Ctx->Midi.startPlay(int64_t(Ctx->gd.time * 1000), 1, curMidiFile, &Eqv->mdTrackData);
					pm.path = ualib::AnsiToWchar(curMidiFile);
					pm.midiDelay = MIDI_DELAY;
					pm.extenalMidi = 1;
				}
				//pm.sb =  s1;
				//pm.beats = beatMan.beats;
				// sendKeyEvent(KEY_F3, 1, 1);
				//sb0->ndRoot->SetScale(vec3(3));
			if (mmd->sabas.size() < 2) {
				sb0->setPlaying(true); sb0->animCycleStart = 0;  sb0->setAllDynRbActive(true); sendKeyEvent(KEY_KEY_O);//  sb0->ndRoot->SetScale(vec3(0.5f));
				//sendKeyEvent(KEY_KEY_I); sendKeyEvent(KEY_KEY_V); //sendKeyEvent(KEY_KEY_K);
				for (int i = 0; i < (IS_WIN_DBG ? 5 :7); i++) {
					sendKeyEvent(KEY_KEY_M);   sendKeyEvent(KEY_KEY_O);
					curChar()->rd.phyAniMul = 0.001f;
				}
				// mmd->sabas[1]->ndHitBall = mmd->sabas[1]->ndUpper2;
				//sendKeyEvent(KEY_SUBTRACT, 1, 1); //sendKeyEvent(KEY_SUBTRACT, 1, 1);
			}
#endif
				auto plate= new CMidiPlateSceneNode(arRoot->snMmdSpaceRoot, SceneManager, mmd);				  
				Plates.emplace_back(plate);
				curMdPlate = Plates.size() - 1;
				 
				Plates[curMdPlate]->drop();
				Plates[curMdPlate]->recreateCubes(&pm);
				if (!foot) 
				{
					plate->onFinish = [=]() {
						FrameWaiter fw; fw.waitNframeAndRun(180, [=](FWTask& t) {
							sendKeyEvent(KEY_KEY_C);
							if (HAS_PIANO && curMidiFile.size() > 1) {
								Ctx->Midi.startPlay(int64_t(Ctx->gd.time * 1000), 1, curMidiFile, &Eqv->mdTrackData);

							}
							for (auto p : Plates) {
								p->recreateCubes(0);
							}

							});
						};
				}
#if POD42
				saba::PMXJoint jp{}; jp.setLocalPos = true;
				jp.springT = glm::vec3(10000);
				jp.springR = glm::vec3(1000);
				jp.dampingR = glm::vec3(0);
				jp.translate = {};
				jp.limitMinT = glm::vec3(-3);
				jp.limitMaxT = glm::vec3(3);
				jp.limitMinR = glm::vec3(-10);
				jp.limitMaxR = glm::vec3(10);
				 
				jp.translate = { 0,-2,0 };  

				jp.t2B = { 0,0,0 }; sb0->Pmx->connectRb(s1->Rb0(), sb0->ndHandL->rb0, 0, 0, jp);
#endif
			}
			else {
				//Plates[0]->Pm.sb = s1;

				Plates[0]->createBall();
				 
			}
			 
		}break;
		case irr::KEY_MEDIA_NEXT_TRACK: {// KEY_/?
			//sendKeyEvent(KEY_KEY_B, 1, 0, 1, 0); sendKeyEvent(KEY_KEY_I, 1, 0, 0, 1);
			sb0->resetAnimation();
			sb0->loadCamera(MMD_CAM_FILE, false); sb0->Pm.activeNodeCam = true;
			sb0->setPlaying(true);

		}break;

		}
	}
	else
	{
		//DP(("up"));
	}
	return false;
}

bool AppNameSpace::AppMainAMP::shotR(const irr::SEvent::SKeyInput& ke, bool& retFlag)
{
 
	retFlag = true;
	addMmdObjParam pm = Ctx->gd.apm; if (Ctx->gd.apm.RCombine) pm.noRtt = true;
	pm.flag = (ke.Shift ? 1 : 0) | (ke.Alt ? 0x0010000 : 0) |   (OBJ_FLY_TO_MMD_CONN ? 0x1000 : 0);
	if (ke.Control && ke.Alt || Ctx->gd.apm.RCombine) pm.flag |= 0x2000;
	else if (ke.Control) pm.flag |= 0x10;
 
	float r = 2.f, d = 0.f; int N = Ctx->gd.apm.countRt * Ctx->gd.apm.countRt;
	if (mmd->Pom->phyObjs.size() + N > VKDRV_MAX_FFMR_ITEM_COUNT - 192) return false;
	if (N == 1)
	{
		if (Ctx->gd.apm.checkMode) sendKeyEvent(KEY_KEY_C);
		if (ke.Control && ke.Shift) {
			for (auto& sb : mmd->sabas) {
				arRoot->setCurSn((SnArItem*)sb->getParent());
				addMmdObj(pm);
			}
			arRoot->setCurSn(it0);
		}
		else addMmdObj(pm);		

	}
	else
		for (int i = 0; i < N; ++i) {
			// Generate random angles and radius for uniform sphere distribution
			float theta = static_cast<float>(rand()) / RAND_MAX * 2 * M_PI;     // azimuthal angle [0, 2π]
			float phi = std::acos(2.0f * static_cast<float>(rand()) / RAND_MAX - 1.0f);  // polar angle [0, π]
			float randomR = r * std::cbrt(static_cast<float>(rand()) / RAND_MAX);  // radius with cubic root for uniform distribution

			pm.camOfs = {
				randomR * std::sin(phi) * std::cos(theta),
				randomR * std::sin(phi) * std::sin(theta),
				randomR * std::cos(phi)
			};
			addMmdObj(pm);
		}
	retFlag = false;
	return {};
}

 
//FROM ChatGPT
void calculateJointStiffnessAndDampingOfRopeUnit(float rbMass, float& stiffness, float& damping, float frequency = 60.0f*3, float damping_ratio = 0.7f) {
	// Calculate stiffness using the formula k = mass * (2 * pi * frequency)^2
	stiffness = rbMass * std::pow(2.0f * M_PI * frequency, 2.0f);

	// Calculate damping using the formula c = 2 * sqrt(mass * stiffness) * damping_ratio
	damping = 2.0f * std::sqrt(rbMass * stiffness) * damping_ratio;
}
 



void AppMainAMP::attachObjToMmd(irr::scene::IrrSaba* sb, int lcount)
{
	int objType = 2;
	if (Ctx->getEvtRcv()->isMouseKeyDown(0)) objType = 100;
	const int lineCount =   lcount; //max 32 irrsaba.attachObjLast[32]
	int No_Self_Collision = 0;
	int& count = sb->adsbcc, y = 0; count = (count + 1); y = count / 5;
	//sb0->actVoiceFx(8, 5, 60, ualib::AnsiToWchar(numToChinese(count +1)));// , ls[UaRand(ls.size())]);
	int lx = count % lineCount, ly =   count / lineCount;
	//if (x >= maxX) break;
	saba::PMXFileCreateParam fcp{"D:/MMD/PMX/JQL/dog/watchNrc.pmx" };// watchNLink
	const wchar_t* rootName = L"root", *lastObjName=L"watch";
	bool collisionCallback = true, randomPos = false;
	float baseY = 0.f;
	saba::PMXJoint jt{}; jt.setLocalPos = true; float scale = 1.0f; bool rootLockRtt = 0; jt.lockPos = true;
	jt.springT = glm::vec3(100);
	jt.springR = glm::vec3(100);
	//jt.translate = { 0,-1.25f,0 };
	jt.limitMaxT = glm::vec3(0, 0, 0); 
	jt.limitMinR = glm::vec3(-PI/2, 0, 0);
	jt.limitMaxR = glm::vec3(PI/2, 0, 0);
	fcp.massMul = 50.f / pow(1.0f, ly);
	vec3 cnctOfs = { 0,-1.25f * (ly)+baseY,0 };
	float a = 0, R = 5;
	//auto ndB = count % 2 ? sb->ndArmL : sb->ndArmR, ndE = count % 2 ? sb->ndHandL : sb->ndHandR;
	auto ndB = count % 2 ? sb->ndLegL : sb->ndLegR, ndE = count % 2 ? sb->ndFootL : sb->ndFootR;
//	ndB = sb->ndLower; ndE = sb->ndLower;
	switch (objType)
	{
	case 0:
		baseY = -1.27f * scale; jt.translate = float3(0, -0.5f, 0) * scale; jt.t2B = float3(0, 0.5, 0) * scale; fcp.massMul = 1.f / pow(1.1f, ly);
		ndB = sb->ndCtr; ndE = sb->ndLower;
		a = glm::degrees(count * 33.f);
		R = 3;
		cnctOfs = { cos(a) * R,0,sin(a)*R};
		break;
	case 1: //Qing Yi weapon
		fcp.filePath = "D:/MMD/PMX/JQL/qy/wp.pmx";  scale = 1.0f; rootLockRtt = true; fcp.massMul = 0.01f;
		baseY = -3.75f; jt.translate = { 0, -2.0f * scale,0 }; lastObjName = L"StickB"; rootName = L"StickA";// L"StickB"; 
		//collisionCallback = false; 
		ndB = sb->ndCtr; ndE = sb->ndLower;
	break;		
	case 2: //
		fcp.filePath = "D:/MMD/ITEMS/yumao/drop.pmx"; scale = 0.5f;  rootName = L"watch"; jt.springR = glm::vec3(100, 1000, 1000);
		baseY = -3.f * scale; cnctOfs = { 0, baseY,0 }; 
		jt.translate = float3( 0,-1.5f,0)* scale; jt.t2B= float3(0,1.5,0)* scale;
		fcp.massMul = 1.f / pow(1.5f, ly);
		jt.springT =  vec3(1000000) ;
		jt.dampingT = jt.springT*0.03f; jt.dampingR= vec3(1);
		jt.limitMinT = glm::vec3(  -110 );
		jt.limitMaxT = glm::vec3(  11111 );
		jt.lockPos = 0;
		
		//No_Self_Collision = 1;
		break;

	case 3: //
		fcp.filePath = "D:/MMD/ITEMS/petal/petal.pmx"; // "D:/MMD/PMX/Y/ntMln/sch.pmx" ; 
		scale = 1.0f;  rootName = L"root"; lastObjName = L"root"; jt.springR = glm::vec3(100, 1000, 1000);
		baseY = -0.f * scale; jt.translate = float3(0, 0.f, -2) * scale; jt.t2B = float3(0, 0, 0) * scale;
		
		break;
	case 4: //
		fcp.filePath =  "D:/MMD/PMX/Y/ntMln/sch.pmx" ; 
		scale = 0.05f;  rootName = L"root"; lastObjName = L"root"; jt.springR = glm::vec3(100, 1000, 1000);
		baseY = -3.f * scale; jt.translate = float3(0, -1.f, 0) * scale; jt.t2B = float3(0, 2.2, 0) * scale;

		break;
	case 5: //Xinjianya Blade
		fcp.filePath = "D:/MMD/PMX/JQL/xjy/dao.pmx"; scale = 0.5f;  rootName = L"root"; jt.springR = glm::vec3(1, 1, 1);
		baseY = -1.27f * scale; jt.translate = float3(0, -0.5f, 0) * scale; jt.t2B = float3(0, 0.5, 0) * scale; fcp.massMul = 1.f / pow(1.1f, ly);
		ndE = sb->findNode(lx % 2 ? L"右手首" : L"左手首"); ndB = ndE->GetParent();
		 
		break;
	case 6: //
		fcp.filePath = "D:/MMD/ITEMS/DRESS/skirt/1.pmx"; scale = 0.5f;    rootName = L"下半身";  //jt.springR = glm::vec3(100, 1000, 1000);
		baseY = -10.f * scale; jt.translate = float3(0, -1.f, 0) * scale; jt.t2B = float3(0, 1, 0) * scale;  fcp.massMul = 10.f / pow(1.5f, ly);
		//No_Self_Collision = 1;
		break;
	case 100:
		fcp.filePath = pmxEqvCharfiles[0]; scale = 0.2f; fcp.massMul = 1.f / pow(1.5f, ly);  fcp.massMul *= 0.001f;   rootName = L"左手首"; jt.springR = glm::vec3(100, 1000, 1000);
		baseY = -2.f * scale; jt.translate = float3( 0,-1.f,0)* scale; jt.t2B= float3(0,1,0)* scale;
		fcp.massMul = 0.1;
		break;	
	}
	
	DP(("WATCH %d %d", lx, ly));
	if (ly >= 60 || fcp.massMul < 1.e-20)
		return;
	if (No_Self_Collision) fcp.collisionId = 0;
	auto sbw = loadSabaModelScale(fcp, scale, false, 0x101)->sb;
	if (!sbw->Rb0()->GetActivation()) sbw->setAllDynRbActive(true);
 
 
	if (objType >= 100) {
		sbw->setAllDynRbActive(true);
	}
	//sb->Pmx->connectRb(nd->rb0, sbw->findNode(L"root")->rb0, true, {(x%50)/2.f,y/2.f,0}, true, {});
	//sb->Pmx->connectRb(sb->ndCtr->rb0, sbw->findNode(L"root")->rb0, true, {(x%2?-1:1)* (x % 50) / 2.f,5+y / 2.f,2 }, true, {});
 
	auto rbA = sb->attachObjLast[lx]; 
	if (!rbA)
		rbA = ndE->rb0;
	auto ndB1 = sbw->findNode(rootName);
	auto rbB = ndB1?ndB1->rb0:sbw->Rb0();
	if (ly == 0) {

		sb->Pmx->connectToRootNodeRatio(
			ndB, ndE,
			rbB=sbw->findNode(rootName)->rb0, ((lx) / 2 + 1) / (lineCount / 2.f), true,cnctOfs, rootLockRtt);
	}
	if (ly > 0) {
 
 
		((PMXModel*)rbB->m_mmdModel)->moveAllOffset(rbA && !randomPos?rbA->getPosition() :
			  UaRandVec3() * 60.f + float3(0, 60, 0)
			- rbB->getPosition());
		mmd->sb0->Pmx->connectRb(rbA, rbB,  jt);

	}
	
	if (objType >= 100) rbB = sbw->findNode(L"右手首")?sbw->findNode(L"右手首")->rb0:sbw->Rb0();
	else rbB = sbw->findNode(rootName)->rb0;
	sb->attachObjLast[lx] = rbB;
	auto rb = rbB;

	if (collisionCallback)
		Ctx->setSharedRbCb(rb);
}

void AppMainAMP::updateLightPos(int _)
{
	{
		

		auto pos = (it0?it0->getAbsolutePosition():vector3df{0,0,0}) + irr::core::quaternion(0, arRoot->Cs.lightRttYdeg * DEGTORAD, 0) * (Ctx->gd.baseLightPos * MMD_SABA_SCALE);
		FLOATS_FROM_VEC3(arRoot->Cs.lightPos, pos);
		if (snLight) snLight->setPosition(pos);
	}
}

void AppMainAMP::addMmdObj(ualib::addMmdObjParam apm)
{
#define TGT_RND_SB  0
#define OPPAI_SAVER 0
#define FRAME_BOX	0
#define MASS_SCALE 1.0f   //0.1f
	struct SS{
		std::string fn;
		float size;
	};
	static SS ss[] = {
			{"poGrassFlw",2},{"poGrassRed",1},{"poFwBox",5},{"poFlower" ,2}, {"poFireball",1.5},{"poFlowerG",1.5},{"poFw",3},{"poFw",3},{"poFw",3},{"poFw",3},{"poFw",3},{"poFw",3},{"poFw",3},{"poFw",3},
	}; 
	int flag = apm.flag;
	bool isMmd = flag & 0x00000001;
	bool isConn = flag & 0x00000010;
	bool isBand = flag & 0x00000100;
	bool flyConn = flag & 0x00001000;
	bool linkSbs = flag & 0x0002000;
	bool camOnBox= flag & 0x00010000;
	bool actAnim = flag & 0x00020000;
	float timer = 30;
	int isOverridePos = 0; vec3 overridePos = vec3(0);
	static int cc = 1;
	saba::MMDNode* cnnToNode{};
	float size = ss[ballFwId].size+cc*0.f;
		//UaRand(5) + 2;
	auto cam = SceneManager->getActiveCamera();			

	PMXFileCreateParam cp;
	cp.modelScale = true;
	float sc = apm.scale;// *0.5f;				Z				//SIZE BASE
	cp.modelScaleVec3 = float3{ sc,sc,sc };
	
	irrSabaParam spm;
	
	cp.modelScale = true; cp.modelScaleVec3 = float3(sc*0.9f);	//	#			
	if (!isBand) cp.massMul = sc*  MASS_SCALE * apm.density;//based on scaled mass. 0.016f / (sc * sc * sc);	//cp.noFriction = true;
	IrrSaba* osb = nullptr;  static IrrSaba* lsb{};
	
	if (isBand || isMmd) {
		//mmd->Pm.spm.allowGPU = 0;
		cp.filePath = pmxEqvCharfiles[apm.setMmdIdx>=0? std::min(apm.setMmdIdx, (int)_countof(pmxEqvCharfiles)-1):cc % _countof(pmxEqvCharfiles)];
		osb = loadSabaModel(cp, false, 0x1101)->sb;
		mmd->Pm.spm.allowGPU = 1;
	}
	else if (linkSbs) { 
		static const char* files[] = {
			  "data/mmd/model/ropeTrack1.pmx",  "D:/MMD/ITEMS/Toy/waterMillFix.pmx","D:/MMD/ITEMS/petal/ropeBase.pmx" ,   "D:/MMD/ITEMS/petal/leafBase1.pmx" , "D:/MMD/ITEMS/petal/leaf2.pmx" , "D:/MMD/ITEMS/petal/leaf3.pmx" , "D:/MMD/ITEMS/petal/leaf1.pmx" ,  "D:/MMD/ITEMS/petal/leafBase.pmx" , "D:/MMD/ITEMS/petal/ropeFlw.pmx" ,"D:/MMD/ITEMS/petal/ropeAnim.pmx" ,
			
			"D:/MMD/ITEMS/petal/petalCopyBaseD1.pmx" , "D:/MMD/ITEMS/petal/petalCopyBase.pmx" ,"D:/MMD/ITEMS/petal/petalAnim.pmx" , "D:/MMD/PMX/Y/wls/hb2.pmx", "D:/MMD/PMX/Y/wls/hb.pmx","D:/MMD/ITEMS/petal/petalNoBr.pmx" ,
		};
		cp.filePath = isConn ? "D:/MMD/ITEMS/petal/petalNoBr.pmx" :
			files[apm.dupModelId % _countof(files)];
		cp.duplicateAndRotatePass = apm.dupCount>1?1:0;
		cp.copyCount = apm.dupCount-1;
		cp.copyTrsInc = apm.dupTrsInc;
		cp.copyRttInc =  apm.dupRttInc;// vec3(0, 360 / (cp.copyCount + 1), 0);
		cp.copyScaleInc = vec3( apm.dupScaleInc);
		
		cp.skipMaterialCopy = 1;
		cp.combineMode = apm.combineMode;
		cp.skipFirstNbones = cp.combineMode==0 ? -1 : 0;
		cp.modVertexWeight = cp.combineMode == 1;
		cp.forceReload = apm.checkMode;
		
		if (itScene)
			cp.collisionId = itScene->sb->modelCollisionId;
		//mmd->Pm.spm.allowGPU = 0; //allow vtxfw
		osb = loadSabaModel(cp,   false, 0x001)->sb;
		mmd->Pm.spm.allowGPU = 1;
		if (apm.dupToSave) {
			apm.dupToSave = 0;
			PMXWriter writer; writer.writeFile(&osb->Pmx->pmx, "R:/save.pmx");
		}
		//isOverridePos = 1; overridePos = { 0,32,0 }; cp.copyRttInc.x = 360.f / apm.dupCount;
		MMDNode* nds[] = { //curAiChar()->ndCtr,curAiChar()->ndUpper,curAiChar()->ndLower,
			 curAiChar()->ndArm1L,curAiChar()->ndArm1R,curAiChar()->ndLeg1L,curAiChar()->ndLeg1R
		};
		osb->Pmx->tgtNode = nds[cc % _countof(nds)];
		//isConn = true; cnnToNode = curChar()->findNode(L"subRbBase");
		timer = 300;
	}
	else if (FRAME_BOX || 0) {
		cp.filePath = pmxBallfiles[cc % _countof(pmxBallfiles)];
		osb = loadSabaModel(cp, false, 0x001)->sb;
	}
	
	if (osb) osb->objId = cc;

	PhyObjParam pm{ 32,  //0:box, 1:ball  , 11:ball.obj  32:32.OBJ(bcoin)
		size * size * size * apm.density,{ size,size,size },{ 0,0,0 },{},{ 0,0,30 } };
	switch (pm.poType)
	{
	case 1:
		pm.instancedMesh = L"data/mesh/ball.obj";
		pm.color = 0xFF00FF00;
		break;

	case 32:
	{
		static auto sms = SceneManager->getMesh("data/mesh/coin.obj");
		pm.size = { pm.size.x * 1.414,  pm.size.y / 3, pm.size.z * 1.414 };
		pm.size = pm.size * sc / 2.f; 	pm.meshScale = float3(sc);
		pm.mesh = sms; pm.pmxrb.m_shape = PMXRigidbody::Shape::Cylinder;
		static int mcc = 0;
		static const std::wstring mfn[] = { L"data/mesh/coin.obj",L"data/mesh/kd.obj",L"data/mesh/bowl.obj" };
		//pm.size = vec3(2, 1, 2);
		pm.instancedMesh = mfn[mcc++ % 1]; // "data/mesh/coin.obj";
	}break;
	}
 
	pm.hitSbTest = true; 
	pm.ragDollPm = { .dur0 = -1, .dur1 = 1 };
	
	pm.isBall = true;
	pm.dummySn = true;
	pm.pmxrb.m_repulsion = 0.5f;
	pm.pmxrb.m_friction = MMD_ZERO_FRICTION?0: apm.friction;
	pm.visible = 1; pm.bandId = cc;
	pm.autoKill = true;  
	pm.timer = isBand ? 300 :timer;			//============ TIMER ============
	pm.atkFlag =
#if SBATK_CATCHER
		(1 << ENdId::eYa)
#else
		actAnim ? 0 :
		0  //| (1 << ENdId::eoL) | (1 << ENdId::eoR)  
		  | (1 << ENdId::efL)				| (1 << ENdId::efR)
		 | (1 << ENdId::ehL) 			| (1 << ENdId::ehR) // |
		  |(1 << ENdId::eHd)//
		//| (1 << ENdId::eYa) 
		//|(1 << ENdId::elL) | (1 << ENdId::elR)	
#endif
		;
	//pm.srcSb = it1 ? it1->sb : 0; if (it1) it1->sb->charAtk.enable = 0;
	auto rndsb = mmd->getSaba(-1); if (!rndsb) rndsb = curSaba();
	if (pm.srcSb) {
		pm.camShoot = false; pm.pos = pm.srcSb->ndHandR->getGlobalPos() + float3(0, pm.size.y, 0);
	}
	else pm.camShoot = true;
	if (apm.fromEmitter && itScene) {
		auto nd = itScene->sb->findNode(L"emitter0");
		pm.camShoot = false;
		if (nd)  pm.pos = nd->getGlobalPos() + vec3(apm.camOfsCtr);
		else pm.pos = apm.camOfsCtr, (apm.camOfsCtr = vector3df(0,0,0));
	}
	float gridL = 12.50;
#if 1
	pm.camStartOfs = apm.camOfsCtr+apm.camOfs;// { 0,0,1 };  	
#else
	pm.camPosOverride = float3{ 0,27,-30 };  
#endif
	//pm.camPosOverride += float3(rndsb->mmd2irr(rndsb->ndCtr->getGlobalPos()));
	//pm.camShootOfs += float3{ cc % 3 * gridL - gridL,(cc / 3 % 3) * gridL - 300, 00 };  //{0,1500, 600 };  ////{cc%2?300:-300, 600, 600 };	
	pm.noRtt =  apm.noRtt;
	pm.toMMD = apm.toMMD;//  true; 			
	pm.ofsOnTgt = true; pm.tgtLocalOfs = float3(0, osb ?0: -6*sb0->ndRbRoot->absScale.y, 0); pm.tgtOfsRange = float3(1, 1, 1);
 
	if (pm.toMMD && 0) {
		float r = 1;
		float a = (6 + UaRand(7)) * core::PI * 2 / 12;
		pm.camStartOfs = float3(gridL * r * cos(a), gridL * 2 + 0* cam->getPosition().y/MMD_SABA_SCALE, gridL * r * sin(a));
	}

#if 0
	pm.hasFw = 1; pm.color = 0xFFF0C880;
	pm.fwIdx = Eqv->getFwIdxByFwIdStr(2, ss[ballFwId].fn);// "poFlowerG");
	//pm.fwSpdIdx = Eqv->getFwIdxByFwIdStr(2, "poSparkle");
	pm.fwSpdChgIdx = Eqv->getFwIdxByFwIdStr(2, "poSparkleBox");
	if (MMD_OBJ_CONNECT)  pm.fwOfs = float3(0,- pm.size.x * MMD_SABA_SCALE / 2,0);
	
	auto& vms = IFppt->vtxMats;
	auto pd = IFppt->vtxMats.dataPtr();
	if (vms.usedSize() < FW_VTX_MAT_MAX - 1) {
		pm.vtxMatId = vms.alloc({});
		pd[pm.vtxMatId].landType = 1;
	}	
	
#endif
 
	pm.p2pInTime = apm.p2p;  pm.p2pTime = 1.0f;
	pm.camAngle = osb ? 1:apm.angle;
	pm.spdLenMul =  apm.spdMul;		pm.speedAdd = vector3df( 0,  (osb?-0.2:0),0 )+ apm.dirOfs;
	pm.vel = { 0,1,0 };
	pm.sb = osb; 
	//pm.vmd = "D:\\Tmp\\magicbomb/1.vmd";
	//pm.autoScale = 1;
	
	static int cnc = 0;
	auto sb = curAiChar();
	auto lastConnId = sb->lastConnId++%2; auto lastConRb = sb->lastConRb;

	if (apm.tgtSb) sb = apm.tgtSb; else if (apm.nextSb) {
		//static int cc = 0;	sb = mmd->sabas[(cc++)%mmd->sabas.size()];
		int id = 0; auto campos = sb->irr2mmd(SceneManager->getActiveCamera()->getAbsolutePosition());
		float dis = 10000;
		for (int i = 0; i < mmd->sabas.size(); i++) 
			if (mmd->sabas[i]->ndCtr->disTo(campos) < dis) 
			{ dis = mmd->sabas[i]->ndCtr->disTo(campos); id = i;  }
		sb = mmd->sabas[id];
	}
	
	bool connFix = false;
	auto& lcrb = lastConRb[lastConnId];
	glm::vec3 connFixOfs = { 0,0,1 };
	if (isConn) //连接
	{
		
		cnc++; 
		auto fn =  cnnToNode?cnnToNode
			:osb? ( connFix = true,sb->findNode(osb->ndAllParent->GetNameU()) )//combine to parent node
			:nullptr;
		if (fn && fn->rb0)
			lcrb = fn->rb0;
		else
		if (!lcrb) {
			
				lcrb = //lastConnId ==3?sb->ndFootR->rb0: lastConnId == 2?sb->ndFootL->rb0: 
				lastConnId == 0 ? sb->ndHandL->rb0 : sb->ndHandR->rb0;

		}
		if (arRoot->snPickMap.size() && arRoot->snPickMap[0].getSbNode()) lcrb = arRoot->snPickMap[0].getSbNode()->rb0;
		if (lcrb) { 
			if (connFix) {
				lcrb->connFixCount++;
				connFixOfs += vec3{ 0,lcrb->connFixCount * 0.0f,lcrb->connFixCount * 0.05f };
			}
			pm.camShoot = false; pm.isBall = false;
			pm.autoKill = false;  pm.tag = 'conn'; 
			size = CONN_RB_SIZE; pm.size = { size,size,size }; pm.mass = 100.f;

			pm.pos=glh::matTransformVec(lcrb->GetTransform(),connFix? connFixOfs:vec3{ 0,size,0 });
			pm.rtt = connFix ? glm::eulerAngles(glm::quat(lcrb->GetTransform())) : glm::eulerAngles(lcrb->getRotation());
			pm.preHitSb = sb;
			pm.vel = { 0,0,0 }; 

		}

	}     
	else if (flyConn) { 
		pm.preHitSb = sb; pm.phe = pheConn; pm.autoKill = false; pm.mass = 10.f;
	}//

 
	if (OPPAI_SAVER && ((cc%2  ||  1 ) || isMmd))
	{
		pm.phe = pheOppaiSaver; pm.atkFlag = (1 << ENdId::eoL) | (1 << ENdId::eoR);
	}
	if (isBand) {
		auto bandpos = glm::vec3((cc % Eqv->eqvBandCount - 8.f + 0.5f) * 6.f, 1, 0);
		pm.camShoot = false; pm.toMMD = 0; pm.spdLenMul = 0; pm.autoKill = false;
		pm.pos = bandpos;
		pm.rtt = float3(90, 0, 0)*core::DEGTORAD;
		if (lsb) {
			//s->Pmx->connectRb(s->ndHandR->rb0, lsb->ndHandL->rb0);
			//	s->Pmx->connectRb(s->ndFootR->rb0, lsb->ndFootL->rb0);
		}
	}
	pm.pmxrb.m_translateDimmer = osb?osb->Rb0()->Pm.m_translateDimmer: 1.33f;
#if MMD_ACTION_ANIM
	bool isActAnim = false;
	if (sb->Pmx->curVA  && (actAnim || sb->Pmx->curVA->afterT))
	{
		int actId = apm.pm1;
		if (osb && osb->Pmx->pmx.realPath.find("lxz") != std::string::npos) pm.actGroup=1;
		actAnimPm(sb, actAnim, actId, pm);
		isActAnim = true;
	}
#endif
	if (isOverridePos) {
		pm.pos = overridePos;
		pm.camShoot = false;
	}
	
	pm.tgtSb = TGT_RND_SB?rndsb: curAiChar();
	auto obj = pm.tgtSb->Pom->addObj(pm);  //++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++
	if (!obj) return  ;

	if (Ctx->gd.apm.addFluid) {
		float r = Ctx->gd.apm.addFluid;
		addFluidParticlesGrid(r,r,r, .5, .5, .5, obj->rb->getPosition(), obj->rb->getLinearVel(), cam->getRotation() * core::DEGTORAD);
	}



	auto rb=obj->rb;
	if (isActAnim)obj->isflyingToSb = true, obj->flyingToFlag=0, obj->flyingToSbNode = nullptr;
 
	if (isConn && lastConRb[lastConnId]) {
		if (osb && osb->ndHandL) {
			bool con = true;
			if (auto lnd = lastConRb[lastConnId]->node) {
				auto lsb = lnd->model->saba;
				if (lnd->model->saba->isAiCharacter()) {
					if (lastConnId == 0) sb0->Pmx->connectRb(lastConnId == 0 ? lsb->ndHandR->rb0 : lsb->ndHandL->rb0, osb->ndHandL->rb0, true, { size * sc,-size * sc  ,0 }, false);
					else sb0->Pmx->connectRb(lastConnId == 0 ? lsb->ndHandR->rb0 : lsb->ndHandL->rb0, osb->ndHandR->rb0, true, { -size * sc,-size * sc  ,0 }, false);
				}
				else if (lsb->ndFootL) {
					sb0->Pmx->connectRb(lsb->ndFootL->rb0, osb->ndArm1L->rb0, true, { 0,-size * sc  ,3 }, false);
					sb0->Pmx->connectRb(lsb->ndFootR->rb0, osb->ndArm1R->rb0, true, { 0,-size * sc  ,3 }, false);
				}
				else con = false;
				if (osb) {
					auto nd = osb->findNode(L"W_0_1");
					if (nd) nd->SetScale(vec3(3.f));
				}
			}
			if (!con)
			{
				sb0->Pmx->connectRb(lastConRb[lastConnId], osb->ndHandL->rb0, true, { 0,-size * sc  ,0 }, false);
				sb0->Pmx->connectRb(lastConRb[lastConnId], osb->ndHandR->rb0, true, { 0,-size * sc  ,0 }, false);
				//sb0->Pmx->connectRb(lastConRb[lastConnId], osb->ndFootL->rb0, true, { 0,-size * sc  ,0 }, false);
				//sb0->Pmx->connectRb(lastConRb[lastConnId], osb->ndFootR->rb0, true, { 0,-size * sc  ,0 }, false);
			}
		}
		else { 
			
			saba::PMXJoint jp{}; jp.translate = { 0,connFix ? 0 : -size * sc * (osb?0:2),0 };	jp.setLocalPos = true;			
			if (connFix) {
				int cid = lastConRb[lastConnId]->connFixCount-1;
				jp.m_type = PMXJoint::JointType::Fixed;				
				jp.translate = connFixOfs; 
				if (cid>0) obj->rb->node->SetScale(vec3(pow(1.017f, lastConRb[lastConnId]->connFixCount)));
				osb->Pmx->attachParentNode = lastConRb[lastConnId]->node;
			} 
			obj->jtB2A = sb0->Pmx->connectRb(lastConRb[lastConnId], rb, 1,   connFix,jp,0);
			lastConRb[lastConnId]->disableAtk = true;
			rb->setCollideFilterMask(1, 0);
			 if (osb) if (auto tail = osb->findNode(L"[tailLock]")) {  //hang				
				tail->rb0->SetActivation(false, true); 
				//tail->rb0->moveRbOfs(vec3(0, 20, 0));
				tail->rb0->SetCoMTransform(curChar()->ndCtr->mGlobalAnim* glm::translate(mat4(1), vec3(0, 30, 0)));
				tail->rb0->setCollideFilterMask(1, 0);
			}
			if (lastConRb[lastConnId]->node) lastConRb[lastConnId]->node->model->attachedWing = true;
			//rb->node->SetAnimationTranslate({});
			//osb->Pmx->UpdateNodeAnimation(false); 
			//osb->Pmx->resetRigidBodies(1);
 
		}
		//lastConRb[lastConnId] = rb;
	}
	
	Driver->dsd.forceUpdateCD = 1;
	if (!osb)
	  Ctx->setSharedRbCb(rb);
	lsb = osb;
	obj->id = cc;
	cc++;

	if (mmd->MPA)
	{
		mmd->MPA->setPPNdB(0, obj->uid);
		//Sb->startSbMPA("data/mpa/jumpTo.json");
	}

	if (FRAME_BOX)
	{
		PMXFileCreateParam cp(ITEM_PMX1);
		cp.modelScale = true;
		sc=0.15f,
		cp.modelScaleVec3 = float3{ sc,sc,sc };
		cp.massMul = 0.01f;		
		auto sb = loadSabaModel(cp, false, 0x101)->sb;

		{
			auto ci = pm.pmxrb; ci.m_translate = obj->rb->getPosition()+ glm::vec3(.0,-3.0f,-.0);
			sb->ndRoot->SetAnimationTranslate(ci.m_translate);
			sb->Pmx->moveAllOffset(ci.m_translate);
			sb->Pmx->setBodyVel(obj->rb->getLinearVel());
		 	if (!lockIt && camOnBox) sendKeyEvent(KEY_F3, 1, 0, 0, 1);
		}
	}
#if 0
	if (linkSbs) {
		PMXFileCreateParam cp("D:/MMD/ITEMS/petal/petal.pmx");		cp.modelScale = true;
		for (int iy = 0; iy < apm.lsYC; iy++) {
			int N = apm.lsXC -iy;
			for (int i = 0; i < N; i++) {
				sc = 1.0f - iy * (0.6/ apm.lsYC);
				cp.modelScaleVec3 = float3{ sc,sc,sc };
				cp.massMul = 0.1f;
				auto sb = loadSabaModel(cp, false, 0x0)->sb;
				PMXJoint jt{};
				vec3 rtt = vec3(iy * piFloat / 2 / apm.lsYC, i * 2 * piFloat / N, 0);
				//jt.rotate = rtt;
				sb->ndRbRoot->SetGlobalTransform(obj->rb->GetTransform()* glm::mat4_cast(glm::quat(rtt)));
				sb->Pmx->resetRigidBodies(1);
				jt.translate = obj->rb->getPosition();// glm::quat(jt.rotate)* vec3(sb->Rb0()->getOfsMat()[3]);
				//jt.t2B = -glm::quat(jt.rotate) * vec3(sb->Rb0()->getOfsMat()[3]);
				jt.limitMinT = vec3(-1.1f);
				jt.limitMaxT = vec3(1.1f);
				jt.limitMinR = vec3(-1);
				jt.limitMaxR = vec3(1);
				jt.setLocalPos = false;
				jt.springT = vec3(10000.f);
				jt.springR = vec3(10000.f);
				jt.dampingT = vec3(100.f);
				jt.dampingR = vec3(100.f);
				//for only one node model or need updateNodeAnimation
				auto j = sb0->Pmx->connectRb(obj->rb, sb->Rb0(),  false, false, jt);
				if (j) 			j->setBreakThreshold(sb->Rb0()->getMass() * 10.1f);				
				sb->Rb0()->setLinearVel(obj->rb->getLinearVel());
				obj->subSns.push_back(sb);
			}
		}
	}
#endif
	if (pm.snIm) {
		auto& mtr = pm.snIm->getMaterial(0);
		switch (pm.poType)
		{
		case 1: {
			mtr.DiffuseColor = 0xFFDAA520;// 0xFFFFC8C0;
			mtr.SpecularColor = 0x08202020;
			//mtr.AmbientColor = 0x00FFE8E8;
		}break;
		case 32:
		{
			mtr.SpecularColor = 0x10808080;
			mtr.DiffuseColor = 0xFFDAA520;
		}
		break;
		}
	}
	obj->onHitBySb = [=]() {
#if MMD_ACTION_ANIM
		if (obj->mmdHitCount == 0) {
			//FrameWaiter fw; fw.waitNframeAndRun(10, [=](FWTask& t) {
			//	sb0->setAdAnim(UaRand(sb0->Pmx->vmdAddAnims.size()), 1);
			//	addMmdObj(0); });
		}
#endif
		};
	//obj->killY = 6;
#if JUMP_ON_GROUND
	if (obj->sb && obj->sb->ndFootL) {
		obj->killY = 9;
		obj->onKillY = [=]() {
			if (obj->grabbed) return;
			auto sb = obj->sb;
			auto pos = sb0->getRb0()->getPosition();//sb->irr2mmd(cam->getTarget());
			auto v = glh::calcVelocityP2PinT(obj->rb->getPosition(), pos + float3(0,0, 0), 0.6f);
			bool prepare = obj->vel.y<0 && obj->pos.y> obj->killY / 2;      
			 sb->Pmx->scaleBodyVel(prepare?0.9f:0.8f, 1);
			if (obj->vel.y < 0) {
				sb->ndFootL->rb0->scaleVel(0.75f);
				sb->ndFootR->rb0->scaleVel(0.75f);
			}
			sb->actVoiceFx(sb->Pm.mmd->mdplr.mmdCount+sb->objId%8, 5, 60, L"niau");
			float vmul = std::clamp(
				//3 + obj->vel.y*0.3f
				prepare ? -std::clamp(pow(9.f / obj->pos.y, 2.000175f) * 0.01f, 0.01f, 1.f) : std::min(1.f, obj->pos.y / 9.f)
				, 0.f, 0.5f) ;
			if (vmul > 0.0f) {
				sb->Pmx->addBodyVel(glh::vecLimitFast(v, 0, 100) * vmul *1.0f, true);
			}
			obj->rb->setAngVelToPos(pos, prepare?30.f:100.f);
			};
	}
		if (!obj->sb)
	{
		rb->usrDat.callHitCb = true; rb->hitCb = [&](saba::PhysicsEngineObjectUserData* hit) {
			static int cc = 0;
			float implen = glm::length(hit->hitImp);
			if (implen < 0.1f) return;
			DP(("imp2 %f", implen));
			Ctx->Midi.playNote(Driver->dsd.time, 10 + (cc++ % 6), core::s32_clamp(66 + UaRand(20), 0, 100),
				glm::clamp(implen * 2.f, 0.1f, 1.f), 0
			);
			};

	} 
#endif

}



bool AppMainAMP::StageOnJoyStickEvent(const irr::SEvent::SJoystickEvent& je)
{

	auto anm = Ctx->getCamTCAnimator();
	//anm->acceptJoystick = false;

	{
		//DP(("je %5d,%5d  %5d,%5d  %5d,%5d",je.Axis[0], je.Axis[1], je.Axis[2], je.Axis[3], je.Axis[4], je.Axis[5]));
		float jx, jy, jr, ju, jxf, jyf, jrf, juf;

		jx = abs(je.Axis[0]); jy = abs(je.Axis[1]); jr = abs(je.Axis[2]); ju = abs(je.Axis[4]);
		jxf = je.Axis[0] ? abs(je.Axis[0]) / je.Axis[0] : 0;
		jyf = je.Axis[1] ? abs(je.Axis[1]) / je.Axis[1] : 0;
		jrf = je.Axis[2] ? abs(je.Axis[2]) / je.Axis[2] : 0;
		juf = je.Axis[4] ? abs(je.Axis[4]) / je.Axis[4] : 0;

		if (je.lastPOV != je.POV)
		{
			switch (je.POV)
			{
			case 0:case 18000:
			{
				int inc = je.POV == 0 ? 1 : -1;
				if (Plates.size()) {
					Plates[0]->nbp.angleN = std::clamp(Plates[0]->nbp.angleN + inc, 1, 20);// UaRand(7)+1;//
					Plates[0]->relocateCubes();
				}
			}break;
			case 9000: case 27000:
			{
				int inc = je.POV == 9000 ? 1 : -1;

			}break;
			}
		}

		for (u32 i = 0; i < 32; i++)
		{
			
			u32 s = je.ButtonStates >> i & 1, ls = je.LastButtonStates >> i & 1;
			if (s != ls) {
				if (s) {
					DP(("joy DOWN %d", i));
					if (i == 12) {
						//sendKeyEvent(KEY_KEY_E, true);
					}
					if (Plates.size()) {
						if (i == 7)  Plates[0]->nbp.footButt ^= 1;
						if (i == 8)  Plates[0]->nbp.headButt ^= 1;
					}
					if (i == 16) {

					}
					//static int cc = 0; cc++; cc = cc % 2;
					//bool is1 = cc == 0;
					//auto sb = (is1 ?  it1->saba : it2->saba);
					//if (sb) {
					//	(is1 ? sb->ndHandL : sb->ndHandR)->rb0->setLinearVel(float3(is1 ? 1 : -1, 0, 1) * 1000.f);
					//}
				}
			}
		}
		float mspeed = 0.3f;
		if (abs(jx) > 2600)
		{
			if (Plates.size()) {
				auto tgt = Plates[0]->getPosition() + vector3df((jxf * pow(jx, 1.f) / 32768.f) * mspeed, 0, 0);
				tgt.x = std::clamp(tgt.x, -6.f, 6.f);
				Plates[0]->setPosition(tgt);
			}
		}
		if (abs(jy) > 3200)
		{
			if (Plates.size()) {
				auto tgt = Plates[0]->getPosition() + vector3df(0, 0, (-jyf * pow(jy, 1.f) / 32768.f) * mspeed);
				tgt.z = std::clamp(tgt.z, -6.f, 9.f);
				if (Plates.size()) Plates[0]->setPosition(tgt);
			}
		}
		if (abs(jr) > 3200)
		{
			if (Plates.size()) {
				auto tgt = Plates[0]->getPosition() + vector3df((jrf * pow(jr, 1.f) / 32768.f) * mspeed, 0, 0);
				tgt.x = std::clamp(tgt.x, -6.f, 6.f);
				Plates[0]->setPosition(tgt);
			}
		}
		if (abs(ju) > 3200)
		{
			if (Plates.size()) {
				auto tgt = Plates[0]->getPosition() + vector3df(0, (-juf * pow(ju, 1.f) / 32768.f) * mspeed, 0);
				tgt.y = std::clamp(tgt.y, -16.f, 3.f);
				if (Plates.size()) Plates[0]->setPosition(tgt);
			}
		}
		//if (Plates.size()) {
		//	auto p0 = Plates[0]->getPosition(); p0.y = std::max(0-sb0->ndCenter->mGlobalInit[3].y, p0.y/3);
		//	p0.x =  std::clamp(p0.x/3, -1.f, 1.f);
		//	p0.z =  std::clamp(p0.z/3, -1.f, 1.f);
		//	sb0->ndCenter->SetAnimationTranslate(p0  );
		//}

		if (!JOYSTICK_CAMERA) {
			mspeed = 1.f;
			mmd->curCtrlSb([=](irr::scene::IrrSaba* sb) {
				auto p = sb->Pmx;
				vec3 ofs = vec3(0, 0, 0);
				if (abs(jx) > 2600)
				{
					ofs.x += (jxf * pow(jx, 1.f) / 32768.f) * mspeed;
				}
				if (abs(jy) > 3200)
				{
					ofs.z += (-jyf * pow(jy, 1.f) / 32768.f) * mspeed;
				}
				if (abs(jr) > 3200)
				{
					
				}
				if (abs(ju) > 3200)
				{
					ofs.y += (-juf * ju/ 32768.f) * mspeed;
				}
				core::vector3df vec(ofs);
				 
				 sb->ndRoot->SetAnimationTranslate(sb->ndRoot->GetAnimationTranslate() + glm::vec3(vec * sb->ndRoot->GetScale().x  ));
				 
				});

	 
		}
	}

#if HAS_MMD
	if (!JOYSTICK_CAMERA) {
		for (auto& sb : mmd->sabas) {
			sb->onJoyEvent(je);
		}
	}
 
	
#endif

	return false;
}



void AppMainAMP::StageAfterPresent()
{
	if (drawDepth || vp.writeFrameToFile /*|| mmd->fluidNode*/)
	{
		if (vp.depthRT && vp.depthRT->getSize() !=  Ctx->mainViewSize())
		{
			Driver->removeTexture(vp.depthRT); vp.depthRT = nullptr;
		}
		if (!vp.depthRT) vp.depthRT = Driver->addRenderTargetTexture( Ctx->mainViewSize(), "deprt", Driver->getSysRT() ? Driver->getSysRT()->getColorFormat() : Driver->getColorFormat());
		auto cam = SceneManager->getActiveCamera();
		Driver->setRenderTarget(vp.depthRT, 1, 1, 0);
		video::SMaterial mr;
		mr.MaterialType = DRV_GET_MT(Driver, Fx2DIdEnum::Fx2D_ProcessImage);
		video::VkMr2D* mr2d = (video::VkMr2D*)Driver->getMaterialRenderer(mr.MaterialType);
		mr2d->cbDraw.processImage.mode = //mmd->fluidNode?-1:
			mmd->outNormalMap == 2 ? CbMr2D::epi_depth2normalMap :
			CbMr2D::epi_depth2color;
		mr2d->cbDraw.processImage.fv.x = cam->getNearValue();
		mr2d->cbDraw.processImage.fv.y = //mmd->fluidNode?cam->getFarValue():
			camDepFar;// 
		mr2d->cbDraw.processImage.fv.z = depthOutMul;
		mr2d->cbDraw.processImage.flag = DEPTH_WHITE_FAR;
		DP(("n=%f f=%f mul=%f", mr2d->cbDraw.processImage.fv.x, mr2d->cbDraw.processImage.fv.y, mr2d->cbDraw.processImage.fv.z));
		mr.setTexture(0, VkDrv->DepthProxyTexture);
		auto rc = core::recti(0, 0,  Ctx->mainViewSize().Width,  Ctx->mainViewSize().Height);
		Driver->draw2DImageMr(mr, rc, rc);
		if (depthRenderFw)
			IFppt->render();
		Driver->setRenderTarget(nullptr, 0, 0, 0);
		//if (mmd->fluidNode) 
		//	mmd->fluidNode->setDepthTexture(vp.depthRT);
		//Driver->saveTexture(vp.depthRT, "out/depth.png");
	}

	//only for video enc, dyn img see capture handler
	if (vp.renderId % vp.writeFrameToFileEveryNFrames == 0) {
		if (vp.recording && vp.writeFrameToFile) {

			Driver->saveTexture(vp.depthRT, ualib::strFmt("r:/imgdp/%05d.png", vp.renderId / vp.writeFrameToFileEveryNFrames).c_str());
			if (vp.texPD && MMD_WRITE_MASK)	 Driver->saveTexture(vp.texPD, ualib::strFmt("r:/imgmk/%05d.png", vp.renderId / vp.writeFrameToFileEveryNFrames).c_str());
		}
		if (vp.writeFrameTxt && arRoot->curArSn && curChar()) {
			using namespace std;
			ofstream ofs(ualib::strFmt("r:/imgtxt/%05d.txt", vp.renderId / vp.writeFrameToFileEveryNFrames).c_str());
			ofs.write(curChar()->writeFrameTxtStr.c_str(), curChar()->writeFrameTxtStr.size());
			ofs.close();
		}
	}


#if SABA_PHYSICS_ASYNC
	bool animating = !Driver->dsd.paused();
	if (animating) {
		//CPU_COUNT_B(PhyMeshData);
		MMDPhysics::UpdateFrameWait();
		//CPU_COUNT_E(PhyMeshData);
		sb0->Pom->freeSceneNodes();
	}
#endif
}

void AppNameSpace::AppMainAMP::StageFrameEnd()
{

}

 


irr::scene::SnArItem* AppMainAMP::loadSabaModel(PMXFileCreateParam  fcp, bool reset, uint32_t aiFlag)
{
	static int cc = 0;
	bool isPhyUpdate = aiFlag & 0x01;
	bool isCharacter = aiFlag & 0x10;
	bool isAllPhy = aiFlag & 0x1000;  //allPhysics
	if (isCharacter) fcp.addFingerRbs = MMD_ADD_FINGER_RBS;
	auto curSn = curArSn();
	assert(!curSn ||curSn->AIFlag != 0xdddddddd);
	auto cp = fcp;
	//cp.massMul = cc==0?0.1f:1.f;
#if SVG_MMD_WRITE
	mmd->Pm.spm.handWrite = isCharacter && (!it0 || !SVG_MMD_ONLY_FIRST_WRITE);
#endif
#if MMD_ZERO_FRICTION
	if (mmd->sabas.size()<1 || !MMD_LOOKAT_SB0)if (isCharacter || isAllPhy) {
		cp.fixFriction = true; cp.setFixFriction = 0.1f;		cp.frictionMul = 1;
		
	}
	if (MMD_LOOKAT_SB0 && mmd->sabas.size()>=1) cp.massMul = 0.1f;;
#endif
	if (reset) {
		cc = 0;//if (it0) it0->remove(); it0 = 0; midiSabas.clear();
	}
	//arRoot->Cs.pickPhysics = 1; 
	arRoot->Cs.gts.scPointCount = 1;

	if (cp.filePath.empty()) cp.filePath = std::string(pmxfiles[cc++ % (sizeof(pmxfiles) / sizeof(char*))]);

	if (AI_PH_NAME.size() > 0) {
		cp.phlabPath = (AI_PH_NAME.pathWithOutExt() + L".lab").c_str();
	}

	arRoot->loadMMD(cp,aiFlag);


	auto captureEvt = [=](IrrSaba* sb, int evt) {
		auto Pmx = sb->Pmx;	sb->animCycleStart = -1;
		Pmx->clearVtxPickNodes();
		//Pmx->GetPhysicsManager()->GetMMDPhysics()->enablePlane(0);
		sb->setAllDynRbActive(true);
		//Pmx->resetPhyCD = 1;
		if (sb==sb0)
		Pmx->connectRb(sb->ndHandL->rb0, sb1->ndHandR->rb0);
		else if (sb == it2->sb)
		Pmx->connectRb(sb->ndHandR->rb0, sb1->ndHandL->rb0);
		else Pmx->connectRb(sb->ndFootL->rb0, sb1->ndUpper2->rb0);
		//Pmx->connectRb(sb->ndFootL->rb0, it1->saba->ndHead->rb0);
		//Pmx->connectRb(sb->ndFootR->rb0, it1->saba->ndHead->rb0);
		//Pmx->connectRb(sb->ndUpper2->rb0, it1->saba->ndHead->rb0);


	};
	isCharacter = isCharacter && arRoot->curArSn->sb->Pmx->isCharacter;
	if (isCharacter) 
	{
		
		
		if (!it0) {
			it0 = arRoot->curArSn; mmd->sb0 = lastCntSb = sb0 = mmd->sb0 = it0->sb; sb0->setItemIdx(0); arRoot->it0 = it0;
			Pom = sb0->Pom; mmd->Pom = Pom;
			float size =  CAM_PHY_SIZE;
			sb0->onArPath = OnArPath[0]; 	sb0->xrMode = xrModes[0];
			PhyObjParam pm{  1,CAM_RB_MASS,{size,size,size },{ 0,0,0 },{},{ 0,0,0 },false,false,false };
			pm.operation = CAM_DYN_RB ?1:0;  pm.parentKimSn = CAM_DYN_RB ? 0 : Ctx->gd.CamRtt; pm.autoKill = false;
			//pm.hitSbTest = false;
			pm.pmxrb.m_repulsion = 0.6f;  pm.pmxrb.m_rotateDimmer=10.f;
			pm.pmxrb.m_friction = 0.0f;
			pm.pmxrb.m_collideMask32 = CAM_PHY_COLLISION; pm.tag = 'cam'; pm.pmxrb.noGravity = true;
			camPhyObj = sb0->Pom->addObj(pm);
			camRb = camPhyObj->rb; 
			if (CAM_MMD_SELFIE)
			{
				PhyObjParam pm{ 1,10.f,{2,1,1 },{ 0,0,0 },{},{ 0,0,0 },false,false,false }; pm.autoKill = false;
				auto rbc = mmd->camRbFpvDmy= sb0->Pom->addObj(pm)->rb; pm.operation = 0;
				mmd->camRbFpvDmyJt=sb0->Pmx->connectRb(sb0->ndUpper2->rb0, rbc, true, glh::matTransformVec(sb0->ndUpper2->rb0->getOfsMatInv(), {0,0.7,-2.6}), false);
				rbc->SetActivation(true);
			}
#ifdef EYE_BOT
			auto camSb = loadPmxItem(
				EYE_BOT,
				{ 0,0,-6 }, { 0,180,0 });
			saba::PMXJoint jt{}; jt.translate = pm.pos;
			jt.setLocalPos = true;
			jt.limitMinT = float3(-100); jt.limitMaxT = float3(100);
			jt.springT = float3(1);
			jt.springR = float3(1);
			jt.dampingT = float3(10);
			//camSb->Pmx->connectRb(camRb , camSb->getRb0(), false, false, jt);
#endif
#if CAM_BACK_BOX_RB
			pm.poType = 0;
			pm.size=float3(10,10,10);
			pm.sn = SceneManager->addCubeSceneNode(pm.size.x * MMD_SABA_SCALE, pm.sn, -1, {0,0,-(pm.size.x + 6 )*MMD_SABA_SCALE/2 });
			pm.sn->setMaterialTexture(0, Driver->getTexture(L"res/pt1.png"));
			sb0->Pom->addObj(pm);
#endif
			
			
			sb0->rcvEqvData = 1;
			sb0->cbVmdEvent = [=](const SVmdEvent& e) {
				

				if (BEAT_TO_JSON) return;
				if (e.eventType == SVmdEvent::eBrickWall) {
					
					if (CCubeGridSceneNode::Walls.size() < CCubeGridSceneNode::maxCbWall) {
						BrickWallParams bwp{};
						bwp.pos = glm::vec3(0, 0, 10);
						bwp.rtt = glm::vec3(0);
						bwp.grid = glm::ivec3(16, 16, 1);
						bwp.brickSize = glm::vec3(1.f, 1, 0.2f);
						bwp.brickSpace = glm::vec3(0);
						bwp.density = 100.f;
						bwp.restitution = 0.5f;
						bwp.friction = 0.5f;
						bwp.color = glm::vec4(1.f);
						createCubes(bwp);
					}
					else
					{
						CCubeGridSceneNode::curCube()->resetCubes(0, &e.brickWallParams);
						//testTextFw(true, ualib::wstrFmt(L"%.0f", sb0->getCurTime()*30), 0x10);
					}
					CCubeGridSceneNode::curCbWall = (CCubeGridSceneNode::curCbWall + 1) % CCubeGridSceneNode::maxCbWall;
				}
				};
			sb0->onSvNoteCb = [=](int ch, SvNoteData& note) {
				DP(("onSvNoteCb %d %d  my %d", ch, note.key, Ctx->gd.mouseY / 10));
				sb0->ndHandR->rb0->addTorque(vec3(note.key - 36, 0, 0) * (10.f));
				sb0->ndHandR->rb0->addTorque(vec3(note.key - 36, 0, 0) * (10.f));
				//sd->s1->findNode(L"stick")->rb0->addLinearVel(vec3(0, note.key - 36, 0) * (Ctx->gd.mouseY));
				};
#if SVG_MMD_RB_HAS_SUBMMD
			sb0->cbOnAddStkRb = [=](irr::scene::PhyObj* o) { auto rb = o->rb;
				saba::PMXFileCreateParam fcp{ "D:/MMD/PMX/JQL/dog/watchN.pmx" }; fcp.massMul = 10.f;

#define SUBSNS_ATTACH	1
#define VTXFW_ON_SUBSNS (SUBSNS_ATTACH && 0)
#if VTXFW_ON_SUBSNS
				auto sbw = loadSabaModelScale(fcp, rb->Pm.m_shapeSize.x, false,0)->sb;
				sb0->Pmx->connectRbMoveB(rb,rb->getPosition(), sbw->findNode(L"root")->rb0, true,{0, - sbw->findNode(L"watch")->mGlobaY/linit[3].y + sbw->findNode(L"root")->mGlobaY/linit[3].y,0}, true);
#else
				auto sbw = loadSabaModelScale(fcp, 1.7f, false, 0)->sb;
				sb0->Pmx->connectRbMoveB(rb, rb->getPosition(), sbw->findNode(L"root")->rb0, true, { 0,0,0 }, true);

#endif
					 //connectRb(rb, sbw->findNode(L"root")->rb0, true, { 0,0,0 }, true, {});
				if (SUBSNS_ATTACH) o->subSns.push_back(sbw);
				else tmpSns.push_back(sbw);
				 auto rbHit= sbw->findNode(L"watch")->rb0;
				 if (VTXFW_ON_SUBSNS) o->vtxFwRb = rbHit;
				 rbHit->usrDat.callHitCb = true; rbHit->hitCb = [&](saba::PhysicsEngineObjectUserData* hit) {
					 static int cc = 0;
					 float implen = glm::length(hit->hitImp);
					 if (implen < 0.1f) return;
					 DP(("imp2 %f", implen));
					 Ctx->Midi.playNote(Driver->dsd.time, 11 + (cc++ % 4), core::s32_clamp(66 + UaRand(20), 0, 127),
						 glm::clamp(implen * 2.f, 0.1f, 1.f), 0
					 );
					 };
				};
#endif
			//auto sb = loadSabaModel("d:/mmd/items/walls/wall1.pmx", false, 0x10);
			if (HAS_PHY_WALL) {
				PhyObjParam pm{ 2,0,{12,200,10 },	{ -27,0,0 },{},{ 0,0,0 },false,false,false };
				//auto rb = it0->saba->addObj(pm);
				PhyObjParam pmb{ 0,0,{200,1000,10 },	{ 0,0,60 },{},{ 0,0,0 },false,false,false };
				PhyObjParam pmc{ 0,0,{10,1000,200 },	{ -60,0,0 },{},{ 0,0,0 },false,false,false };
				PhyObjParam pmt{ 0,0,{1000,100,1000 },	{ 00,250,0 },{},{ 0,0,0 },false,false,false };
				pm.pmxrb.m_friction = 0; 
				pmb.pmxrb = pm.pmxrb;pmc.pmxrb = pm.pmxrb; pmt.pmxrb = pm.pmxrb;
				pmb.operation = 0; pmb.pos.z = 110; wallRb[0] = sb0->Pom->addObj(pmb)->rb; pmb.pos.z = -110; wallRb[1]=sb0->Pom->addObj(pmb)->rb;
				pmc.operation = 0; pmc.pos.x = 110; wallRb[2] = sb0->Pom->addObj(pmc)->rb; pmc.pos.x = -110; wallRb[3]=sb0->Pom->addObj(pmc)->rb;
				sb0->Pom->addObj(pmt);
			}

			if (MMD_HAS_WHEEL) {

				loadSabaModel(PMXFileCreateParam("D:/MMD/ITEMS/wheel/scene.pmx"), false, 0x01);
			}
			 if (MMD_HAS_MIC) { 
				 
#if 1
				PMXFileCreateParam fcp1("d:/mmd/Items/mic/1rb.pmx"); fcp1.collisionId = sb0->modelCollisionId;
				mic = loadSabaModel(fcp1, false, 0x101)->sb;
				mic->setParent(sb0);mic->attachToParentNode(L"左ダミー");  mic->setRotation({ 180, 0, 0 }); mic->setPosition(0.3, -0.3, 0);
#else	
				mic = loadSabaModel(PMXFileCreateParam("d:/mmd/Items/mic/1rb.pmx"), false, 0x101)->saba;
				auto dmy = it0->saba->ndHandL;// dmy->SetAnimationRotate(glm::quat(glm::vec3(0, 90, 0))); //dmy->EnableDeformAfterPhysics(1);
				mic->Pmx->connectRb(it0->saba->ndHandL->rb0, mic->Pmx->getRb(0), {0,3,0}, true); //dmy->SetAnimationRotate(glm::quat(glm::vec3(180, 0, 0)));
				//mic->setAllRbActive(true);
#endif 
				sb0->mic = mic; 
				arRoot->setCurSn(it0);

			}
			else if (MMD_HAS_CLOTH)
			{
				mmd->Pm.spm.clsType = ESabaClass::Cloth;
				cloth =static_cast<SabaCloth*>(loadSabaModel(PMXFileCreateParam("d:/mmd/PMX/cloth.pmx"), false, 0x01)->sb);
				mmd->Pm.spm.clsType = ESabaClass::SabaBase;
				cloth->setItemIdx(0);
#ifdef CLOTH_VMD
				cloth->loadAnimation(CLOTH_VMD);
#endif
				cloth->writer = sb0;				
				cloth->writer->sbCloth = cloth;
				//it0->saba->Pmx->rt1Tr = float3(-3,0,7);
			}

			io::path sceneFile = reloadScene().c_str();
#ifdef _WIN32
			if (!fcdScene) { 
				fcdScene = new FileChangeDetectorEx(sceneFile.c_str());
				fcdScene->mCallBack = [=](irr::io::path file) {
					if (file != sceneFile.pathGetFileName())
						return;
					FRAMEWAITER_CALL_B(1) {
					reloadScene();
					arRoot->setCurSn(it0);
					//sendKeyEvent(KEY_KEY_P, 1, 0, 0, 0); 
					//}).waitNframeAndRun(6, [=](FWTask& task) {
					//	sendKeyEvent(KEY_KEY_P, 1, 0, 0, 0);
						}); 
					};
			}
#endif

#if USE_DML_AI
			Ai = new SnTestAI(sb0, sb0,SceneManager,-1);
#endif
#if MMD_JOYSTICK_GAMECAST
				const char* path = "d:/mmd/PMX/ganyu/v2/joystick1x.pmx"; 
				auto sb = loadSabaModel(PMXFileCreateParam(path),  false, 0x101)->sb;
				it0->sb->hdlJgc->setJoyStick(sb);				
#endif
 
#if HAS_PIANO
				PianoParam pianopm;
				pianopm.ppt = IFppt;
				pianopm.eqv = Eqv;
				pianopm.ctx = Ctx;
				pianopm.cbOnKeyDown = [this](int id, float x, float y, float h) {
					//if (snWater) snWater->addWaveSource(x, y, h);
					};
				Piano = new SnPiano(SceneManager->getRootSceneNode(), SceneManager, -1, pianopm);
				Piano->drop();
				//Piano->setScale(0.1);
				Piano->onReset();

#endif

		}
		else if (!it1) {
			it1 = arRoot->curArSn; sb1 = it1->sb;  sb1->setItemIdx(1);
			sb1->onArPath = OnArPath[1];	sb1->xrMode = xrModes[1];
			
			if (cloth && SVG_MMD_WRITE || MMD_JOYSTICK_GAMECAST) {
				cloth->clothOwnerSb = sb1;
				cloth->setParent(sb1);
				cloth->setScale(1);
				sb1->ndLookAt = sb0->ndHandR; sb1->ndLookAtMul = 6.f;
			}

			//it1->saba->Pmx->connectRb(it1->saba->ndCtr->rb0, it0->saba->ndArm1L->rb0,{0,10,0},true);
			//it1->saba->Pmx->rootSc = { 5,5,5};
		}
		else if (!it2) {
			it2 = arRoot->curArSn; sb2 = it2->sb;  sb2->setItemIdx(2); 
			it2->sb->onArPath = OnArPath[2]; it2->sb->xrMode = xrModes[2];
			if (cloth && SVG_MMD_WRITE|| MMD_JOYSTICK_GAMECAST) {
				cloth->clothOwnerS1 = it2->sb;
				
			}
 
			//it2->saba->onSabaEvent = captureEvt;
			//it2->saba->Pmx->connectRb(it2->saba->ndHandL->rb0, it1->saba->ndFootL->rb0);
			//it2->saba->Pmx->connectRb(it2->saba->ndHandR->rb0, it1->saba->ndFootR->rb0);
			//it2->saba->Pmx->rootSc = {3,3,3};
		}
		else {
			auto sb = curSaba(); 
			sb->setItemIdx(midiSabas.size());
			sb->onArPath = OnArPath[2]; sb->xrMode = xrModes[2];
			sb->onSabaEvent = captureEvt;
		}
		auto csb = curSaba();
		if (csb) {
#define CHAR_STAND_FORMATION  0
#if MMD_MIDI_MODE==10
			//if (midiSabas.size() && (SABA_ONE_WORLD))			
			//	arRoot->curArSn->saba->Pmx->connectRb(arRoot->curArSn->saba->ndHandL->rb0, midiSabas[midiSabas.size() - 1]->ndHandR->rb0);
			int i = midiSabas.size(), md = 3;
			//curSaba()->Pmx->rt1Tr = (float3{ 0 + ((i % md + 1) / 2 * 3.f) * ((i % md + 1) % 2 * 2 - 1) * 3, 0, i / md * 10.f });
			static float initps[32][6] = { {0,0,0,  0, 0,0} ,//  {6,0,0},{-6,0,0},   {6,0,6}, {0,0,6},{-6,0,6},{-6,0,6},{0,0,12},{-6,0,12},{-6,0,12},
			//	{-12,0,10},{12,0,10},{20,0,-6}, {-26,0,-6.7},{10,0,6},{-10,0,6},{0,0,12},{-8,0,12},{8,0,12},{0,0,16},{-8,0,16},{8,0,16},
			 //{-20,0,0}, { -10,0,0 }, { 0,0,0 }, { 10,0,0 }, { 20,0,0 },
			};
			auto sb = csb;
#if CHAR_STAND_FORMATION
			if (
				fcp.initPos != glm::vec3(0)

				)
			{
				csb->Pmx->rt1Tr = fcp.initPos;
				csb->Pmx->rt1Rt = fcp.initRtt;
				csb->Pmx->InitializeAnimation();
				csb->setAllDynRbActive(true);
				csb->Pmx->ResetPhysics();
				csb->localPhyAnim = true;
			}

			else for (int i = 0; i < mmd->sabas.size(); i++) 
			{
				sb = mmd->sabas[i];				
				if (MMD_ZERO_FRICTION) {
					switch (CHAR_STAND_FORMATION)
					{
					case 11: {
						   float R = 10; 	int N = mmd->sabas.size(); float a = 19 * DEGTORAD;
						   if (N > 1) {
							   initps[i][2] = -i * R * cos(a); initps[i][1] = -i * R * sin(a); initps[i][0] = 0; //pos
							   //initps[i][4] = 180;  //rotation
						   }
					}		break;
					default:
						initps[i][0] = i * 0;// 27.5;
						initps[i][1] = i * 0;
						break;
					}
					

				}
				else if (SEESAW)
				{
					float addY = 0;
					if (mmd->sabas.size() == 1) {
						saba::PMXFileCreateParam cp{ .filePath = "D:/MMD/ITEMS/Toy/qqbBaseNY.pmx", .massMul = 10.f };
						int N = 6;
						cp.duplicateAndRotatePass =1;
						cp.copyCount = N - 1;
						cp.copyTrsInc = vec3(0, addY,0);
						cp.copyRttInc = vec3(0,180.f/N,0);// vec3(0, 360 / (cp.copyCount + 1), 0);				
						cp.skipMaterialCopy = 1; 
						cp.skipFirstNbones = -1;
						itScene = loadSabaModelScale(cp, 1.f, false, 0x1101), itScene->sb->ndRoot->SetAnimationTranslate({ 0,0,0 });
				 
						//PMXWriter writer; writer.writeFile(&itScene->sb->Pmx->pmx, "R:/save.pmx");
				 
					}
					float R = 3; 	int N = mmd->sabas.size();
					float X = 0;
					
					float radius = 16.0f; // Spacing between characters
					float centerX = 0.0f;  // Center X position
					// Calculate position based on index
					if (i % 2 == 1) {
						int rightPos = (i) / 2 *0;
						X = centerX +radius - R * rightPos;
					}
					else {
						int leftPos = i / 2 *0;
						X = centerX - radius +R* leftPos;
					}
					auto brb = itScene->sb->findNode(ualib::wstrFmt(L"board.%d", i / 2))->rb0;
					auto qr = quat(brb->Pm.m_rotate );
					bool L = i % 2==0;
					if (i > 1) {
						vec3 pos = qr * vec3(X, 0, 0);
						vec3 rtt = glm::degrees(glm::eulerAngles(glm::normalize(qr * quat(glm::radians(vec3(0, (L) * 180 + 90, 0))))));
						initps[i][0] = pos.x; initps[i][1] = pos.y + i * addY; initps[i][2] = pos.z; // Position
						initps[i][3] = rtt.x; initps[i][4] = rtt.y; initps[i][5] = rtt.z; // Rotation
						if (i == mmd->sabas.size() - 1) {
							sendKeyEvent(KEY_KEY_O); sb->loadMotion("D:/mmd/vmd/buildwall.vmd");

							PMXJoint jt{};
							jt.limitMinT = vec3(-10);
							jt.limitMaxT = vec3(10);
							jt.setLocalPos = true;
							jt.springT = vec3(100.f);
							jt.springR = vec3(100.f);
							jt.dampingT = vec3(100.f);
							jt.dampingR = vec3(10.f);

							jt.translate = qr * vec3(X, addY, 0);

							sb->Pmx->connectRb(brb, sb->ndCtr->rb0, false, false, jt);
							jt.springT = vec3(1000.f); jt.dampingT = vec3(100.f);
							jt.translate = qr * vec3(X + (L ? 1 : -1) * 0, 1, !L ? 1 : -1); sb->Pmx->connectRb(brb, sb->ndHandR->rb0, false, false, jt, 1);
							jt.translate = qr * vec3(X + (L ? 1 : -1) * 0, 1, !L ? -1 : 1); sb->Pmx->connectRb(brb, sb->ndHandL->rb0, false, false, jt, 1);

						}
					}
					else {
 
						vec3 pos = qr * vec3(X, 0, L ?3 : -3);
						vec3 rt = glm::eulerAngles(glm::normalize(qr * quat(glm::radians(vec3(0, (L ? 0 : -180), 0)))));
						vec3 rtt = glm::degrees(rt);
						initps[i][0] = pos.x; initps[i][1] = pos.y; initps[i][2] = pos.z; // Position
						initps[i][3] = rtt.x; initps[i][4] = rtt.y; initps[i][5] = rtt.z; // Rotation
						if (i == mmd->sabas.size() - 1) {
							sendKeyEvent(KEY_KEY_O); sb->loadMotion("D:/mmd/vmd/buildwall.vmd");

							PMXJoint jt{};
							jt.limitMinT = vec3(-100);
							jt.limitMaxT = vec3(100);
							jt.setLocalPos = true;
							jt.springT = vec3(1.f);
							jt.springR = vec3(100000.f);
							jt.dampingT = vec3(1.f);
							jt.dampingR = vec3(10.f);

							jt.translate = qr * vec3(X, -4, L ? 3 : -3);
							jt.rotate = rt;  jt.rotate.x += piFloat * -0.1f;
							// sb->Pmx->connectRb(brb, sb->ndCtr->rb0, 0,0, jt);
							 jt.springT = vec3(3700.f); jt.dampingT = vec3(100.f);  
							 jt.springR = vec3(1000.f);
							 sb->ndArmL->setNodePhyAnimRat(0.01f, true, false); sb->ndArmR->setNodePhyAnimRat(0.01f, true, false);
							//jt.translate = qr * vec3(X + (L ? -1 : 1) * 1, 2, 0); sb->Pmx->connectRb(brb, sb->ndArm1R->rb0, false, false, jt, 1);
							//jt.translate = qr * vec3(X + (L ? 1 : -1) * 1, 2, 0); sb->Pmx->connectRb(brb, sb->ndArm1L->rb0, false, false, jt, 1);

						} 
					}

					//sb->setParent(itScene->sb);
					//sb->attachToParentNode(L"base");
				}
				else 
				switch (CHAR_STAND_FORMATION)
				{
				default: //1+circle
				{
					float R = 25; int N = mmd->sabas.size() - 1; // skip sb0
					if (N > 1) {
						float a = (i - 1) * 2 * piFloat / N;
						initps[i][0] = cos(a) * R; initps[i][1] = 0; initps[i][2] = sin(a) * R; //pos
						initps[i][3] = 0; initps[i][4] = glm::degrees(a) + 180; initps[i][5] = 0;  //rotation
					}
				}
					break;
				case 2: {//circle in
					int N = mmd->sabas.size();
					if (N > 1) {
						float R = 10 + N * 0.5;
						float a = (i) * 2 * piFloat / N;
						initps[i][0] = cos(a - piFloat / 2) * R; initps[i][1] = 0; initps[i][2] = sin(a - piFloat / 2) * R;//pos
						initps[i][3] = 0; initps[i][4] = glm::degrees(-a)+180; initps[i][5] = 0; //rotation
					}
				}
					  break;
				case 3:	{//circle out
						int N = mmd->sabas.size() ;
						if (N > 1) {
							float R = 10 + N * 0.5;
							float a = (i  ) * 2 * piFloat / N;
							initps[i][0] = cos(a- piFloat /2) * R; initps[i][1] = 0; initps[i][2] = sin(a- piFloat / 2) * R;//pos
							initps[i][3] = 0; initps[i][4] = glm::degrees(-a)  ; initps[i][5] = 0; //rotation
						}
					}
					break;
				case 10:  //line h
				{
					float R = 15; 	int N = mmd->sabas.size(); float startX = -(N - 1) / 2.f * R;
					if (N > 1) {
						initps[i][0] = startX + i * R; initps[i][1] = 0; initps[i][2] = 0; //pos
						//initps[i][4] = 180;  //rotation
					}
				}
				break;

				case 11:  //line h z
				{
					float R = 6; 	int N = mmd->sabas.size(); float startX = -(N - 1) / 2.f * R;
					if (N > 1) {
						initps[i][2] = startX + i * R; initps[i][1] = 0; initps[i][0] = 0; //pos
						//initps[i][4] = 180;  //rotation
					}
				}
				break;

				case 12: {
					int N = mmd->sabas.size();
					if (N < 2) break;
					const float spacing = 6.0f;  // Space between objects
					const float defaultY = 0.0f;  // Default height
					int gridCols = 3, gridRows = N/gridCols;
					// Calculate starting positions to center the grid
					float startX = -(gridCols - 1) * spacing / 2.0f;
					float startZ = -(gridRows - 1) * spacing / 2.0f;

					// Calculate current row and column
					int currentRow = i / gridCols;
					int currentCol = i % gridCols;

					// Calculate final positions
					initps[i][0] = startX + (currentCol * spacing);
					initps[i][1] = defaultY;
					initps[i][2] = startZ + (currentRow * spacing);
				}break;
				case 15:  //line v
				{
					float R = 3.6f; 	int N = mmd->sabas.size(); float startX = 0;
					if (N > 1) {
						initps[i][1] = startX + i * R; initps[i][0] = 0; initps[i][2] = 0; //pos

					}
				}
				break;
				case 20:  //pyramid
				{
					float spacing = 6;  // Space between characters
					float rowSpacing = 5;  // Space between rows
					int N = mmd->sabas.size();
					int rowCount = 1;
					int totalSpots = 0;
					while (totalSpots < N) {	totalSpots += rowCount;		rowCount++;			}
					rowCount--; 
					int currentSpot = i;
					int currentRow = 0;
					int spotsInCurrentRow = 1;
					while (currentSpot >= spotsInCurrentRow) {
						currentSpot -= spotsInCurrentRow;
						currentRow++;
						spotsInCurrentRow++;
					}
					float rowWidth = spacing * (spotsInCurrentRow - 1);
					float startX = -rowWidth / 2;
					initps[i][0] = startX + currentSpot * spacing;  // X position
					initps[i][1] = 0;                              // Y position (height)
					initps[i][2] = currentRow * rowSpacing;        // Z position (depth)
					initps[i][3] = 0; initps[i][4] = 0;  initps[i][5] = 0;  //rotation
				}
				break;
				case 21:  //pyramid
				{
					float spacing = 6;  // Space between characters
					float3 rowSpacing(0,6,0);  // Space between rows
					int N = mmd->sabas.size();
					// Calculate number of rows needed for pyramid
					int rowCount = 1;
					int totalSpots = 0;
					while (totalSpots < N) {
						totalSpots += rowCount;
						rowCount++;
					}
					rowCount--; // Adjust back if we went over
					// Find which row this character belongs in
					int currentSpot = i;
					int currentRow = 0;
					int spotsInCurrentRow = rowCount;  // Start with widest row
					while (currentSpot >= spotsInCurrentRow) {
						currentSpot -= spotsInCurrentRow;
						currentRow++;
						spotsInCurrentRow--;
					}
					float rowWidth = spacing * (spotsInCurrentRow - 1);
					float startX = -rowWidth / 2;
					// Set position
					initps[i][0] = startX + currentSpot * spacing;  // X position
					initps[i][1] = currentRow * rowSpacing.y + (i < BASE_CHAR_NUM? -BASE_CHAR_NUM:0);                              // Y position (height)
					initps[i][2] = currentRow * rowSpacing.z;       // Z position (depth), negative for forward direction
					initps[i][3] = 0; initps[i][4] = 0;  initps[i][5] = 0;  //rotation
				}
				break;
				}

#endif			
 
			//	for (int i = 0; i < 32; i++) initps[i][2] = i * 50;
				sb->Pmx->rt1Tr = (i == 90 ? vec3(0, 10, 0) : initpsBasePos)+glm::vec3(initps[i][0], initps[i][1], initps[i][2]) * 1.f;// +float3{ -2,0,-3 };
				sb->Pmx->rt1Rt = (i == 90 ? vec3(0, 0, 0) : initpsBaseRtt)+ glm::vec3(initps[i][3], initps[i][4], initps[i][5]) * DEGTORAD;
				
				 if (i == mmd->sabas.size() - 1) {
					sb->Pmx->InitializeAnimation();
					sb->setAllDynRbActive(true);
					sb->Pmx->moveAllOffset(sb->Pmx->rt1Tr);
					sb->Pmx->ResetPhysics();
				}
				//float a = core::PI / 2 + core::PI * 2 / 6 * midiSabas.size(), rlen = 10;
				//curSaba()->Pmx->rt1Tr = float3(cos(a) * rlen*.7f, 0, -sin(a) * rlen);
#if CHAR_STAND_FORMATION
			}
#endif
#else
		if (sabas.size() && (SABA_ONE_WORLD))
			arRoot->curArSn->saba->Pmx->connectRb(arRoot->curArSn->saba->ndHandL->rb0, sabas[sabas.size() - 1]->ndHandR->rb0);

		arRoot->curArSn->saba->Pmx->rt1Tr = float3(-6 - (midiSabas.size() * 11.f), 0, 1);
#endif
		}
		if (mmd->sabas.size() > MIDI_START_MMD) midiSabas.push_back(curChar());
		
#if ALL_NODE_PHYSICS
	auto ndRoot = curSaba()->Pmx->GetNodeManager()->getRootNode();
	//if (ndRoot) { 
	//	//ndRoot->SetAnimationTranslate({ 0, 0, 0 });
	//	ndRoot->UpdateLocalTransform();
	//	ndRoot->UpdateGlobalTransform();
	//}
	curSaba()->setAllDynRbActive(false);
#endif
	}

	if (!sb0) {
		sb0 = arRoot->curArSn->sb;
		if (it0) snLight->setPosition(it0->getAbsolutePosition() + Ctx->gd.baseLightPos * MMD_SABA_SCALE);
	}
	if (!MMDPhysics::cbOnPhysicsStep) setPhyCallBack();
	auto sb = curChar();
	if (sb==sb0) {
		mmd->createMrk();
	}
	else if (isCharacter) {
		mmd->mainMPA->resetSabas();
		if (MMD_ALL_LOOK_AT_SB0 && sb->ndHead) {
			sb->lookAtPos = &sb0->ndHead->rb0->pos;
		}
	}
#if MMD_ACTION_ANIM
	if (isCharacter) {
		sb->loadActionAnimations("data/mmd/actVmd/actionsAll"//baotuiti"//ground" //
			".json5");
		//sb->setAdAnim(0, 1);
	}
#endif
	auto retSn = arRoot->curArSn;
	if ( isCharacter  ) {
		arRoot->setCurSn(arRoot->curArSn, FPVCAM_LOCK_ALL_NEW_SB);//set sb;
		if (isCharacter && sb->isAiCharacter()) {
			sendKeyEvent(KEY_KEY_P); sendKeyEvent(KEY_KEY_O);//第一帧前启用，效果不同，否则人物更懒？
		}
	}
	return retSn;
}
irr::scene::SnArItem* gLoadSabaModel(irr::scene::PMXFileCreateParam  fcp, uint32_t aiFlag) {
	return gAppMain->loadSabaModel(fcp, false, aiFlag);
}
void AppMainAMP::createCubes(BrickWallParams bwp)
{
	CCubeGridSceneNode::createCubes(mmd, arRoot->snMmdSpaceRoot, bwp);
 
}

IrrSaba* AppMainAMP::loadPmxItem(std::string pmxfile, irr::float3 pos, irr::float3 rtt, bool massDec,float scale)
{
	PMXFileCreateParam cp(pmxfile);
	cp.massDec = massDec; cp.massDecRate = 0.3f;
	mmd->Pm.spm.allowGPU = 0; cp.massMul = 1 / scale;
	auto sb = loadSabaModelScale(cp, scale, false, 0x101)->sb;
	mmd->Pm.spm.allowGPU = 1;
	mmd->Pm.spm.fcp.massDec = false;
	sb->rcvEqvData = 10;
	sb->ndRoot->SetAnimationTranslate(pos);
	sb->ndRoot->SetAnimationRotate(rtt*DEGTORAD);
	sb->Pmx->moveAllOffset(pos);

	return sb;
}
irr::scene::SnArItem* AppMainAMP::loadSabaModelScale(saba::PMXFileCreateParam fcp, float sc, bool reset, uint32_t flag)
{
	fcp.modelScale = sc != 1.f;
	fcp.modelScaleVec3 = { sc,sc,sc };
	auto m = loadSabaModel(fcp, reset, flag);
	return m;
}

void AppMainAMP::setPhyCallBack()
{
 
	MMDPhysics::cbOnPhysicsStep = [this](int step, int maxStep)
	{
		if (lockOnSb && lockOnSb->ndHead){
			if (isRenderingFpv) { 
				lockOnSb->ndHead->rb0->scaleVel(0.9, 2);
			}
			if (!lockOnSb->canLookAtCam) {
				//lockOnSb->lookOnMousePos(1);
				lockOnSb->lookOnVelocity();
			}
		}
		sb0->Pom->updateBegin(Ctx->gd.deltaTime/maxStep, step, maxStep);
		if (camRb && CAM_DYN_RB && Ctx->gd.CamFpv ) 
		{			 
			bool isRttCam = baseCamera == Ctx->gd.CamRtt;
			auto anm = Ctx->getCamTCAnimator();  if (isRttCam) {
				anm->animateNode(Ctx->gd.CamRtt, 0);
				Ctx->gd.CamRtt->updateAbsolutePosition(); Ctx->gd.CamRtt->updateMatrices();
			}
			Ctx->gd.camRbMat = camRb->GetTransform();
			glm::vec3 rtt= glm::eulerAngles(glm::quat(Ctx->gd.camRbMat));
			DP(("tmm %f , pos= %f, %f, %f", Ctx->gd.timeMul, rtt.x, rtt.y, rtt.z));
			camRb->scaleVel(pow(isRttCam ? (lockIt?0.7f:0.8f) : 0.95f, Ctx->gd.timeMul), 1);
			camRb->scaleVel(pow((isRttCam ? (lockIt ? 0.1f : 0.6f)  : 0.7f), Ctx->gd.timeMul), 2);
			camRb->addLinearVelToPos(sb0->irr2mmd(baseCamera->getAbsolutePosition()),(isRttCam? (lockIt ? 1.f : 1.15f) :0.35)* Ctx->gd.timeMul);
			auto vlen = glm::length(camRb->getLinearVel());
			glm::mat4 mrtt = glm::mat4(isRttCam ? anm->getRotationMatrix() : baseCamera->getAbsoluteTransformation()) ;


			camRb->addRotationToMatOnNode(mrtt
				//snCam->getAbsoluteTransformation()
				//Ctx->gd.CamRtt->getAbsoluteTransformation()
				, (2000.f + glm::clamp(vlen*10 , 0.f, 9000.f))* (isRttCam ? (lockIt ? 1.f : 0.15f) : 10.0002f ) *Ctx->gd.timeMul);
			//camRb->SetCoMTransform(camRbMat);
				 

			//camRb->setAngVelToPos(sb0->irr2mmd(baseCamera->getTarget()), 1, 1.f, 180.f, true);
			if (KickRbCam) for (int i = 0; i < std::min(16,(int)mmd->sabas.size());i++) {
				auto sb = mmd->sabas[i];
				const float T = 0.5f, Fmul = 1.1f;
				struct KickStgData {
					int stage = 0;
					float t0 = 0, t0last;
					saba::MMDNode* ndFoot;
					bool isL,isLastL;

				};
				vec3 campos = mmd->camPos; campos.y = std::max(1.f, campos.y);
				glm::mat4 camMat = mmd->camMat;
				auto curCam = SceneManager->getActiveCamera();
				if (Ctx->gd.usingCamRbMat) {
					camMat = Ctx->gd.camRbMat; campos = camMat[3]; //sbFw2("sw2", campos+vec3(0,-1,0), {}, 0xFFFF00FF);
				}
				static KickStgData data[16];
				auto& dt = data[i];
				auto& stage = data[i].stage;
				auto& t0 = data[i].t0;
				auto& t0last = data[i].t0last;
				auto& ndFoot = data[i].ndFoot;
				auto& isL = data[i].isL;

				float dR = sb->ndLegR->disTo(campos), dL = sb->ndLegL->disTo(campos);
				float angDeg = glh::angleDegBetweenVec(glh::matRotateVec(sb->ndHead->rb0->GetTransform(), vec3(0, 0, -1)), campos - sb->ndHead->rb0->pos);
				
				bool isFoot = 1;
				float maxKick = 16 * sb->ndCtr->absScale.x;
				if (stage == 0 && (gSceneTime - t0last) > 0.f) {
					bool chgFoot = (dt.isLastL && dR < maxKick * .833f) || (!dt.isLastL && dL < maxKick * .833f);
					if (angDeg<90.f && (dR < maxKick || dL < maxKick) && (gSceneTime - t0last) > T *(chgFoot? 0.0f:1.f)) {
 						stage = 1, t0 = gSceneTime;
						isL = chgFoot ? !dt.isLastL : dL < dR;
						dt.isLastL = isL;
						ndFoot = isFoot ? (isL ? sb->ndFootL : sb->ndFootR):
							(isL?sb->ndHandL : sb->ndHandR);
					}
				}
				float t = (gSceneTime - t0);

				if (stage) {
					MMDNode* p1 = isFoot ? ndFoot->GetParent() : ndFoot->GetParent()->GetParent(), * p2 = isFoot ? p1->GetParent() : p1->GetParent()->GetParent();

					float dis = isL ? dL : dR;
					 
					switch (stage)
					{
					case 1:
					{
						float f = 10* Fmul, st1 = T*0.666f;
						float l1dis = std::min(dis - 3.f, 6 + 6 * sb->ndCtr->absScale.x);
						vec3 fp = glh::matTransformVec(camMat, vec3(0, 0, l1dis - sb->ndCtr->absScale.x * 6)); //sbFw2D("pt1s", fp, {}, 0xFFFFFF00);
						vec3 fp1 = glh::matTransformVec(sb->ndCtr->rb0->GetTransform(),vec3(isL?-1.f:1.f,0,-3.9f)*sb->ndCtr->absScale.x); //sbFw2D("pt1s", fp1, {}, 0xFFFF0000);
						vec3 adv = ndFoot->rb0->addLinearVelToPos(fp, f);
						ndFoot->rb0->scaleVel(0.1f);

						sb->ndCtr->rb0->scaleVel(0.5f, 2);
						float stageRatio = glm::clamp(t / st1, 0.f, 1.f);
						//sb->ndCtr->rb0->rotateFromDirToDir(vec3(0, 0, -1), campos - sb->ndCtr->rb0->pos, 1600.f*(1- stageRatio *.833f) * Fmul);
						sb->ndCtr->rb0->setAngVelToPos(campos, 100.f* (1 - stageRatio * .833f) * Fmul);
						//sb->ndHead->rb0->rotateFromDirToDir(vec3(0, 0, -1), campos - sb->ndHead->rb0->pos, 300.f * (1 - stageRatio * .833f) * Fmul);
						ndFoot->rb0->rotateFromDirToDir(isFoot?vec3(0, -1, 0):(isL?vec3(-1,-1,0):vec3(1,-1,0)), campos - ndFoot->rb0->pos, 1000.f * (0.1f + stageRatio) * Fmul);
						p2->rb0->scaleVel(0.3f, 2);
						if (//glh::angleDegBetweenVec(sb->Rb0()->getPosition() - campos, glm::quat(mmd->camMat) * vec3(0, 0, 1)) 
						glh::posInCameraViewAngleDeg(camMat,sb->Rb0()->getPosition())
						> 30.f) {
							vec3 adv1 = p1->rb0->addLinearVelToPos(fp1, f * 0.25);				//ndFoot->GetParent()->scaleVel(0.6f);
							//ndFoot->GetParent()->GetParent()->rb0->addLinearVel(-(adv) * 0.25f);			
							DP((">30"));
						}
						else						
							p2->rb0->addRotationToMatOnNode(camMat* glm::rotate(glm::mat4(1), glm::radians(150.f), vec3(1, 0, 0)), 900.f * (0.01f + stageRatio) * Fmul);

						if (t > st1) stage = 2;
					}
					break;
					case 2:
					{
						//sendKeyEvent(KEY_KEY_R);
						float f = 21 * Fmul;
						vec3 adv = ndFoot->rb0->addLinearVelToPosLimitDis(glh::matTransformVec(camMat, vec3(0, 0, 2)), f,3, maxKick);
						p1->rb0->addLinearVel(-adv * 0.1f );
						//ndFoot->rb0->rotateFromDirToDir(vec3(0, -1, 0), campos - ndFoot->rb0->pos, 1000.f *  Fmul);

						if (t > T)  stage = 0, t0last = gSceneTime;
					}
					break;
					}
				}
			}



		}
		else if (!CAM_DYN_RB && lockOnSb) {
			//lockOnSb->ndHead->rb0->scaleVel(0.5f, 1);
			if (lockOnSb->ndHead && lockOnSb->ndHead->rb0)lockOnSb->ndHead->rb0->scaleVel(0, 2);
		}
#if MMD_ZERO_FRICTION
		if (!Driver->dsd.paused())
			for (auto sb : mmd->sabas) {
				auto rb = sb->Rb0();
				auto vel = rb->getLinearVel();
				auto rtt = rb->getRotation();
				vec3 front=rb->getRotation()* vec3(0, 0, -1);
				float projVF = glm::dot(vel, front);
				float ab = abs(sb->ndLegL->rb0->getPosition().y - sb->ndLegR->rb0->getPosition().y)*10.f/sb->ndCtr->absScale.x;
					+glh::angleDegBetweenVec(sb->ndLower->rb0->getRotation() * vec3(0, 1, 0), vec3(0, 1, 0));
				if (ab > 10.f)
				{
					float closeForce = std::min(20.f, (ab - 10.f) * 2.5f);
					sb->ndLeg1L->rb0->addLinearVelToPosLimitDis(sb->ndLeg1R->rb0->getPosition(), closeForce*0.35f,.0f,10.f);
					sb->ndLeg1R->rb0->addLinearVelToPosLimitDis(sb->ndLeg1L->rb0->getPosition(), closeForce*0.35f,.0f,10.f);
					sb->ndHandL->rb0->addLinearVelToPos(sb->ndLower->rb0->getNodePos()+ rtt * vec3(6,6,0), closeForce * 0.35f);
					sb->ndHandR->rb0->addLinearVelToPos(sb->ndLower->rb0->getNodePos()+ rtt * vec3(-6,6,0), closeForce * 0.35f);
					sb->ndLower->rb0->addRotationToMatOnNode_MatRttResetXZ(2000.f + (ab - 10.f) * 700.f);
					//rb->scaleVel(0.96f, 3);
					sb->ndHead->rb0->addRotationToMatOnNode_MatRttResetXZ(sb->ndLower->rb0->getNodeTransform(),  1.f+ (ab) * 100.f, -0.9f, 0);
				}
				//if (projVF < 10.f) { //auto push
				//	rb->scaleVel(0.9f, 3); 
				//	rb->addLinearVel(rb->getRotation() * vec3(0, 0, -20));
				//}
 
				
				auto q=rb->addRotationToMatOnNode_MatRttResetXZ(sb->ndLower->rb0->getNodeTransform(),6000.f + ab * 60.f);
				//sbFw2LineD("pt", rb->getPosition(), rb->getPosition() + quat( sb->ndLower->rb0->getNodeTransform()) * vec3(0, 0, -10), 0xFFFFFFFF, 60);
				//sbFw2LineD("pt", rb->getPosition(), rb->getPosition()+q*vec3(0,0,-10), 0xFFFF00FF, 60);
				sb->ndUpper2->rb0->addRotationToMatOnNode_MatRttResetXZ(1000.f + (ab) * 100.f, -.1f, 0);
				sb->ndUpper2->rb0->addLinearVel({ 0, 6 + ab * 0.25f, 0 });
				if (rb->pos.y < 30 && rb->vel.y < -0.f) {
#if 0
					auto ndSfn = sbScene->findNode(L"safeNet");
					if (ndSfn
						&& glm::length((sb->Rb0()->pos - ndSfn->getGlobalPos()) * vec3(1, 0, 1)) > 3.f
						) {
						auto pos = sb->Rb0()->pos; pos.y = 20.f;
						ndSfn->setAnimToGlobalPos(glm::mix(ndSfn->getGlobalPos(), pos, 1.0f));
						if (rb->pos.y < 20) sb->Pmx->scaleBodyVel(vec3(0.9), 1);
						//FRAMEWAITER_CALL_B(1) {
							//ndSfn->rb0->pdbResetPos();
						sbFw2D("sw21s", pos, UaRandVec3() * 100.f, 0xFFFFFF00);
						//},1);

					}
#endif
					if (sb->Rb0()->pos.y < -6) {
						sb->Pmx->moveAllOffset(vec3(sb->Rb0()->node->mGlobalAnim[3]) - sb->Rb0()->getPosition(), false);
						sb->setAllDynRbActive(false, 1);
						FRAMEWAITER_CALL_B(10) {
							sb->setAllDynRbActive(true, 1);
							sb->Pmx->resetRigidBodies(1);
						});
					}
				}

				if (MMD_LOOKAT_SB0 && sb->getItemIdx() > 0)
				{
					sb->mmdLookAt = sb0->ndHead->rb0->pos;
					sb->ndHandL->rb0->addLinearVelToPosLimitDis(mmd->sb0->ndCtr->rb0->pos, 10, 1, 100);
					sb->ndHandR->rb0->addLinearVelToPosLimitDis(mmd->sb0->ndCtr->rb0->pos, 10, 1, 100);
					rotateRbDir(sb->ndUpper2->rb0, vec3(0, 0, -1), mmd->sb0->ndCtr->rb0->pos - sb->ndUpper2->rb0->pos, 10000.f);
					rotateRbDir(sb->ndLower->rb0, vec3(0, 0, -1), mmd->sb0->ndCtr->rb0->pos - sb->ndUpper2->rb0->pos, 1000.f);
					rotateRbDir(sb->ndHandL->rb0, vec3(-1, -1, 0), mmd->sb0->ndCtr->rb0->pos - sb->ndHandL->rb0->pos, 1000.f);

				}

			}

#endif

#if SEESAWX
		for (int i = 0; i < std::min(2,(int)mmd->sabas.size()); i++) {
			auto sb = mmd->sabas[i];
			bool isL = i % 2 == 0;
			float a = (isL ? 1 : 0) * piFloat + gSceneTime*piFloat*2 * 0.5f, rlen = 10;
			sb->ndCtr->rb0->addLinearVel(vec3(0, sin(a) * 20, 0));
		}

#endif
		arRoot->forEachArChild([=](SnArItem* sn) {
				
			if (beatMan.beats.size())
			{
				float t = vp.renderTime + Ctx->gd.deltaTime * step / maxStep ;
				if (MMD_ACTION_ANIM) t += MMD_ACTION_TIME;
				static int lastBeatId = -1;
				float rat; int beatId= beatMan.getBeatVal(t,rat);
				if (lastBeatId == -1) lastBeatId = beatId;
				if (beatId >= 0)
				{

#if BEAT_TO_JSON
					if ( vp.working) {
						
						if (BEAT_TO_JSON_first) {
							BEAT_TO_JSON_first = false;
							b2j_jss.SetFile("out/brickWallVmdExt.json",false);
							b2j_jss.Clear();
							auto& root = b2j_jss.refRootValue();
						}
						static int lastbeatId = -1 ,lastFrame=-1;
						int frame = int(sb0->getCurTime() * 30.f + 0.5f);
						if (beatId != lastbeatId && frame - lastFrame>6) {
							lastbeatId = beatId; lastFrame = frame;
							Json::Value jv, jvp;
							jv["frame"] = frame;
							jv["event"] = "BrickWall";
							auto lp=sb0->ndFootL->rb0->getPosition();
							auto rp = sb0->ndFootR->rb0->getPosition();
							auto p = lp.z>rp.z?lp:rp;
							jvp["hitpos"].append(p.x); jvp["hitpos"].append(p.y); jvp["hitpos"].append(p.z);
							jv["param"] = jvp;
							b2j_jss.refRootValue()["events"].append(jv);
							testTextFw(true, ualib::wstrFmt(L"%d , %d",beatId, frame), 0x10);
							sb0->mmdFw(p, Eqv->getFwIdxByFwIdStr(2, "fw21s"), {}, SColorf(1,0,0,1));
						}
					
					}
					else
#endif
#if MMD_ACTION_ANIM
					{
#define ACTANIM_DROP_SB 0
						static float lastT = -1;
						if (beatId != lastBeatId) {
							lastBeatId = beatId;
							const float minIntv = 0.2f;
							static int sid = 0;
							sid= (sid+1)%mmd->sabas.size();
							auto sb=mmd->getSaba(sid);
							if (t - lastT >= minIntv || t < lastT) {
								lastT = t;
								float nextTime = 100.f;
								int j = 1;
								do { nextTime = beatMan.beats[beatId + j] - beatMan.beats[beatId]; j++; }
								while (beatId + j < beatMan.beats.size() && nextTime < minIntv);
								static int icc = 0;
								int i = (icc += 1) % sb->Pmx->vmdAddAnims.size();// UaRand(sb->Pmx->vmdAddAnims.size());
								if (mmd->sabas.size() < 2) sb->Pmx->vaGetLessTime(nextTime);
								static bool nextShort;
								bool curShort = nextShort;
								float anmOfs = curShort?0.f:0;
								nextShort = nextTime < 0.35f;
								if (sb->Pmx->vmdAddAnims.size()>1 && mmd->sabas.size() < 2 && (nextShort || curShort) ) {
									static int cc = 0;
									i = 1+(cc++%2); 
									
								} 

								auto& va = sb->Pmx->vmdAddAnims[i];
								float dt = MMD_ACTION_TIME - va.vi.actT +(!sb->Rb0()->GetActivation()?0:ACTANIM_DROP_SB?0.1f:0.000367f);

								FrameWaiter fw; fw.waitNframeAndRun(1, [=](FWTask& t) {						
								auto pm = Ctx->gd.apm;
								pm.flag = ACTANIM_DROP_SB? 0x00020001:0x00020000; pm.pm1 = i; pm.tgtSb = sb;
								// pm.flag |= 0x2000;
								addMmdObj( pm);
								if (sb->Pmx->vaPastT(0.2f) && sb->Pmx->vaPastT(0.5f)) {
									sb->setAdAnim(i, 1,dt, anmOfs);
								}
								Eqv->setPtrFwByIdStr(0, "label0.2s");

								testTextFw(true, ualib::wstrFmt(L"%d", i), 0x10);
									
								
									} );

								

							}
							 
						}
					}
#endif
					
				}
			}
			sn->sb->updatePhysicsStep(step, maxStep);
			},1);
		//if (!sb0->getRb0()->GetActivation()) {
		//	sb0->setAllRbActive(true); sb0->Pmx->resetRigidBodies(); sb0->Pmx->GetMMDPhysics()->clearForces();
		//}
		if (scMmdFrame >= 0 && !Ctx->scenePaused)
		{



			auto s0 = sb0, s1 = sb1;

			scMmdFrame++;
#if 1
			static int fcc = -1;
			if (fcc == Ctx->gd.frameCount) throw;
			if (scMmdFrame == 1 || scMmdFrame > 60 /*&& scMmdFrame%2*/) {
				MMDNode* node = sb0->ndRoot;// findNode(L"头饰_1");
				static float sc = 0.2;
				sc *= 1.05f;
				if (sc < 1) {
					node->SetScale({ sc,sc,sc });

					s0->Pmx->resetRigidBodies();
					if (sc > 0.3)
					{
						s0->setAllDynRbActive(false,1);

					}
				}
			}
			else if (scMmdFrame == 10) sendKeyEvent(KEY_F9);

			//else 			s0->Pmx->resetRigidBodies();
#else



			int maxf = 600, stg1 = 30;
			auto pos = (sb0->ndHandR->getGlobalPos() + sb0->ndHandL->getGlobalPos()) / 2.f + float3(0, 0, 0);
			//sb0->ndUpper2->getGlobalPos() + float3(0, 3, -1);
			if (scMmdFrame < stg1) {
				float rat = float(scMmdFrame) / (stg1 - 1);
				scaleMmdNode(sb1, sb1->ndCtr, 0.02 + 0.03 * rat, 2);
				float3 add = float3(0, 0.0f, float(stg1 - 1 - scMmdFrame) / stg1 / 10);

				if (scMmdFrame < 10) {
					s1->ndCtr->rb0->SetCoMTranslate(pos + add);
					s1->ndHead->rb0->SetCoMTranslate(s0->ndHandL->getGlobalPos() + float3(0, 2, -1) + add);
					s1->ndFootL->rb0->SetCoMTranslate(s0->ndHandR->getGlobalPos() + float3(0, 1, -1) + add);
					s1->ndFootR->rb0->SetCoMTranslate(s0->ndHandR->getGlobalPos() + float3(0, 1, 0) + add);
				}
			}
			else if (scMmdFrame > maxf) {
				scMmdFrame = -1;
			}
			else {
				int stg2 = 500;
				float sc = 0.05f + 0.95f * (1 - cos(float(scMmdFrame - stg1) / (maxf - stg1) * core::HALF_PI));
				sb0->ndHead->rb0->setAngVelToPos(sb1->ndCtr->getGlobalPos(), 10);
				if (scMmdFrame < stg2) {
					float sr = sin(sc * 30), cr = cos(sc * 30); float sr1 = 1 + sr;
					s1->ndCtr->rb0->setLinearVelToPos(pos + float3(cr * sc, 0.0f + sc + sr * sc, sc * 3), 100);
					s1->ndUpper2->rb0->setLinearVelToPos(pos + float3(0, 1.1f + sc * 6, sc * 3), 10);
					s1->ndFootL->rb0->setLinearVelToPos(s0->ndHandL->getGlobalPos() + float3(sc * sr1 * 2, sc * 2, -2), 20);
					s1->ndFootR->rb0->setLinearVelToPos(s0->ndHandR->getGlobalPos() + float3(-sc * sr1 * 2, sc * 2, -2), 20);
				}
				else {
					{
						if (!s0->ndHandR->rb0->GetActivation()) {// s0->setBonePhsActive(s0->ndUpper2,true,true);//
							sb0->setAllRbActive(true);
							//s1->ndCtr->rb0->setLinearVelToPos(s0->ndUpper2->rb0->getPosition() + float3(0, -2, -1), 30);

						}
					}
					s0->ndHandR->rb0->setLinearVelToPos(s1->ndLegL->rb0->getPosition() + float3(0, -2, -1), 30);
					s0->ndHandL->rb0->setLinearVelToPos(s1->ndUpper2->rb0->getPosition() + float3(0, -2, -1), 30);


				}
				if (scMmdFrame>60)
					sb1->ndHead->rb0->setAngVelToPos(sb1->mmdCamPos, 5.1);

				//it1->saba->ndCtr->rb0->setLinearVelToPos(pos, 100);

				scaleMmdNode(sb1, sb1->ndCtr, sc, (scMmdFrame>stg2) ? 0 : 0);
			}
#endif

		}
		sb0->Pom->updateEnd(Ctx->gd.deltaTime / maxStep, step, maxStep);
		double dt = Ctx->gd.deltaTime / maxStep;
		SceneManager->dSceneTime += dt;
		gSceneTime = SceneManager->dSceneTime;
		//DP(("gSceneTime %.8f dt %.8f", gSceneTime,dt));
	};
}



void AppMainAMP::StageStringMsgFromUIThread(std::string msg, std::string pm1)
{
	if (!inited) return;
	if (msg == "arJson")
	{
		int us;
		MatrixRecorder::DrDataStruct ds;
		ualib::UaJsonSetting jss("camdatjson2");
		jss.LoadJsonString(pm1);
		Json::Value js = jss.copyRootValue();
		//DP(("JS %s",pm1.c_str()));
		bool por = Ctx->gd.scrHeight > Ctx->gd.scrWidth;
		if (Ctx->isApp(APPID_ArDatCam) && por) Eqv->ARCamRotation = 90;
		Ctx->gd.CamNormal->setFOV(Eqv->JsonToDS(us, js, ds, 21, false));
		Eqv->UpdateCamState(&ds, por);

	}
	else if (msg == "danmaku")
	{ 
		onDamakuCmd(pm1,true);
	}
#if DRONE_AR
	else if (msg == "arJsonDji")
	{
		int us;
		MatrixRecorder::DrDataStruct ds;
		ualib::UaJsonSetting jss("camdatjson2");
		jss.LoadJsonString(pm1);
		Json::Value js = jss.getRootValue();
		bool por = Ctx->gd.scrHeight > Ctx->gd.scrWidth;
		int ms;
		Eqv->JsonToDS_DJI(ms, js, ds, 8);

		if (dsFrame == -1) { lastDs1 = lastDs; lastDs = ds; }
		else { lastDs1 = lastDs; lastDs = curDs; }
		curDs = ds;
		dsFrame = 0;
	}
	else if (msg == "ajDjiTgt")
	{
		int us;
		MatrixRecorder::DrDataStruct ds;
		ualib::UaJsonSetting jss("camdatjson2");
		jss.LoadJsonString(pm1);
		Json::Value js = jss.getRootValue();
		bool por = Ctx->gd.scrHeight > Ctx->gd.scrWidth;
		int ms;
		Eqv->JsonToDS_DJI(ms, js, ds, 8);
		if (snBox) {
			snBox->setPosition(ds.d.pos + vector3df(0, -500, 0));
			snBoxSub->updateTransform();
			matrix4 m = snBoxSub->getAbsoluteTransformation();
			DP(("DjiTgtPos %f,%f,%f", ds.d.pos.X, ds.d.pos.Y, ds.d.pos.Z));
			//DP(("\n%3.3f %3.3f %3.3f %3.3f\n%3.3f %3.3f %3.3f %3.3f\n%3.3f %3.3f %3.3f %3.3f\n%3.3f %3.3f %3.3f %3.3f\n",m[0], m[1], m[2], m[3],m[4], m[5], m[6], m[7],m[8], m[9], m[10], m[11],m[12], m[13], m[14], m[15]));
		}
		if (mmd) {
			mmd->RootNode->setPosition(ds.d.pos);
			mmd->RootNode->setRotation(ds.d.rtt);

			mmd->RootNode->setScale(ds.d.scaleAdd);
		}
	}
#endif

	else if (msg == "vmdPath")
	{
		mmd->vmdPaths[0] = pm1.c_str();
		if (mmd->sabas.size() > 0) mmd->sabas[0]->loadAnimation(ualib::Utf8toWcs(pm1).c_str(), false);
	}
	else if (msg == "vmdFile0")
	{
		mmd->curCtrlSb([=](irr::scene::IrrSaba* sb) {
		 sb->loadMotion(ualib::Utf8toWcs(pm1).c_str());
		sb->setPlaying(true);
			});
	}
	else if (msg == "vmdFile1")
	{
		auto sb = mmd->nextSaba(curChar());
		sb->loadMotion(ualib::Utf8toWcs(pm1).c_str());
		sb->setPlaying(true);	 
	}
	else if (msg == "vmdFile2")
	{
		auto sb = mmd->nextSaba(mmd->nextSaba(curChar()));
		sb->loadMotion(ualib::Utf8toWcs(pm1).c_str());
		sb->setPlaying(true);

	}
	else if (msg == "pmxFile")
	{
		//arRoot->onModelUploaded(mmd, nullptr, ualib::Utf8toWcs(pm1).c_str(), arRoot->curArSn, false,true);
		loadSabaModel(PMXFileCreateParam(pm1));
	}
	else if (msg == "wavFile")
	{
		PLAY_AUDIO_FILE = ualib::Utf8toWcs(pm1); customAudioLoaded = true;
		curInVideo = ualib::Utf8toWcs(pm1).c_str();
	}
	else if (msg == "assFile")
	{
		SUBTITLE_PATH = ualib::Utf8toWcs(pm1); 
		curInSubtitle = ualib::Utf8toWcs(pm1).c_str();
	}
	else if (msg == "textChg")
	{
		curArText = pm1;
		genStkText();
	}
	else if (msg == "textSet")
	{
		curArText = pm1; toShowTextCD = 2;
	}
#if HAS_MIDI
	else if (msg == "midFile")
	{		
		if (Plates.size()) {
			if (HAS_PIANO) if (curMidiFile.size() > 1) {
				curMidiFile = ualib::WcharToAnsi(ualib::Utf8toWcs(pm1));				
				Ctx->Midi.startPlay(int64_t(Ctx->gd.time * 1000), 1, curMidiFile, &Eqv->mdTrackData);				
			}
			curMidiFile = ualib::WcharToAnsi(ualib::Utf8toWcs(pm1));
			for (auto& plate : Plates) plate->resetFile(ualib::Utf8toWcs(pm1));
			;
			Eqv->setPtrFwByIdStr(0, "label");
			testTextFw(true, ualib::wstrFmt(L"%d", Plates[0]->trackCount), 0x10);
		}
		else {
			curMidiFile = ualib::WcharToAnsi(ualib::Utf8toWcs(pm1));
			Ctx->Midi.startPlay(int64_t(Ctx->gd.time * 1000), 1, curMidiFile, &Eqv->mdTrackData);
			
		}
		dropInMidiFile = true;
	}
#endif
	else if (msg == "zip")
	{

#if IS_WIN
		auto rf = Ctx->getFileSystem()->createAndOpenFile(ualib::Utf8toWcs(pm1).c_str());
#else
		auto rf = Ctx->getFileSystem()->createAndOpenFile(io::path(pm1.c_str()));
#endif

		if (rf) {
			irr::SEvent evo{ EET_CMD_INPUT_EVENT };
			evo.CmdInput.cmdId = 20127;
			evo.CmdInput.pm1 = (uint64_t)malloc(rf->getSize());
			evo.CmdInput.pm2 = rf->getSize();
			rf->read((void*)evo.CmdInput.pm1, evo.CmdInput.pm2);
			rf->drop();
			StageOnCmdEvent(evo.CmdInput);
		}
	}
#if IS_WIN
	else if (msg == "pngFile")
	{
		bool isStyle = true;

		apng::Image img;
		apng::load_image(pm1.c_str(), &img);
		isStyle = img.w <= 256;


#if 1
		if (isStyle) {
			if (!Eqv->loadStyleFile(pm1))
			{
				Eqv->inputPng = pm1;
				Eqv->recreateOnUpdate();
			}
		}
		else if (bgImg) bgImg = Driver->getTexture(pm1.c_str());
		else if (snSkyDome) {
			arRoot->Cs.skyDomePath = ualib::AnsiToWchar(pm1); 
			snSkyDome->setMaterialTexture(0, Driver->getTexture(arRoot->Cs.skyDomePath.c_str()));
			if (snSkyDomeM) snSkyDomeM->setMaterialTexture(0, Driver->getTexture(arRoot->Cs.skyDomePath.c_str()));
		}

#else
		arRoot->curArSn->saba->reloadTex(ualib::Utf8toWcs(pm1).c_str());
#endif
	}
	else if (msg == "jpgFile")
	{
		if (bgImg) bgImg = Driver->getTexture(pm1.c_str());
		else if (snSkyDome) {
			arRoot->Cs.skyDomePath = ualib::AnsiToWchar(pm1); 
			snSkyDome->setMaterialTexture(0, Driver->getTexture(arRoot->Cs.skyDomePath.c_str()));
			if (snSkyDomeM) snSkyDomeM->setMaterialTexture(0, Driver->getTexture(arRoot->Cs.skyDomePath.c_str()));
		}
	}	
	else if (msg == "zipDrop")
	{

		//auto rf = Ctx->getFileSystem()->createAndOpenFile(pm1.c_str());
		loadSabaModel(PMXFileCreateParam(pm1));
	}	
	else if (msg == "meshFile")
	{
		{
			auto ms = SceneManager->getMesh(pm1.c_str());
			auto snMesh = SceneManager->addMeshSceneNode(ms, Ctx->gd.RootSn, -1, { 0,0,0 });
			
			snMesh->getMaterial(0).MaterialType = EMT_TRANSPARENT_ALPHA_CHANNEL;
			snMesh->getMaterial(0).DiffuseColor = 0xC0808080;
			snMesh->setScale(333.333); 
			snMesh->getMaterial(0).Wireframe = true;
			snMesh->setRotation({ -90, -90, 0 });//instant-ngp -> mashlab ply
		}
	}
	else if (msg == "voxFile")
	{
		vmsnId = (vmsnId + 1)% VOX_NUM;
		if (!vmsn[vmsnId]) {
			vmsn[vmsnId] = new CVoxelMeshSceneNode(L"data/mesh/cubeRS.obj", mmd->sb0, SceneManager, mmd); vmsn[vmsnId]->drop();

		}
		vmsn[vmsnId]->loadVox(pm1,{0,3,0},1,1);
	}
	else if (msg == "recBegin")
	{
		sendKeyEvent(KEY_KEY_E, true,true);
	}
	else if (msg == "recEnd")
	{
		sendKeyEvent(KEY_F2, true,true,false,false);
	}
#endif
}



void AppMainAMP::genStkText(bool all,int id, float spdmul )
{
	//Eqv->clearFws();
 
	if (all)
	{
		 
		arRoot->forEachArChild([=](SnArItem* sn) {
			if (sn->sb && sn->sb->svgMan) {
				sn->sb->svgMan->fixLineNum = 0;
				sn->sb->onClear();
				sn->sb->svgMan->GenSvTxtFw(svgPm.getText(sn->sb->getItemIdx() ));
			}
		 	});
	}
	else
	{
 
		auto sb = curChar();
		if (!camSave) saveCam(Ctx->gd.CamNormal);
		if (sb)
			sb->saveCam(SceneManager->getActiveCamera());
		if (auto sm= sb->svgMan) {
			sb->onClear();
			sm->startAppendMul = spdmul; static int addid = 0;
			if (id>=0)
			sm->GenSvTxtFw(svgPm.getText(curChar()->getItemIdx() + id + (Ctx->getEvtRcv()->IsKeyDown(KEY_LSHIFT) ? ++addid:0)));// ualib::Utf8toWcs(curArText));
			else
			sm->GenSvgFw(""); //

		}
	}
}

int64_t AppMainAMP::StageOnCmdEvent(const irr::SEvent::SCmdInput& ce)
{
	if (!inited) return 0;
#if ASYNC_AR
	if (ce.cmdId==109)
	{
		arUdpate();
		return 0;
	}
#endif
	if (ce.cmdId & 0x10000000) {
		return arRoot->handleEditorCmd(ce);
	}

	switch (ce.cmdId)
	{
	case 211://arRoot->curArSn->saba->openPoseDraw(); //vp.copyScrShotToClipboard();
		break;
#if USE_OXR
	case 800: // XR Update Event
	{

	}
	case 801: // XR Button Event
	{
		XRBtnEnum bt = (XRBtnEnum)ce.pm1;
		switch (bt)
		{
		case ualib::xb_none:
			break;
		case ualib::xb_select:
			if (curOud.h[ce.pm2].buttons[xb_select])
			{
				//ctrOud = origOud;
			}
			break;
		case ualib::xb_squeeze:
			if (curOud.h[ce.pm2].buttons[xb_squeeze])
			{
				ctrOud = origOud;
			}
			break;
		case ualib::xb_menu:
			break;
		case ualib::xb_haptic:
			break;
		default:
			break;
		}
	}
	break;
#endif
 	case 10001:  //rec started
	{
		bRecording = true;
		//if (mmd)		mmd->resetAni();
	}
	break;
	case 10002:  //rec stopped
	{
		bRecording = false;
	}
	break;
	case 10100:  //show
	{
		bMmdSHow = ce.pm1;

#if HAS_MMD
		if (mmd->sabas.size() < 1)
		{
			mmdMax = ce.pm2 - 1;
			mmd->createSabas(ce.pm2);
			nextVmd();
		}
		mmd->RootNode->setVisible(bMmdSHow);
		if (bMmdSHow && snBox) snBox->setVisible(false);
#endif

	}
	break;
	case 10101:  //show snBox
	{
		bTgtShow = ce.pm1;
		if (snBox) snBox->setVisible(bTgtShow);
		Eqv->pifSnOrigin->setVisible(bTgtShow);
	}
	break;
	case 10102:  //show gimbal
	{
		//Eqv->snLW->setVisible(!Eqv->snLW->isVisible());
	}
	break;
	case 10103:  //show gimbal
	{
		//Eqv->snLW->toggleCps1Type();
	}
	break;
	case 10105:  		mmd->ikVisable = !mmd->ikVisable;break;
	case 10106:  	arRoot->curArSn->Cei->previewPhysics = !arRoot->curArSn->Cei->previewPhysics; break;
	case 10200:  //delay frame
	{
		bDelayFrame = ce.pm1;
	}
	break;
	case 20001:  //set mmd frame
	{
		if (mmd) {

			mmd->autoPause = ce.pm2 & 1;
			mmd->pauseDemoing = mmd->autoPause && bool(ce.pm2 & 2);
			mmd->owTimeDiv = (ce.pm2 & 0xFF00) >> 8;
			mmd->owTimeMul = (ce.pm2 & 0xFF00000000) >> 32;

			bool prepareRecFrame = bool(ce.pm2 & 4);
			int64_t dt = 33333 * mmd->owTimeMul / mmd->owTimeDiv / 100;
			if (mmd->autoPause && mmd->sabas.size() > 0)
			{
				if (prepareRecFrame) {
					mmd->pauseDemoFr = 0;
					return 2;
				}
				mmd->pauseIdx = (ce.pm2 >> 16) & 0xFFFF;
				int ret = 0;
				if (ret = mmd->sabas[0]->isCamJump(float(ce.pm1 / 1000000.0))) {
					mmd->setOverrideTIme(ce.pm1, Ctx->gd.time, dt);
					//mmd->setOverrideTIme(ce.pm1- mmd->pauseIdx* 33333333, Ctx->gd.time);
					return ret;
				}

			}
			mmd->setOverrideTIme(ce.pm1, Ctx->gd.time, dt);
		}

	}
	break;
	case 20100:  //mmd count
	{

		mmdMax = ce.pm1 - 1;
		mmd->resetPmxFiles(mmdMax + 1);
		mmd->setPmxFile("dir/yj.pmx");
	}
	break;
	case 20110:  //next model
	{
		nextModel();
	}
	break;
	case 20111:
	{
		static bool defaultPmx = true;
		if (defaultPmx)		mmd->setPmxFile("mmd/pmx/s/1.pmx");
		else mmd->resetPmxFiles();
		defaultPmx = !defaultPmx;
	}
	break;
	case 20121:
	{

		if (arRoot)		arRoot->onModelUploaded(mmd, nullptr, "ulpmx/", nullptr, false);

	}
	break;
	case 20122:
	{
		if (mmd->sabas[0])
			mmd->sabas[0]->loadAnimation(Ctx->getDataFilePath("cur.vmd"), false);
	}
	break;
	case 20126:
	{
		static int pmxcc = 0;
		std::string dir = strFmt("dir%d/", pmxcc++);
		auto fs = Ctx->getFileSystem();
		auto rfz = fs->createMemoryReadFile((void*)ce.pm1u, ce.pm2, strFmt("dir%d.zip", pmxcc).c_str());
		io::IFileArchive* fa{};
		if (fs->addFileArchive(rfz, true, false, io::EFAT_ZIP, "", dir.c_str(), &fa))
		{
			if (arRoot)		arRoot->onModelUploaded(mmd, fa, dir.c_str(), arRoot->curArSn, true);
			fs->removeFileArchive(fa);
		}

		rfz->drop();


	}
	break;


	case 20127:
	{
		static int pmxcc = 0;
		std::string dir = strFmt("dir%d/", pmxcc++);
		auto fs = Ctx->getFileSystem();
		auto rfz = fs->createMemoryReadFile((void*)ce.pm1u, ce.pm2, strFmt("dir%d.zip", pmxcc).c_str());
		io::IFileArchive* fa{};
		if (fs->addFileArchive(rfz, true, false, io::EFAT_ZIP, "", dir.c_str(), &fa))
		{
#if 1
			irr::SEvent evo{ EET_CMD_INPUT_EVENT };
			auto& p = arRoot->Cs.icp;
			p.itemType = 1;
			p.itemSubType = SVG_MMD_WRITE ? 2 : 0;
			p.posType = 0;
			p.posFrontDistance = 6000;;

			evo.CmdInput.cmdId = 0x10000000 | cceItemNew;
			evo.CmdInput.pm1 = StageOnCmdEvent(evo.CmdInput);
			arRoot->curArSn->AIFlag = 0x11;
			evo.CmdInput.cmdId = 0x10000000 | cceItemUpdate;
			CsEditorItem* ei = (CsEditorItem*)evo.CmdInput.pm1;
			ei->pos.x = 0;	ei->pos.y = 0;		ei->pos.z = 0;
			StageOnCmdEvent(evo.CmdInput);
#endif
			if (arRoot)		arRoot->onModelUploaded(mmd, fa, dir.c_str(), arRoot->curArSn, false);
			fs->removeFileArchive(fa);
		}

		rfz->drop();


	}
	break;

	case 20128:
	{
		char* psz = (char*)ce.pm1;
		auto fp = Ctx->getDataFilePath("motions/") + psz + ".vmd";

		if (arRoot->curArSn->loadMotion(fp))
			arRoot->curArSn->leiBk.motionFile = std::string(psz) + ".vmd";
	}
	break;
	case 20129:
	{
		char* psz = (char*)ce.pm1;
		auto fp = Ctx->getDataFilePath("poses/") + psz + ".vpd";

		if (arRoot->curArSn->loadMotion(fp))
			arRoot->curArSn->leiBk.motionFile = std::string(psz) + ".vpd";
	}
	break;


	case 20151:  //next vmd
	{
		nextVmd();
	}
	break;
	case 20201:
	{
		mmd->toggleLnv(0);
	}
	break;
	case 20202:
	{
		mmd->toggleLnv(1);
	}
	break;

	case 20301:
	{
		mmd->sabas[0]->showGuqin(ce.pm1);
	}
	break;


	//VIDEO PROCESSING 
	case 1291001:  // videoInfo  onFrameInfo
	{
		if (ce.unU64)
			vp.curFFfd = *(ualib::FF_FrameData*)ce.unU64;
		else
			vp.curFFfd.mediaId = 0;

		if (vp.curFFfd.mediaId == 0)
		{
			//vp.reset();
		}

		int id = vp.curFFfd.mediaId;
		//DP(("DECVID %lld,%lld", ce.pm1, ce.pm2));//1080,1776 
		if (id >= sizeof(vp.fr) / sizeof(vp.fr[0]))  throw;
		vp.fr[id] = vp.curFFfd;

		float mediaPtsTimeS = vp.curFFfd.frameStartS;

		if (id == 0)
		{
			vp.durationMs = vp.fr[id].durationUs / 1000;
			DP(("Video Duration %lld", vp.durationMs));

			if (vp.stage() == 0)  // VIDEO FRAME START 
			{
				vp.stage(1);
				vp.ivpm.w = vp.curFFfd.w;
				vp.ivpm.h = vp.curFFfd.h;
				vp.ivpm.fps = float(vp.curFFfd.fpsNum) / vp.curFFfd.fpsDen + 0.5f;
				vp.ovpm = vp.ivpm;
				vp.hasVideo = 1;

				vp.mediaTime = mediaPtsTimeS;  Ctx->gd.mediaSrcFrameId = Ctx->gd.mediaSrcFrameIdDrawn = -1;

				Eqv->MatRec->drBeginPlay();

				if (vp.mediaListId <= 0)
				{
					vp.renderTime = 0;

					

					startVideoProcessing();
				}
				if (mmd) mmd->updating = true;
#if HAS_MMD && AR_PATH_ON_STEP
				if (mmd) mmd->resetAni();
#endif
		}
	}
	}
	break;
	case 1291010:
	{
		//startDecVideo(false, true);
	}
	break;
	case 1291090:
	{
#if !DRONE_AR
		if (ce.pm1) {
			std::string sJson;
			if (0x04034b50 == *(uint32_t*)ce.unU64) //"PK\3\4"
			{
				if (Ctx->isApp(APPID_WinTest)) ualib::binarySaveToFile("out/arjszip.zip", (void*)ce.unU64, ce.pm1);
				auto fs = Ctx->getFileSystem();
				auto rfz = fs->createMemoryReadFile((void*)ce.unU64, ce.pm1, "ardatcamzip/ardat.zip");
				io::IFileArchive* fa{};
				if (fs->addFileArchive(rfz, true, true, io::EFAT_ZIP, "", "", &fa))
				{
					auto rf = fa->createAndOpenFile("ardatcam.json");
					auto fsize = rf->getSize();
					char* buf = new char[fsize + 1]; rf->read(buf, fsize); buf[fsize] = 0;
					sJson = buf; delete[]buf;
					rf->drop();	fs->removeFileArchive(fa);
				}
				rfz->drop();
			}
			else sJson = (char*)ce.unU64;
			if (vp.cmArr.size() > 0)
				sJson = vp.cmJson;
			Eqv->loadMatRecData(sJson);
			if (vp.cmRoot) vp.cmRoot->setMatrix(Ctx->gd.CMCloudMat);
			arRoot->setScale(Eqv->MatRec->arRootScale);
#if IS_WINXXX
			if (Ctx->isApp(APPID_WinTest)) {
				ualib::stringSaveToFile(sJson, "out/arembed.json", false);
				for (auto& ad : Eqv->ancDatVec)
				{
					SceneManager->addCubeSceneNode(10, 0, -1, ad.pos * 1000, ad.rtt);
				}
			}
#endif
		}
		arRoot->onArDatLoaded(ce.pm1 > 0);

#endif
	}
	break;
	case 1291099:
	{
		DP(("VP END"));

		//vp.reset();
		vp.clearVFrames();
		vp.clearScrFrames();

	}
	break;
	case 1291101:  // video data onFrameData
	{
		if (!vp.hasVideo || vp.stage() != 1) {
			vp.clearVFrames();
			return false;
		}

		if (ce.unU64)
		{
			vp.curFFfd = *(ualib::FF_FrameData*)ce.unU64;
		}
		else
			vp.curFFfd.mediaId = 0;

		int id = vp.curFFfd.mediaId;

		vp.fr[id] = vp.curFFfd;


		{
			auto& fr = vp.fr[id];
			auto& fv = vp.fv[id];


			//fr.colorFormat = 21;//yuv420sddp
			void* uv = fr.pb1;

			if (fr.colorFormat != 19 && fr.colorFormat != 21)
				return false;
			//auto DrvOfs = Ctx->getDriverOnThread();
			UP_LOCK_GUARD(DrvOfs->dsd.driverLock);
			//DP(("yuv lock +"));
			if (fv.texY && fv.texY->getSize() != dimension2du(fr.w, fr.h)) {
				DrvOfs->freeTexture(fv.texY); fv.texY = nullptr;
				DrvOfs->freeTexture(fv.texUV); fv.texUV = nullptr;
			}
			bool c21 = fr.colorFormat == 21;
			//DP(("texy %p",fv.texY));
			if (!fv.texY) {
				IImage* imageY = DrvOfs->createImageFromData(ECF_R8, core::dimension2du(fr.w, fr.h),
					(void*)fr.pb, true, false);
				if (fr.len)	imageY->setPitch(fr.len);
				fv.texY = DrvOfs->addTexture("texY", imageY);
				IImage* imageUV = DrvOfs->createImageFromData(c21 ? ECF_R8G8 : ECF_R8,
					core::dimension2du(fr.w / 2, c21 ? fr.h / 2 : fr.h), uv, true, false);
				fv.texUV = DrvOfs->addTexture("texUV", imageUV);
				imageY->drop();	imageUV->drop();
			}
			else {
				void* pbY = fv.texY->lock(ETLM_WRITE_ONLY); memcpy(pbY, fr.pb, fr.w * fr.h); fv.texY->unlock();
				void* pbUV = fv.texUV->lock(ETLM_WRITE_ONLY); memcpy(pbUV, uv, (fr.w / 2) * (c21 ? fr.h / 2 : fr.h) * (c21 ? 2 : 1)); fv.texUV->unlock();
			}
			if (vp.mtYuv2rgb == irr::video::EMT_NONE)
				vp.mtYuv2rgb = Ctx->getVkDrv(1)->getMT(fr.colorFormat == 21 ? Fx2DIdEnum::Fx2D_Yuv420spCvt : Fx2DIdEnum::Fx2D_Yuv420pCvt);


			if (vp.mediaListId >= 0)
			{
				if (!fv.texVideo)
				{
					fv.texVideo = (DrvOfs)->addTexture({ (u32)fr.w, (u32)fr.h }, "<TsfSrcDbg>videoTex");
					fv.texVideoRT = DrvOfs->addRenderTargetTexture(fv.texVideo->getSize());
				}
				if (vp.mFF->getNeedVideo() && fv.texY && fv.texUV)
				{
					DrvOfs->setRenderTarget(fv.texVideoRT, true, true, 0);
					video::SMaterial mr;
					mr.MaterialType = vp.mtYuv2rgb;// VkDrv->getMT(vp.colorFormat == 21 ? Fx2DIdEnum::Fx2D_Yuv420spCvt : Fx2DIdEnum::Fx2D_Yuv420pCvt);;
					mr.setTexture(0, fv.texY);
					mr.setTexture(1, fv.texUV);
					SColor col[4] = { 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF };
					DrvOfs->draw2DImageMr(mr, fv.texVideoRT->getRectI(), recti(0, 0, fr.w, fr.h), nullptr, col);
					DrvOfs->setRenderTarget(0, true, true);

					fv.texVideoRT->copyTo(fv.texVideo);
					if (id == 0) vp.texVideoBk[0] = fv.texVideo;

				}
			}

			if (id == 0 && (Ctx->gd.mediaSrcFrameId > -2)) {
				Ctx->gd.mediaSrcFrameId = ce.pm2i32;;
				Ctx->gd.mediaSrcTimeUs = ce.pm1i32;
				if (vp.mediaListId >= 0)
				{
					vp.doToFreeVFrames();
					auto texbk = vp.texVideoBk[0];
					if (texbk)
					{
						auto texclone = texbk->Clone(io::path("vfclone") + ualib::strFmt("%p", texbk).c_str());
						VideoProcessor::VPFrameTexture vpf{};
						vpf.tex = texclone;
						vpf.timeS = float(Ctx->gd.mediaSrcTimeUs / 1000000.0);
						vpf.vfIdx = Ctx->gd.mediaSrcFrameId;
						if (vp.saveVFIntval > 0 && vpf.vfIdx % vp.saveVFIntval == 0) saveVFrame(vpf.tex, vpf.vfIdx);
						UP_LOCK_GUARD(vp.vfLock);
						vp.vFrames.push_back(vpf);
						vp.switching = false;
						//DP(("add vframes Tex %p F:%2d T:%.3f", vpf.tex, vp.vFrames.size(), vpf.timeS));
					}
				}
			}
			//DP(("yuv lock -"));
		}
	}
	break;
	case 1501001: {	//start dec
		inVFilePtr = (uint8_t*)ce.pm1;
		inVFileSize = (size_t)ce.pm2;

		startDecVideo(1, 0);
	} break;
	case 1501002: {	//start dec
		inVFilePtr = (uint8_t*)ce.pm1;
		inVFileSize = (size_t)ce.pm2;

		startDecVideo(2, 0);
	} break;
	case 1501009: {	//start dec
		Eqv->clearFws();
		inVFilePtr = 0;
		inVFileSize = 0;

		startDecVideo(1, 0);
		//editMode=2;
		//startDecVideo();
	} break;
	case 1501101: {	//start dec
		stopRecord();

	} break;
	case 1502000:	vp.paused = ce.pm1 ? 0 : 1;
		if (!vp.working)
			startDecVideo(1, 0.f);
		break;
	case 1502010:	mediaSeekToPercent(ce.pm1 / 10000.0);	break;//set media pos
	case 1502500:	return (int)((editMode == 2 ? vp.recTimeS : vp.mediaTime) * 1000 + 0.5f);	//get time
	case 1502501:	return (int)(vp.durationMs);			//get duration

	case 1505001: {
		if (vp.stage() == 1)
			saveCam(SceneManager->getActiveCamera());
	}break;
	case 1505010: {
		drawWaterMarkCC = 0;
	}break;

}
	return 0;
}


bool AppMainAMP::startDecVideo(int edMode, float startFromS)
{

	//Eqv->clearFws();
	//vp init
	vp.Ctx = Ctx;

	if (vp.working)
	{
		stopRecord();

		ualib::SleepMs(100);
	}

	vp.reset();
	if ((inVFilePtr == nullptr || inVFileSize == 0) && curInVideo.size() < 2) {
		arRoot->onMediaStarted(0);
		return false;
	}

	Ctx->gd.EqvUseFFAudio = true;

	InputMediaInfo imi;
	imi.pb = inVFilePtr;
	imi.size = inVFileSize;
	imi.filePath = curInVideo;//Ctx->getDataFilePath( "outTemp.mp4");
	imi.startS = 0;
	imi.startSeekS = startFromS;
	imi.endS = 999;
	vp.mediaListId = 0; vp.mediaListCount = 1;
	if (edMode > 0) editMode = edMode;
	//Ctx->setFixFrameTime(editMode == 2 || IS_WIN, 1.0 / APP_FPS, APP_FPS);
	mediaStartHandled = false;
	vp.mFF->startDecodeFile(imi, "", 1, curInSubtitle.c_str());

	//if (snGrid) snGrid->setVisible(arRoot->Cs.showGroundPlane);
	return true;
}


void AppMainAMP::stopRecord()
{

	vp.stopVideo();

	switch (vp.recMode) {
	case 2:
	{


	}
		break;
	case 0:
	case 10:
	{
		jniCallbackMessage(2001001, editMode, 0, nullptr);
		if (snGrid) snGrid->setVisible(arRoot->Cs.showGroundPlane);
		arRoot->onMediaStopped();
		mmd->setOverrideTIme(-1, 0, 0);

//#if MMD_SAVE_VMD_MORPH || MMD_SAVE_VMD_MOTION
//		if (arRoot->curArSn->saba->vmdWriteFile)
//			arRoot->curArSn->saba->vmdWriteFile->SaveToFile(L"out/mmd.vmd");
//#endif
	}break;
	}
#if HAS_MIDI

	if (RECORD_MIDI)
		Ctx->Midi.recordEnd("r:/faMidiOut.mid", 127.f);
	if (FW_TO_MIDI)
		Ctx->MidiFw.recordEnd("r:/fwMidiOut.mid",127.f);
	Ctx->Midi.stopPlay();
	Ctx->MidiFw.stopPlay();
	if (Plates.size())	Plates[0]->recordEnd();		 
	
#endif
#if MMD_VIRTUAL_SINGER
	writeTimelineToFile("r:/timeline");
	mmd->mdplr.recordEnd();
#endif
#if BEAT_TO_JSON
	if (!BEAT_TO_JSON_first) {
		b2j_jss.SaveFile();
	}
#endif
	if (Ctx->audioMan && (PLAY_AUDIO || customAudioLoaded)) Ctx->audioMan->stopPlay();
}

void AppMainAMP::startVideoProcessing()
{
	vp.working = 1;
	switch (editMode)
	{
	case 1://playback
		break;
	case 2://convert
	{
		startRecord(vp.ivpm.w>0?10:9);
	}
	break;
	}
}

void AppMainAMP::nextVmd()
{
	if (mmd->sabas.size() == 0) return;
	static size_t id1 = 0, id2 = 0, id3 = 0;
	static  const char* vmdList1[][2] = {
		{"mmd/vmd/standS.vmd",""},{"mmd/vmd/wrt.vmd",""},{"mmd/vmd/sitzz.vmd",""},{"mmd/vmd/fcs.vmd",""},{"mmd/vmd/shakeIt.vmd","mmd/vmd/shakeItCam.vmd"},{"mmd/vmd/laym2.vmd",""},//{"mmd/vmd/sitGuqin.vmd",""},
		{"mmd/vmd/runAR.vmd",""},{"mmd/vmd/balei.vmd",""},{"mmd/vmd/dance.vmd",""},
		{"mmd/vmd/moon.vmd"},{"mmd/vmd/jl.vmd"},
		{"mmd/vmd/motion fix model Miku.vmd"}
	};
	static  const char* vmdList2[][3] = {
		{"mmd/vmd/f2a.vmd","mmd/vmd/f2b.vmd","mmd/vmd/f2cam1.vmd"},
		{"mmd/vmd/DTBd1.vmd","mmd/vmd/DTBd2.vmd","mmd/vmd/DTBcam.vmd"},//motion after this losk foot IK?
		{"mmd/vmd/yao1.vmd","mmd/vmd/yao2.vmd"},
		{"mmd/vmd/f2a.vmd","mmd/vmd/f2b.vmd"},
		{"mmd/vmd/laym3.vmd","mmd/vmd/laym2.vmd"},
	};
	static  const char* vmdList3[][4] = {
		{"mmd/vmd/f3c.vmd","mmd/vmd/f3b.vmd","mmd/vmd/f3a.vmd","mmd/vmd/f3cam.vmd"},
		{"mmd/vmd/tak/3k.vmd","mmd/vmd/tak/2k.vmd","mmd/vmd/tak/1k.vmd","mmd/vmd/tak/cam.vmd"},
		{"mmd/vmd/tak/3k.vmd","mmd/vmd/tak/2k.vmd","mmd/vmd/tak/1k.vmd","mmd/vmd/tak/cam1.vmd"},
	};

	if (mmdMax == 0)
	{
		mmd->sabas[0]->loadAnimation(vmdList1[id1][0], false);
		mmd->sabas[0]->showGuqin(vmdList1[id1][0] == "mmd/vmd/sitzz.vmd");
		mmd->sabas[0]->loadCamera(vmdList1[id1][1], true);
		size_t sc = (sizeof(vmdList1) / sizeof(vmdList1[0]));
		id1 = (id1 + 1) % sc;
	}
	else if (mmdMax == 1)
	{
		mmd->sabas[0]->showGuqin(false);

		mmd->sabas[0]->loadAnimation(vmdList2[id2][0], false);
		mmd->sabas[1]->loadAnimation(vmdList2[id2][1], false);

		mmd->sabas[0]->loadCamera(vmdList2[id2][2], true);

		size_t sc = (sizeof(vmdList2) / sizeof(vmdList2[0]));
		id2 = (id2 + 1) % sc;
	}
	else if (mmdMax == 2)
	{
		mmd->sabas[0]->loadAnimation(vmdList3[id3][0], false);
		mmd->sabas[1]->loadAnimation(vmdList3[id3][1], false);
		mmd->sabas[2]->loadAnimation(vmdList3[id3][2], false);

		mmd->sabas[0]->loadCamera(vmdList3[id3][3], true);

		id3 = (id3 + 1) % (sizeof(vmdList3) / sizeof(vmdList3[0]));

	}

}

void AppMainAMP::nextModel()
{
	static size_t id1 = 0, id2 = 0, id3 = 0;
	static std::vector<const char*> pmxList1 = {
     "gs_NilouIK.zip"
	//"mmd/pmx/miku/gy.pmx","mmd/pmx/shenhe/1.pmx","mmd/pmx/Miniganyu/ganu.pmx","mmd/pmx/angela/1.pmx","mmd/pmx/miku/miku.pmx","mmd/pmx/t2/1.pmx",

	};


/*	if (mmdMax < 2)
	{
		mmd->setPmxFile(pmxList1[id1]);
		id1 = (id1 + 1) % pmxList1.size();
	}*/
	loadSabaModel({});

}




void AppMainAMP::InitFwPPT()
{
	if (g.FwInited)
		return;
	mEqvDat.maxParticles = FW_MAX_PARTICLE;
	try
	{
		if (!IFppt)
		{
			Eqv->ft.supportFW = EQVisual::CMrCsParticle::InitMr(L"GsParticleEx", mDriver);

		}
		if (Eqv->ft.supportFW)
		{
			//if (IFppt)
			//{
			//	IFppt->remove();
			//	IFppt = nullptr;
			//}

			//mEqvDat.recreateOnUpdate();

			{
				Eqv->ft.supportFW = false;
				{
					DP(("PPT+"));
					InitPPT(1);
					DP(("PPT-"));
					Eqv->ft.supportFW = true;

					//IFppt->mRenderer->SetFxId(0, 0);
					
					g.FwInited = true;
				}
 
			}
		}
	}
	catch (std::exception& e)
	{
		DP(("Catch EXCEPTION %s", e.what()));


		Eqv->ft.supportFW = false;
		mEqvDat.mEqvLoadingFailed = true;
	}
	//	catch (...)
	//	{
	//		//LibMainPtr()->AppShowMsg(L"EQV(Fw) Loading Failed.", 2);
	//		Eqv->ft.supportFW = false;
	//		mEqvDat.mEqvLoadingFailed = true;
	//#ifdef _DEBUG
	//		throw;
	//#endif // _DEBUG
	//	}

}
void AppMainAMP::InitPPT(int kld)
{
	//return;

	if (kld > 36) kld = 36;
	
	//size_t dsm = Ctx->gd.gpuMemSize;
	//if (dsm > 1024)						
	if (!IFppt)
	{
		FwParticleParam pm;
		pm.maxKaleido = kld;
		pm.drawReflection = FW_DRAW_WATER;
		pm.isShaderKaleido = kld > 1;
		pm.isShaderUpdateHasLand = (FW_HAS_LAND && kld <= 1);// && !USE_AR_DATA;
		IFppt = new EQVisual::SnCsParticle(
 
			arRoot, SceneManager, -1, mEqvDat.maxParticles, nullptr, pm);

		//if (FW_DRAW_WATER)		IFppt->passTypeFlags &= ~(IrrPassType_Mirror);
		IFppt->setOrderForTransparent(-100);
		IFppt->drop();
		//IFppt->setScale(MMD_SABA_SCALE/100.f);
		// FW TO SOUND
		IFppt->cbOnFwSound = [this](int sid, float count) {
#if HAS_MIDI
			//static int cc = 0; cc++;if (cc % 6) return;
			int ch = 0;
			uint32_t key = sid;
			if (sid > 127) {
				ch = sid - 128 + 10;
				const auto& ecd = Eqv->getCurPtrFw()->ecd;
				key = int(ecd.mncd[ch].key + ualib::UaRandm1to1() * ecd.mncd[ch].keyRandAdd + 0.5f);
			} 
			//DP(("fwm ch %d key %d count %f",ch,key,count));
			Ctx->MidiFw.playNote(Driver->dsd.time, ch, core::s32_clamp(key, 0, 127), 1 - exp(-count));
#endif
		};


	}
	IFppt->SetFwMrCb(FwMan);

	//if (mNeedUpdateSplitImg)
	//{
	//	mNeedUpdateSplitImg = false;
	//	FwCreateImage(L"");
	//}

	Ctx->eqv.ifPPt = IFppt;




#if APP_HAS_CLOCK
	if (fwClk) fwClk->setPpt(IFppt, Eqv);
#endif

#if FW_DRAW_WATER
	if (!snWater) {
		WaterParam pianopm;
		pianopm.ppt = IFppt;
		pianopm.eqv = Eqv;
		pianopm.ctx = Ctx;
		snWater = new SnWater(Ctx->gd.RootSn, SceneManager, -1, pianopm);
		float waterP1 = 2000, waterP2 = 1000;
		snWater->setPm(waterP1, waterP2);
	}
#endif
}

void AppMainAMP::UpdateEqvData(const EQVisual::PlayerEQVParam& evd) {
	if (!Eqv) return;
	//DP(("EQV %f",evd.bvd->val[0]));
	EqvUpdateParam pms;
	pms.eqv = &evd;

	if (snWater)
	{
		auto* cbcp = snWater->getMaterial0CbCp();
		for (int i = 0; i < 8; i++)
		{
			float ratio = i / 8.0f;//
			cbcp->fvecs[i] = { cos(ratio * core::PIx2),sin(ratio * core::PIx2),  evd.bvd->val[i] / 10 + 10, 10000.f / powf(2.f,i + 1.f) };
		}
	}
	if(0)if (mmd->fluidNode) {
		static float lastv[32] = {};
		for (int i = 0; i < evd.bvd->count; i++) {
			float dv = lastv[i] - evd.bvd->val[i];
		vec3 pos =vec3( - 16 + i*2,3,6);
		vec3 dir(0, std::min(100.f,evd.bvd->val[i]*1+1+ std::max(0.f,dv)*10), -1);
		lastv[i] = evd.bvd->val[i];
		addFluidParticlesGrid(1, 5, 1, .5, .5, .5, pos, dir * 10.f);
		}
	}
#if !EQV_TEST_PTC
	Eqv->onUpdateEqvData(pms);
#endif 
	arRoot->forEachArChild([=](SnArItem* sn) {
		if (sn->sb && sn->sb->rcvEqvData) {
			sn->sb->UpdateEqvData(pms);
		}
		},0x1);
 

}

void AppMainAMP::startRecord(int recMode) {
	//if (snGrid) snGrid->setVisible(false);
	//DP(("RECVID 10"));
	auto scrSize = Driver->getScreenSize();

	VideoParam pm;
	pm.w = scrSize.Width;
	pm.h = scrSize.Height;
	pm.fps = APP_FPS;
	pm.file = IS_WIN ? "R:/" WIN_OUTPUT_FILE : Ctx->getDataFilePath(WIN_OUTPUT_FILE);
	pm.timeDiv = Eqv->timeDiv;
	vp.useFFEncoder = USE_FF_ENCODER || arRoot->Cs.outputAlphaVideo;
	vp.encodeHDR = mDriver->dsd.initHDR && HDR_VIDEO_OUTPUT;
	pm.inputHdr = Driver->dsd.initHDR;
	if (vp.mis.vdResP > 0)
	{
		if (pm.w >= pm.h)
		{
			pm.h = vp.mis.vdResP;
			pm.w = pm.h * vp.fr[0].w / vp.fr[0].h / 4 * 4;
		}
		else
		{
			pm.w = vp.mis.vdResP;
			pm.h = pm.w * vp.fr[0].h / vp.fr[0].w / 4 * 4;
		}
	}
	if (pm.w * pm.h < 1)
	{
		pm.w = scrSize.Width;
		pm.h = scrSize.Height;
	}
	pm.h264 = Ctx->vrOn;
	DP(("CvtVideo Out %d x %d", pm.w, pm.h));
	vp.working = 1;
	vp.recMode = recMode;
	switch (recMode) {
	case 2: {
		if (ualib::removeFilesInDir("r:/img01") == 0)	std::filesystem::create_directories(std::filesystem::path("r:/img01"));
		if (ualib::removeFilesInDir("r:/imgdp") == 0)	std::filesystem::create_directories(std::filesystem::path("r:/imgdp"));
		vp.writeFrameToFile = SAVE_OTHER_IMAGES;
		vp.startRecordDynImg(pm);
	}break;
	case 9: {
		vp.startRecordScreen(pm);
	}break;
	case 10: {
		
		vp.startRecordCvt(pm);
	}break;
	}

#if HAS_MIDI
	if (RECORD_MIDI) {
		Ctx->Midi.recordBegin();
		if (Plates.size())	Plates[0]->recordBegin();
		//for (int i = 10; i < 15; i++) Ctx->Midi.setFwNoteInst(i, 112);
	}
	if (FW_TO_MIDI ) {
		  Ctx->MidiFw.recordBegin();
		  Ctx->MidiFw.setFwNoteInst(0, 127); Ctx->MidiFw.setFwNoteInst(10, 127); 
	}
	if (GAMESCENE_PLATE) {
		FRAMEWAITER_CALL_B(1,name0) {
			sendKeyEvent(KEY_KEY_C); //recreate plates
		});
	}
#endif
#ifdef LOAD_EQ_BANDLIST
	AppBase* base = (AppBase*)Ctx->getLib();
	base->loadEqBandList(LOAD_EQ_BANDLIST);
#endif
}

void AppMainAMP::updateArPath()
{
	// Front FW grass / rings
	if (!arRoot || !arRoot->Cs.showArPath || !Eqv->MatRec->getCount() > 0) return;
	//if (editMode != 2) return;
	irr::core::vector3df vel;
	MatrixRecorder::DrDataStruct ds;
	int mrid = 0;
	static int cc = 0; cc++;
	float ofstime = 1.5f;
 
	static int lastMrId = -1;
	if (vp.curFrameIdx % 60 == 0)
	{
 
		const MatRecData* a, * b; float ratio;
		int rid = Eqv->MatRec->getDataPtrUs((double(vp.curFrameIdx) / Eqv->MatRec->fps + 5.0) * 1000000, a, b, ratio);

		core::matrix4 mt = a->mat;
		mt = mt.interpolateTR(b->mat, ratio);
		auto dir = Eqv->MatRec->dirAt(rid);
		//DP(("LLLLLLLL %d  %d %f", vp.renderId, rid ,ratio));
		core::matrix4 m;
#if 0
		m.buildRotateFromTo({ 0,0,1 }, dir);
		auto pos = mt.getTranslation();
		m.setTranslation(pos);
		//if (vp.renderId % 120 == -10 + 60 * ofstime)
		Eqv->LaunchFw3D(pos, Eqv->getFwIdxByFwIdStr(1, "airPrintFW"), { 0,0,0 }, SColorf(0xCFFF2020));
#else
		m = mt;
#endif
		
		int fw = Eqv->getFwIdxByFwIdStr(2, "ringCloud");

#if 1
		{
			vector3df v1(-50, -50, 0), v2(-50, 50, 0), vpos;
			m.transformVect(v1); m.transformVect(v2);
			int maxc = 100;
			for (int j = 0; j <= maxc; j++) {
				float fr = float(j) / maxc;
				vpos = v1.getInterpolated(v2, fr);
				float3 tgt = vpos;
				LfwParam pm;
				Eqv->LaunchFw3D(tgt, fw, { 0,0,0 }, SColorf(1, 1, 1, 1), &pm);
			}
		}
		{
			vector3df v1(50, -50, 0), v2(50, 50, 0), vpos;
			m.transformVect(v1); m.transformVect(v2);
			int maxc = 100;
			for (int j = 0; j <= maxc; j++) {
				float fr = float(j) / maxc;
				vpos = v1.getInterpolated(v2, fr);
				float3 tgt = vpos;
				LfwParam pm;
				Eqv->LaunchFw3D(tgt, fw, { 0,0,0 }, SColorf(1, 1, 1, 1), &pm);
			}
		}
		
#else
		{
			for (int j = 0; j < 360; j += 1)
			{
				vector3df v1(60 * cos(j * core::DEGTORAD), 60 * sin(j * core::DEGTORAD), 0);
				//v1 += mdOfs;
				m.transformVect(v1);
				float3 tgt = v1;
				LfwParam pm;
				Eqv->LaunchFw3D(tgt, fw, { 0,0,0 }, SColorf(1, 1, 1, 1), &pm);
			}
		}
#endif
	}


}

void AppMainAMP::LaunchOneFW(float hueadd, int mid, const irr::core::vector3df* pos, irr::core::vector3df* vel, int fwIdOfs)
{
	static float hue = 0.0f; hue += hueadd;
	SColorHSL hsl(hue, 50, 50);
	SColor sc = hsl.toSColor();
	irr::core::vector3df position, velocity(0, 1000, 0);
	if (pos) position = *pos;

	if (vel) velocity = *vel;
	LfwParam pm;
	pm.mid = mid;
	int fid = Eqv->getCurPtrFw(1)->gfd.FwId + fwIdOfs;
	if (Eqv->getCurPtrFw(1))
		Eqv->LaunchFw3D(V3dToFloat3(position), fid, V3dToFloat3(velocity), sc, &pm);
}

void AppMainAMP::setCamSb(irr::scene::IrrSaba* sb, bool forceSet) 
{ 
	if ((forceSet ||  !lockOnSb) //|| (!lockOnSb->isAiCharacter() || sb->isAiCharacter())
		)  {
		if (lockOnSb) lockOnSb->drop(); lockOnSb = sb; lockOnSb->grab();
		mmd->camSb = sb;
		mmd->camSbTgtNd = sb0->ndUpper2;
	}	
}

void AppMainAMP::saveCam(irr::scene::ICameraSceneNode* cam) {
	if (!camSave)
		camSave = SceneManager->addCameraSceneNode(0, { 0,0,0 }, { 0,0,0 }, -1, false);
	if (cam)
		cam->copyDataTo(camSave);
	Eqv->svgCam = camSave;
}

void AppMainAMP::saveCameraVmd()
{
#if MMD_SAVE_VMD
	stopRecord();
	Eqv->MatRec->drBeginPlay();
	curSaba()->recordCamBegin();
	int idx = 0, retIdx = -1;
	do {
		retIdx = Eqv->ARCamUpdate(idx, 0, idx   / 60.f, true);
		curSaba()->recordCamFrame(idx * 30.f / Eqv->MatRec->fps + 0.001f);
		idx++;
		DP(("record cam %d %d", idx, retIdx));
	} while (retIdx < Eqv->MatRec->getCount() - 1);
	curSaba()->recordCamEnd();
	Eqv->MatRec->drEndPlay();
#endif
}

void AppMainAMP::translateInActiveCamView(irr::core::vector3df vec)
{
	using namespace glm; 
	vec3 ofs = vec;
	auto p = curChar()->Pmx;
	mat4 mCv = SceneManager->getActiveCamera()->getViewMatrix(), sbi = curChar()->mmdBaseInv;
	float sc = 10;
#if 0
	vec4 v(0,0,0,1);	
	v= mCv*v; v += vec4(ofs,0);
	v = glm::inverse(mCv) * v;
	v= sbi*v;
	ofs = glm::quat(arRoot->curArSn->saba->Pmx->rootM0i) * glm::vec3(v);
	ofs = ofs  ;
	p->rt1Tr += ofs;
#else
	vec4 v(p->rt1Tr, 1);
	v = mCv*mat4(curChar()->mmdBaseMat) * p->rootM0 * v;
	v += vec4(ofs*sc, 0);
	v = p->rootM0i*sbi *glm::inverse(mCv) * v;
	p->rt1Tr = v;
#endif
}

void AppMainAMP::camTimelineStart(std::string file, int tlNum, bool xxx)
{
	auto anm =  Ctx->getCamTCAnimator();

	//reset in setTlNum anm->aniTl_Reset(/*Ctx->gd.scrHeight / 1024.0*/);
	anm->onStageChanged = [this](int stage) {
		if (stage == 1) {
			//if (mmd) mmd->sabas[pmd.id]->saveCam(SceneManager->getActiveCamera());
		}
		else if (stage == 2) {
			if (vp.working)
				stopRecord();
		}
		};
	typedef AnimationTimeLine AT;

	Eqv->CTL.loadTimeLines(anm, file);
	
	 

	//if (returnToStart) {
	//	auto s = anm->getCurStateTxt(1, 1);
	//	UaJsonSetting jss; jss.LoadJsonString("[\n" + s + "\n]");
	//	auto addTl = &jss.getRootValue();

	//	Eqv->CTL.setTlNum(tlNum, addTl);
	//}
	//else 
	Eqv->CTL.setTlNum(tlNum, nullptr);

	//anm->aniTl_Add(0, { 0.0,3.6,180.0,0.0,		1,	AT::easeInOutSine });
	//anm->aniTl_Add(1, { 0.0,3.6,3.0,60.0,		1,	AT::easeInOutSine });
	////anm->aniTl_Add(2, { 0.0,1.0,1.0,1,		1,	AT::easeInOutBack });
	//anm->aniTl_Add(2, {0,3.6,1.1,0.86 ,		1,	AT::easeInOutSine });
	


	anm->aniTl_start();
}


 


void AppMainAMP::loadVmd(int inc, bool cyc)
{	 

	static int vmdcc = -1;
	vmdcc += inc;
	auto path = VMD_FILE[vmdcc % (sizeof(VMD_FILE) / sizeof(VMD_FILE[0]))];
	//if (SEESAW) path = "D:/mmd/vmd/buildwall.vmd";
	auto it = arRoot->curAiSn();
	if (!mmd->syncMovNode) {
		it->sb->clearAnimation();
		bool br = it->loadMotion(ualib::AnsiToWchar(path).c_str());
		if (!br) return;
		static int lc = 0;
		if (!lc) { lc = 1;  sb0->loadCamera(MMD_CAM_FILE, false); sb0->Pm.activeNodeCam = true; }
		it->sb->setPlaying(true);
		it->sb->animCycleStart = cyc ? 0 : -1;
	}
	else for (auto sb:mmd->sabas)
	{
		auto oldPos = sb->ndRoot->GetAnimationTranslate();
		if (sb->loadMotion(path)) {			
			sb->setPlaying(true);
			sb->animCycleStart = cyc ? 0 : -1;
			sb->ndRoot->SetAnimationTranslate(oldPos);
		}
	}
}

void AppMainAMP::newCombineDance(irr::scene::IrrSaba* sb)
{
#if 1
	//sendKeyEvent(KEY_KEY_M, 1, 0, 0, 0);
	//sendKeyEvent(KEY_KEY_M, 1, 0, 0, 0);
	//sb1->disableAnim(2, 1);
	arRoot->setCurSn(it0); 
	sb0->setPhyAnim(0, false, true);
	mmd->createMrk();

#else
	if (sb) lastCntSb = sb;
	sendKeyEvent(KEY_KEY_M, 1, 0, 1, 0);
 
	mmd->sbCatcher = sb ? sb : lastCntSb ? lastCntSb : sb0;
	mmd->sbCatcher->cat.catchStart(curChar());
#endif

}


void AppMainAMP::addFluidParticlesGrid(int nx, int ny, int nz, float spacingX, float spacingY, float spacingZ, const glm::vec3 centerPosition, const glm::vec3 initialVelocity,const glm::vec3 rtt) {
	if (!mmd || !mmd->fluidNode)
		return;
	if (spacingX <= 0 || spacingY <= 0 || spacingZ <= 0 || nx <= 0 || ny <= 0 || nz <= 0) {
		spacingX = spacingY = spacingZ = mmd->fluidNode->particleSpacing;
	}
	// Calculate the total number of particles
	int totalParticles = nx * ny * nz;

	// Create arrays for positions and velocities
	std::vector<float3> positions(totalParticles);
	std::vector<float3> velocities(totalParticles);

	// Calculate offsets to center the grid around the specified position
	float offsetX = (nx - 1) * spacingX / 2.0f;
	float offsetY = (ny - 1) * spacingY / 2.0f;
	float offsetZ = (nz - 1) * spacingZ / 2.0f;
	quat rttQuat = rtt;
	vec3 center = vec3(centerPosition.x, centerPosition.y, centerPosition.z); // Invert Z for OpenGL compatibility
	// Initialize positions and velocities
	int index = 0;
	for (int x = 0; x < nx; x++) {
		for (int y = 0; y < ny; y++) {
			for (int z = 0; z < nz; z++) 
			 
			{
				// Calculate position for this grid cell
				auto ofs = vec3(
					(x * spacingX) - offsetX,
					(y * spacingY) - offsetY,
					(z * spacingZ) - offsetZ
				);
				if (glm::length(ofs) > (nx)* spacingX/2  ) continue;
				positions[index] = center+ rttQuat*ofs;
				positions[index].z = -positions[index].z;
				// Set velocity
				velocities[index] = vec3(initialVelocity.x, initialVelocity.y,-initialVelocity.z);

				index++;
			}
		}
	}

	// Add particles to the fluid simulation
	mmd->fluidNode->setParticles(totalParticles, positions.data(), velocities.data());
}
