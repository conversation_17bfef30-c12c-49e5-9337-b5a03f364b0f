#pragma once

#include "IrrCompileConfig.h"
#ifdef _IRR_COMPILE_WITH_VULKAN_

#if VK_ENABLE_RAYTRACING
#include "irrlicht.h"
#include "VkHeader.h"
#include "VkRaytracingPipeline.h"
#include "VkRaytracingShaders.h"

namespace irr {
namespace video {

// Forward declarations
class VkDriver;
class VkTexture;
class VkBottomLevelAS;
class VkTopLevelAS;

//! Simple raytracing demo manager
//! This connects our compiled shaders with the VkRaytracingPipeline system
//! and provides a basic raytracing implementation for testing
class VkRaytracingDemo {
private:
    VkDriver* driver;
    VkRaytracingPipeline* pipeline;
    VkRaytracingShaders* shaders;
    
    // Output texture for raytracing results
    VkTexture* outputTexture;
    
    // Sky texture for environment mapping
    VkTexture* skyTexture;
    
    // Descriptor sets for raytracing
    VkDescriptorSetLayout descriptorSetLayout;  // Set 0: Raytracing globals
    VkDescriptorSetLayout geometryDescriptorSetLayout;  // Set 1: Geometry data
    VkDescriptorPool descriptorPool;
    VkDescriptorSet descriptorSet;  // Set 0 descriptor set
    VkDescriptorSet geometryDescriptorSet;  // Set 1 descriptor set
    
    // Simple test geometry (triangle)
    VkBottomLevelAS* testBLAS;
    VkTopLevelAS* testTLAS;
    
    // Geometry buffers for descriptor set updates
    VkHardwareBuffer* testVertexBuffer;
    VkHardwareBuffer* testIndexBuffer;
    VkHardwareBuffer* testMaterialIDsBuffer;
    VkHardwareBuffer* testMaterialsBuffer;
    
    VkSampler m_defaultSampler = VK_NULL_HANDLE;
    bool isInitialized;
    
public:
    VkRaytracingDemo(VkDriver* driver);
    ~VkRaytracingDemo();
    
    //! Initialize the raytracing demo
    bool initialize();
    
    //! Render a frame using raytracing
    bool renderFrame(u32 width, u32 height);
    
    //! Test raytracing with a simple triangle
    bool testRaytracing();
    
    //! Get the output texture for display
    VkTexture* getOutputTexture() const { return outputTexture; }
    
    //! Check if demo is ready
    bool isReady() const { return isInitialized && pipeline && pipeline->isValidPipeline(); }
    
    //! Shutdown and cleanup
    void shutdown();
    
private:
    //! Create the raytracing pipeline with our compiled shaders
    bool createRaytracingPipeline();
    
    //! Create descriptor sets for raytracing
    bool createDescriptorSets();
    
    //! Create simple test geometry
    bool createTestGeometry();
    
    //! Create output texture for raytracing results
    bool createOutputTexture(u32 width, u32 height);
    
    //! Create sky texture for environment mapping
    bool createSkyTexture();
    
    //! Update descriptor sets with current resources
    void updateDescriptorSets();
};

} // namespace video
} // namespace irr

#endif // VK_ENABLE_RAYTRACING
#endif // _IRR_COMPILE_WITH_VULKAN_ 