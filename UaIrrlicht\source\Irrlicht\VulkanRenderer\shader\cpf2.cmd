@echo off
rem THIS CODE AND INFORMATION IS PROVIDED "AS IS" WITHOUT WARRANTY OF
rem ANY KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO
rem THE IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
rem PARTICULAR PURPOSE.
rem
rem Copyright (c) Microsoft Corporation. All rights reserved.

setlocal

set fxDebug=1
set error=0

set FXCOPTS= -g -V

if %fxDebug% == 1 (
	set FXCOPTS=  -g -V 
)

set PCFXC=glslangValidator

:continue


call :CompileFX FFCode2DColor vert 
call :CompileFX FFCode2DVertexAlpha frag 
call :CompileFX FFCode2DVertexColorDuDv frag 
call :CompileFX FFCode2DVertexColor2a frag 
 call :CompileFX FFCode2D_BlurV frag 
 call :CompileFX FFCode2D_BlurH frag 
 call :CompileFX FFCode2D_Shadow frag 
call :CompileFXN FFCode2D_Blur frag FFCode2D_BlurHM "-DBLUR_COLOR_TO_BW_HMAP=1"
call :CompileFX FFCode2D_Blur frag "-DBLUR_COLOR_TO_BW_HMAP=0"
 call :CompileFX FFCode2D_NormMap frag 
 call :CompileFX FFCode2D_Gradient frag 
 call :CompileFX FFCode2DYuv420spCvt frag 
 call :CompileFX FFCode2DYuv420pCvt frag 
rem call :CompileFXN FFCode2DYuv420spCvt frag FFCode2DYuv420spCvt_RGB "-DRGBA_ORDER=1"
rem call :CompileFXN FFCode2DYuv420pCvt frag FFCode2DYuv420pCvt_RGB "-DRGBA_ORDER=1"
call :CompileFX FFCode2D_HDR frag 
call :CompileFX FFCode2D_ToneMapping frag 
call :CompileFX FFCode2D_CubeToSphere frag 
call :CompileFX FFCode2D_ProcessImage frag 

  call :CompileFX FfSsaoGBuf vert 
  call :CompileFX FfSsaoGBuf frag 
  call :CompileFXDbg FfSsaoFullscreen vert 
  call :CompileFXDbg FfSsao frag 
  call :CompileFXDbg FfSsaoBlur frag 
  call :CompileFXDbg FfSsaoComposition frag 
echo.

if %error% == 0 (
    echo Shaders 2D compiled OK ~~~~~~~~~~~~~~~~~~
) else (
    echo There were shader compilation errors!
    pause
)

endlocal
exit /b


:CompileFX
set fxc=%PCFXC% %FXCOPTS%   -o Compiled\%1_%2_SpirV_base.spv -S %2  %1.%2 -e main %3   --target-env vulkan1.3
echo.
echo %fxc%
%fxc% || set error=1
spirv-opt -O Compiled\%1_%2_SpirV_base.spv -o Compiled\%1_%2_SpirV.spv  
call spv2h.exe %1 %2 
exit /b

:CompileFXN
set fxc=%PCFXC% %FXCOPTS%  -o Compiled\%3_%2_SpirV_base.spv -S %2  %1.%2 -e main  %4   --target-env vulkan1.3
echo.
echo %fxc%
%fxc% || set error=1
spirv-opt -O Compiled\%3_%2_SpirV_base.spv -o Compiled\%3_%2_SpirV.spv  
call spv2h.exe %3 %2 
exit /b

:CompileFXDbg
set fxc=%PCFXC% %FXCOPTS%   -o Compiled\%1_%2_SpirV.spv -S %2  %1.%2 -e main  
echo.
echo %fxc%
%fxc% || set error=1
exit /b

:needxdk
echo ERROR: CompileShaders xbox requires the Microsoft Xbox One XDK
echo        (try re-running from the XDK Command Prompt)
pause