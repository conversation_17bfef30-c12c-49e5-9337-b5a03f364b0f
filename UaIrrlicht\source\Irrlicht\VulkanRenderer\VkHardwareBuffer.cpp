// Copyright (C) 2002-2009 <PERSON><PERSON>
// This file is part of the "Irrlicht Engine".
// For conditions of distribution and use, see copyright notice in irrlicht.h

#include "IrrCompileConfig.h"

#ifdef _IRR_COMPILE_WITH_VULKAN_

#define _IRR_DONT_DO_MEMORY_DEBUGGING_HERE

#include "VkDriver.h"
#include "VkHardwareBuffer.h"
#include "os.h"
#include "irrTypes.h"
#include "Helpers/UpUtils.h"
namespace irr
{
namespace video
{


VkHardwareBuffer::VkHardwareBuffer(VkDriver* driver, E_HARDWARE_BUFFER_TYPE type, 
							E_HARDWARE_BUFFER_ACCESS accessType, u32 size, u32 flags, const void* initialData)

	:
//, UAView(0)
 Driver(driver)
//, UseTempStagingBuffer(false)
, Size(size)
, Type(type)
, CrFlags(flags)
, AccessType(accessType)
{
	#ifdef _DEBUG
	setDebugName("VkHardwareBuffer");
	#endif
	Device = Driver->Device;
	
	if (!createInternalBuffer(initialData))
		throw 33283;

	// set need of staging buffer
	//if (AccessType == EHBA_DYNAMIC && AccessType == EHBA_IMMUTABLE)
	//	UseTempStagingBuffer = true;
}

VkHardwareBuffer::~VkHardwareBuffer()
{
	if (mCmdBuf != VK_NULL_HANDLE)
		freeCmdBufAndFence();
	mBuffer.freeBufferAndMemory();
	//vkDestroyBufferView(Device,SRView,nullptr);
	//SAFE_RELEASE(UAView);
	//hBuffer = VK_NULL_HANDLE;//vkDestroyBuffer(Device,Buffer,nullptr);
	mStagingBuffer.freeBufferAndMemory();


}
void VkHardwareBuffer::setCurrentCommandBuffer() {
	if (!mCmdBuf) {
		auto cmb = Driver->mDevice->createCommandBuffer(VK_COMMAND_BUFFER_LEVEL_PRIMARY,cmbPool, false);
		auto fence = Driver->mDevice->createFence(true);
		isExtCmdBuf = true;
		mCmdBuf = cmb; mFence = fence;
	}
}
void VkHardwareBuffer::freeCmdBufAndFence()
{
	Driver->mDevice->freeCommandBuffer(cmbPool,mCmdBuf);
	Driver->mDevice->freeFence(mFence);
}

bool VkHardwareBuffer::createInternalBuffer(const void* initialData)
{

	VkBufferUsageFlags  vUsage;
	VkMemoryPropertyFlags vMemFlag;


	vMemFlag = GetMemoryFlagsForUsage(AccessType);
	assert(vMemFlag == VK_MEMORY_PROPERTY_DEVICE_LOCAL_BIT);//only support default currently

	// Check bind flags
	switch (Type)
	{
	case EHBT_VERTEX:
	case EHBT_VERTEX_NO_INPUT:
		vUsage = VK_BUFFER_USAGE_VERTEX_BUFFER_BIT;
		break;

	case EHBT_INDEX:
		vUsage = VK_BUFFER_USAGE_INDEX_BUFFER_BIT;
		break;

	case EHBT_STREAM_OUTPUT:
#ifdef __ANDROID__
		throw;
#else
		vUsage = VK_BUFFER_USAGE_TRANSFORM_FEEDBACK_BUFFER_BIT_EXT;
#endif
		break;

	case EHBT_COMPUTE:
		vUsage = VK_BUFFER_USAGE_STORAGE_TEXEL_BUFFER_BIT; assert(0);		throw "not support yet";
		break;

	case EHBT_SHADER_RESOURCE:
		vUsage = VK_BUFFER_USAGE_UNIFORM_TEXEL_BUFFER_BIT;
		break;

	case EHBT_CONSTANTS:
		vUsage = VK_BUFFER_USAGE_UNIFORM_BUFFER_BIT;  mStagingCreateCount = 100;
		break;
	case EHBT_STORAGE:
		vUsage = VK_BUFFER_USAGE_STORAGE_BUFFER_BIT;
		break;
	case EHBT_STORAGE_INDIRECT:
		vUsage = VK_BUFFER_USAGE_STORAGE_BUFFER_BIT | VK_BUFFER_USAGE_INDIRECT_BUFFER_BIT;
		break;
	case EHBT_SYSTEM:

		break;
	}

	 
	mUsageFlags = vUsage |  (Type != EHBT_VERTEX_NO_INPUT? VK_BUFFER_USAGE_TRANSFER_DST_BIT : 0);
	if (AccessType == EHBA_DEFAULT_RW) mUsageFlags |= VK_BUFFER_USAGE_TRANSFER_SRC_BIT;


	// Only add device address bit to storage buffers when specifically requested
	// This avoids performance overhead for regular storage buffers
	if ( CrFlags & EHBF_DEVICE_ADDRESS) {
		mUsageFlags |= VK_BUFFER_USAGE_SHADER_DEVICE_ADDRESS_BIT;
	}
	
	// Add acceleration structure build input flag when requested
	if (CrFlags & EHBF_ACCEL_STRUCT_BUILD_INPUT) {
		mUsageFlags |= VK_BUFFER_USAGE_ACCELERATION_STRUCTURE_BUILD_INPUT_READ_ONLY_BIT_KHR;
	}
	
	// Add shader binding table flag when requested
	if (CrFlags & EHBF_SHADER_BINDING_TABLE) {
		mUsageFlags |= VK_BUFFER_USAGE_SHADER_BINDING_TABLE_BIT_KHR;
	}
	
	// Add acceleration structure storage flag when requested
	if (CrFlags & EHBF_ACCEL_STRUCT_STORAGE) {
		mUsageFlags |= VK_BUFFER_USAGE_ACCELERATION_STRUCTURE_STORAGE_BIT_KHR;
	}

	VK_CHECK_RESULT(mBuffer.createBuffer(Driver->mDevice,
		mUsageFlags,
		VK_MEMORY_PROPERTY_DEVICE_LOCAL_BIT,
		Size));
	if ( (mBuffer.mMemFlag & (VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT)) == (VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT))
	{
		//VK_CHECK_RESULT(mBuffer.recreateBuffer(Driver->mDevice,
		//	mUsageFlags,
		//	VK_MEMORY_PROPERTY_DEVICE_LOCAL_BIT | VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT,
		//	Size,
		//	initialData));
		mNeedStaging = false;
	}
	setupDescriptor();

	if (initialData)
	{
		if (!mNeedStaging) {
			copyFromMemory(initialData, 0, Size);
		}
		else
		{
			//int *p = (int*)initialData;		DP(("HB INITDATA [%d]= %08X %08X %08X %08X %08X %08X %08X %08X",Size,p[0],p[1],p[2],p[3],p[4],p[5],p[6],p[7]));
			VK_CHECK_RESULT(mStagingBuffer.createBuffer(Driver->mDevice,
				VK_BUFFER_USAGE_TRANSFER_SRC_BIT | (AccessType == EHBA_DEFAULT_RW? VK_BUFFER_USAGE_TRANSFER_DST_BIT:0),
				VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT,
				Size,
				initialData));
			CopyFromVkBuffer(0, 0, Size, mStagingBuffer.hBuffer);
			if (mStagingCreateCount < 2 || 	CrFlags & EHBF_ONE_TIME_UPLOAD) 
			mStagingBuffer.freeBufferAndMemory();

			//Driver->mDevice->copyBuffer(&mStagingBuffer, &mBuffer, Driver->queueCopy());
		}
	}

	DP(("HB %p ------------------------------------", mBuffer.hBuffer));
	
	// If buffer is of type shader resource, create view
	if (Type == EHBT_SHADER_RESOURCE)
	{
		VkBufferViewCreateInfo bvc;
		throw; // not support yet

		//Buffer->CreateView(bvd, &SRView);
	}

	// If buffer if of type compute, create view
	if (Type == EHBT_COMPUTE)
	{
#if TODO_COMPUTE
		D3D11_UNORDERED_ACCESS_VIEW_DESC UAVDesc;
		UAVDesc.ViewDimension = D3D11_UAV_DIMENSION_BUFFER;
		UAVDesc.Buffer.FirstElement = 0;
		UAVDesc.Buffer.Flags = 0;

		if (Driver->queryFeature(EVDF_COMPUTING_SHADER_5_0))
		{
			UAVDesc.Format = DXGI_FORMAT_UNKNOWN;
			UAVDesc.Buffer.NumElements = desc.ByteWidth / 4;	// size in floats
		}
		else if (Driver->queryFeature(EVDF_COMPUTING_SHADER_4_0))
		{
			UAVDesc.Format = DXGI_FORMAT_R32_TYPELESS;
			UAVDesc.Buffer.NumElements = desc.ByteWidth;		// size in bytes
		}

		hr = Driver->Device->CreateUnorderedAccessView(Buffer, &UAVDesc, &UAView);
		if (FAILED(hr))
		{
			IRRLOG(("Error creating unordered access view for buffer", ELL_ERROR));
			return false;
		}
#endif
	}

	return true;
}


//! Lock function.
void* VkHardwareBuffer::lock(bool needRead, u32 offset, u32 length)
{
	lockNeedRead = needRead;
	//DP(("lock %d %d ", offset, length));
	if (!mBuffer.hBuffer || offset + length > Size)
	{
		throw "memLockErr";
		return nullptr;
	}
	if (length == 0)
		length =   Size - offset;  // VK_WHOLE_SIZE is 64bit
	if (length ==0) return nullptr;
	void* data = nullptr;
	//assert(AccessType == EHBA_DYNAMIC);

	VkMemoryAllocateInfo memAllocInfo = vks::initializers::memoryAllocateInfo();
	VkMemoryRequirements memReqs;



	// Create a host-visible staging buffer that contains the raw image data
	if (mNeedStaging)
	{
		if (mStagingBuffer.hBuffer == VK_NULL_HANDLE)
		{
			mStagingCreateCount++;
			VK_CHECK_RESULT(Driver->mDevice->createBuffer(
				VK_BUFFER_USAGE_TRANSFER_SRC_BIT|VK_BUFFER_USAGE_TRANSFER_DST_BIT,
				VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT| (lockNeedRead || Size>3072 ? VK_MEMORY_PROPERTY_HOST_CACHED_BIT : 0), //write only CACHED also faster (only tested on PC)
				&mStagingBuffer,
				Size,
				nullptr));
		}
		if (needRead)
		{
			CopyToVkBuffer(0, 0, Size, mStagingBuffer.hBuffer);
		}
		mStagingBuffer.map(length, offset);

		data = mStagingBuffer.mapped;
		//DP(("vkMap %d %d ", offset, length));
		//VK_CHECK_RESULT(vkMapMemory(Device, mStagingBuffer.hMemory, offset, length, 0, (void **)&data));
	}
	else
	{
		VK_CHECK_RESULT(vkMapMemory(Device, mBuffer.hMemory, offset, length, 0, (void **)&data));
	}
	if (!data)
	{
		DP(("vkMapfailed XXX"));
		throw;
	}
	return data;


}

//! Unlock function. Must be called after a lock() to the buffer.
void VkHardwareBuffer::unlock()
{
	if (!mBuffer.hBuffer)
		return;
	if (mNeedStaging)
	{
		mStagingBuffer.unmap();
		//vkUnmapMemory(Device, mStagingBuffer.hMemory);
		//Driver->mDevice->copyBuffer(&mStagingBuffer, &mBuffer, Driver->Queue);
		CopyFromVkBuffer(0, 0, Size, mStagingBuffer.hBuffer);
		if (mStagingCreateCount<2 && !isExtCmdBuf && !mCmdBuf) //mCmdBuf delayed submit wait fence
		mStagingBuffer.freeBufferAndMemory();
		else if (!mCmdBuf && allowPrivateCmdBuf)
		{
			mCmdBuf = Driver->mDevice->createCommandBuffer(VK_COMMAND_BUFFER_LEVEL_PRIMARY, cmbPool, false);
			//DP(("Create HB CMB %p",mCmdBuf));
			isExtCmdBuf = false;
			mFence = Driver->mDevice->createFence(true);
		}
		//vkDestroyBuffer(Device, mStagingBuffer.buffer, nullptr);
		//vkFreeMemory(Device, stagingMemory, nullptr);
	}
	else
		vkUnmapMemory(Device, mBuffer.hMemory);


}

//! Copy data from system memory
void VkHardwareBuffer::copyFromMemory(const void* sysData, u32 offset, u32 length)
{
	if (length == 0) 	return;
	if (AccessType == EHBA_IMMUTABLE)
	{
		assert(0);
		return;		// Immutable cannot be modified
	}
	assert(Size >= offset+length);
	//CPU_COUNT_BEGIN;
	uint8_t *p = (uint8_t*)lock(false,offset,length);
	memcpy(p, sysData, length);
	unlock();
	//CPU_COUNT_END();
}


//! Copy data from another buffer
void VkHardwareBuffer::copyFromBuffer(IHardwareBuffer* buffer, u32 srcOffset, u32 destOffset, u32 length)
{
	if( AccessType == EHBA_IMMUTABLE )
	{
		assert(0);
		return;		// Immutable cannot be modified
	}

	if (!mBuffer.hBuffer)
		return;

	if (buffer->getDriverType() != EDT_VK)
	{
		IRRLOG(("Fatal Error: Tried to copy data from a buffer not owned by this driver.", ELL_ERROR));
		return;
	}

	VkHardwareBuffer* srcBuffer = static_cast<VkHardwareBuffer*>(buffer);
	assert(buffer);
	VkBuffer srcBuf = srcBuffer->mBuffer.hBuffer;
	CopyFromVkBuffer(srcOffset, destOffset, length, srcBuf);
}


void VkHardwareBuffer::CopyFromVkBuffer(const irr::u32 &srcOffset, const irr::u32 &destOffset, const irr::u32 &length, const VkBuffer &srcBuf)
{
	VkCommandBuffer copyCmd = mCmdBuf;
	VkCommandPool pool;
	if (!mCmdBuf)
		copyCmd = Driver->mDevice->createCommandBuffer(VK_COMMAND_BUFFER_LEVEL_PRIMARY, pool, true);
	else {
		beginCopyCmdBuf();
		pool = cmbPool;
	}

	VkBufferCopy bufferCopy{};
	bufferCopy.srcOffset = srcOffset;
	bufferCopy.dstOffset = destOffset;
	bufferCopy.size = length;
	vkCmdCopyBuffer(copyCmd, srcBuf, mBuffer.hBuffer, 1, &bufferCopy);

	if (!mCmdBuf)
	Driver->mDevice->flushCommandBuffer(copyCmd, pool, Driver->queueCopy());
	else
		flushCopyCmdBuf();

}
void VkHardwareBuffer::CopyToVkBuffer(const irr::u32& srcOffset, const irr::u32& destOffset, const irr::u32& length, const VkBuffer& dstBuf)
{
	VkCommandBuffer copyCmd = mCmdBuf;
	VkCommandPool pool;
	if (!mCmdBuf)
		copyCmd=Driver->mDevice->createCommandBuffer(VK_COMMAND_BUFFER_LEVEL_PRIMARY, pool, true);
	else {
		beginCopyCmdBuf();
		pool = cmbPool;
	}

	VkBufferCopy bufferCopy{};
	bufferCopy.srcOffset = srcOffset;
	bufferCopy.dstOffset = destOffset;
	bufferCopy.size = length;
	vkCmdCopyBuffer(copyCmd,  mBuffer.hBuffer, dstBuf, 1, &bufferCopy);

	if(!mCmdBuf)
		Driver->mDevice->flushCommandBuffer(copyCmd, pool, Driver->queueCopy());
	else
		flushCopyCmdBuf();
	
}
void VkHardwareBuffer::beginCopyCmdBuf() {
	//VK_CHECK_RESULT(vkWaitForFences(Driver->Device, 1, &mFence, VK_TRUE, DEFAULT_FENCE_TIMEOUT));

	VK_CHECK_RESULT(vkResetFences(Driver->Device, 1, &mFence));
	VkCommandBufferBeginInfo cmdBufInfo = vks::initializers::commandBufferBeginInfo();
	VK_CHECK_RESULT(vkBeginCommandBuffer(mCmdBuf, &cmdBufInfo));
}
void VkHardwareBuffer::flushCopyCmdBuf()
{
	//vks::tools::insertMemoryBarrier(mCmdBuf, 		VK_PIPELINE_STAGE_TRANSFER_BIT, VK_ACCESS_TRANSFER_WRITE_BIT, 			VK_PIPELINE_STAGE_BOTTOM_OF_PIPE_BIT, 0);
	
	VK_CHECK_RESULT(vkEndCommandBuffer(mCmdBuf));
	//DP(("END CMB %p", mCmdBuf));
	VkSubmitInfo submitInfo = vks::initializers::submitInfo();
	submitInfo.commandBufferCount = 1;
	submitInfo.pCommandBuffers = &mCmdBuf;
	//assert(mFence);
	VK_CHECK_RESULT(vkQueueSubmit(Driver->queueCopy(), 1, &submitInfo, mFence));
	//Driver->addWaitFence(mFence);
	VK_CHECK_RESULT(vkWaitForFences(Driver->Device, 1, &mFence, VK_TRUE, DEFAULT_FENCE_TIMEOUT));
}

bool VkHardwareBuffer::swapBindingMemory(VkHardwareBuffer* vkbuf)
{
	mBuffer.swapMemory(vkbuf->mBuffer);
	return true;
}
//! Get size of buffer in bytes
u32 VkHardwareBuffer::size() const
{
	return Size;
}

//! Get driver type of buffer.
E_DRIVER_TYPE VkHardwareBuffer::getDriverType() const
{
	return EDT_VK;
}

//! Get type of buffer.
E_HARDWARE_BUFFER_TYPE VkHardwareBuffer::getType() const
{
	return Type;
}

u32 VkHardwareBuffer::getFlags() const
{
	return CrFlags;
}

 
VkBuffer VkHardwareBuffer::getBufferResource() const
{
	return mBuffer.hBuffer;
}

#if VK_ENABLE_RAYTRACING
//! Get device address for raytracing
VkDeviceAddress VkHardwareBuffer::getDeviceAddress() const
{
	if (!mBuffer.hBuffer) {
		return 0;
	}
	
	VkBufferDeviceAddressInfo addressInfo = {};
	addressInfo.sType = VK_STRUCTURE_TYPE_BUFFER_DEVICE_ADDRESS_INFO;
	addressInfo.buffer = mBuffer.hBuffer;
	
	return vkGetBufferDeviceAddress(Device, &addressInfo);
}
#endif
void VkHardwareBuffer::setupDescriptor(VkDeviceSize size, VkDeviceSize offset )
{
	Descriptor.offset = offset;
	Descriptor.buffer = mBuffer.hBuffer;
	Descriptor.range = size;
}
//! return unordered access view
//ID3D11UnorderedAccessView* VkHardwareBuffer::getUnorderedAccessView() const
//{
//	return UAView;
//}

//VkBufferView VkHardwareBuffer::getShaderResourceView() const
//{
//	return SRView;
//}
void VkHardwareBuffer::resize(u32 size)
{
	assert(size > Size);
	Size = size;
	VK_CHECK_RESULT(mBuffer.createBuffer(Driver->mDevice, mUsageFlags | VK_BUFFER_USAGE_STORAGE_BUFFER_BIT | VK_BUFFER_USAGE_TRANSFER_DST_BIT,
		VK_MEMORY_PROPERTY_DEVICE_LOCAL_BIT, Size));
	if (mStagingBuffer.hBuffer)
		mStagingBuffer.createBuffer(Driver->mDevice,
			VK_BUFFER_USAGE_TRANSFER_SRC_BIT | (AccessType == EHBA_DEFAULT_RW ? VK_BUFFER_USAGE_TRANSFER_DST_BIT : 0),
			VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT | (lockNeedRead ? VK_MEMORY_PROPERTY_HOST_CACHED_BIT : 0),
			Size,
			nullptr);

}
VkMemoryPropertyFlags GetMemoryFlagsForUsage(E_HARDWARE_BUFFER_ACCESS acctype) {
	VkMemoryPropertyFlags memoryFlags = 0;

	switch (acctype) {
	case EHBA_DEFAULT:
	case EHBA_DEFAULT_RW:
	case EHBA_IMMUTABLE:
		memoryFlags |= VK_MEMORY_PROPERTY_DEVICE_LOCAL_BIT;
		break;

	case EHBA_DYNAMIC:
		memoryFlags |= VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT
			| VK_MEMORY_PROPERTY_HOST_COHERENT_BIT;
		break;

	default://EHBA_SYSTEM_MEMORY
		memoryFlags |= VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT
			| VK_MEMORY_PROPERTY_HOST_COHERENT_BIT
			| VK_MEMORY_PROPERTY_HOST_CACHED_BIT
			;
		break;
	}

	return memoryFlags;
}

}
}

#endif