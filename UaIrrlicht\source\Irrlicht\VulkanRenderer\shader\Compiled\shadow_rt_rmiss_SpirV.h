	// 1115.1.0
	 #pragma once
const uint32_t SpirV_shadow_rt_rmiss[] = {
	0x07230203,0x00010600,0x0008000b,0x00000039,0x00000000,0x00020011,0x0000117f,0x0008000a,
	0x5f565053,0x5f52484b,0x5f6e6f6e,0x616d6573,0x6369746e,0x666e695f,0x0000006f,0x0006000a,
	0x5f565053,0x5f52484b,0x5f796172,0x63617274,0x00676e69,0x000b000b,0x00000001,0x536e6f4e,
	0x6e616d65,0x2e636974,0x64616853,0x442e7265,0x67756265,0x6f666e49,0x3030312e,0x00000000,
	0x0006000b,0x00000004,0x4c534c47,0x6474732e,0x3035342e,0x00000000,0x0003000e,0x00000000,
	0x00000001,0x0006000f,0x000014c5,0x0000000f,0x6e69616d,0x00000000,0x0000002a,0x00060007,
	0x00000002,0x64616873,0x725f776f,0x6d722e74,0x00737369,0x00080007,0x00000003,0x61522f2e,
	0x61727479,0x676e6963,0x72616853,0x682e6465,0x00000000,0x00040007,0x00000009,0x746e6975,
	0x00000000,0x00040007,0x00000011,0x6e69616d,0x00000000,0x00540007,0x00000014,0x72657623,
	0x6e6f6973,0x30363420,0x7865230a,0x736e6574,0x206e6f69,0x455f4c47,0x725f5458,0x745f7961,
	0x69636172,0x3a20676e,0x616e6520,0x0a656c62,0x74786523,0x69736e65,0x47206e6f,0x4f475f4c,
	0x454c474f,0x636e695f,0x6564756c,0x7269645f,0x69746365,0x3a206576,0x71657220,0x65726975,
	0x69230a0a,0x756c636e,0x22206564,0x74796152,0x69636172,0x6853676e,0x64657261,0x0a22682e,
	0x202f2f0a,0x64616853,0x7220776f,0x70207961,0x6f6c7961,0x6c0a6461,0x756f7961,0x6f6c2874,
	0x69746163,0x3d206e6f,0x5f545220,0x4c594150,0x5f44414f,0x44414853,0x2029574f,0x50796172,
	0x6f6c7961,0x6e496461,0x20545845,0x64616853,0x6152776f,0x79615079,0x64616f6c,0x61687320,
	0x52776f64,0x0a3b7961,0x696f760a,0x616d2064,0x29286e69,0x200a7b20,0x2f202020,0x6853202f,
	0x776f6461,0x79617220,0x73696d20,0x20646573,0x6f6e202d,0x6e692074,0x61687320,0x0a776f64,
	0x20202020,0x64616873,0x6152776f,0x6e692e79,0x64616853,0x3d20776f,0x6c616620,0x0a3b6573,
	0x0000207d,0x00040007,0x0000001e,0x6c6f6f62,0x00000000,0x00050007,0x00000022,0x68536e69,
	0x776f6461,0x00000000,0x00070007,0x00000025,0x64616853,0x6152776f,0x79615079,0x64616f6c,
	0x00000000,0x00050007,0x0000002c,0x64616873,0x6152776f,0x00000079,0x00030007,0x0000002f,
	0x00746e69,0x00060004,0x455f4c47,0x725f5458,0x745f7961,0x69636172,0x0000676e,0x000a0004,
	0x475f4c47,0x4c474f4f,0x70635f45,0x74735f70,0x5f656c79,0x656e696c,0x7269645f,0x69746365,
	0x00006576,0x00080004,0x475f4c47,0x4c474f4f,0x6e695f45,0x64756c63,0x69645f65,0x74636572,
	0x00657669,0x00040005,0x0000000f,0x6e69616d,0x00000000,0x00070005,0x00000020,0x64616853,
	0x6152776f,0x79615079,0x64616f6c,0x00000000,0x00060006,0x00000020,0x00000000,0x68536e69,
	0x776f6461,0x00000000,0x00050005,0x0000002a,0x64616873,0x6152776f,0x00000079,0x0006014a,
	0x72746e65,0x6f702d79,0x20746e69,0x6e69616d,0x00000000,0x0006014a,0x65696c63,0x7620746e,
	0x616b6c75,0x3030316e,0x00000000,0x0006014a,0x67726174,0x652d7465,0x7320766e,0x76726970,
	0x00362e31,0x0007014a,0x67726174,0x652d7465,0x7620766e,0x616b6c75,0x332e316e,0x00000000,
	0x0006014a,0x72746e65,0x6f702d79,0x20746e69,0x6e69616d,0x00000000,0x00020013,0x00000005,
	0x00030021,0x00000006,0x00000005,0x00040015,0x00000008,0x00000020,0x00000000,0x0004002b,
	0x00000008,0x0000000b,0x00000020,0x0004002b,0x00000008,0x0000000c,0x00000006,0x0004002b,
	0x00000008,0x0000000d,0x00000000,0x0009000c,0x00000005,0x0000000a,0x00000001,0x00000002,
	0x00000009,0x0000000b,0x0000000c,0x0000000d,0x0004002b,0x00000008,0x0000000e,0x00000003,
	0x0007000c,0x00000005,0x00000007,0x00000001,0x00000008,0x0000000e,0x00000005,0x0007000c,
	0x00000005,0x00000013,0x00000001,0x00000023,0x00000002,0x00000014,0x0004002b,0x00000008,
	0x00000015,0x0000000a,0x0004002b,0x00000008,0x00000017,0x00000001,0x0004002b,0x00000008,
	0x00000018,0x00000004,0x0004002b,0x00000008,0x00000019,0x00000002,0x0009000c,0x00000005,
	0x00000016,0x00000001,0x00000001,0x00000017,0x00000018,0x00000013,0x00000019,0x000e000c,
	0x00000005,0x00000012,0x00000001,0x00000014,0x00000011,0x00000007,0x00000013,0x00000015,
	0x0000000d,0x00000016,0x00000011,0x0000000e,0x00000015,0x00020014,0x0000001d,0x0009000c,
	0x00000005,0x0000001f,0x00000001,0x00000002,0x0000001e,0x0000000b,0x00000019,0x0000000d,
	0x0003001e,0x00000020,0x0000001d,0x0004002b,0x00000008,0x00000023,0x0000003b,0x000d000c,
	0x00000005,0x00000021,0x00000001,0x0000000b,0x00000022,0x0000001f,0x00000013,0x00000023,
	0x00000015,0x0000000d,0x0000000d,0x0000000e,0x0004002b,0x00000008,0x00000026,0x0000000c,
	0x000f000c,0x00000005,0x00000024,0x00000001,0x0000000a,0x00000025,0x00000017,0x00000013,
	0x00000026,0x0000000d,0x00000016,0x00000025,0x0000000d,0x0000000e,0x00000021,0x00040020,
	0x00000027,0x000014de,0x00000020,0x0004002b,0x00000008,0x00000028,0x000014de,0x0008000c,
	0x00000005,0x00000029,0x00000001,0x00000003,0x00000024,0x00000028,0x0000000d,0x0004003b,
	0x00000027,0x0000002a,0x000014de,0x0004002b,0x00000008,0x0000002d,0x00000008,0x000e000c,
	0x00000005,0x0000002b,0x00000001,0x00000012,0x0000002c,0x00000024,0x00000013,0x00000026,
	0x0000000d,0x00000016,0x0000002c,0x0000002a,0x0000002d,0x00040015,0x0000002e,0x00000020,
	0x00000001,0x0009000c,0x00000005,0x00000030,0x00000001,0x00000002,0x0000002f,0x0000000b,
	0x00000018,0x0000000d,0x0004002b,0x0000002e,0x00000031,0x00000000,0x0003002a,0x0000001d,
	0x00000032,0x00040020,0x00000033,0x000014de,0x0000001d,0x0008000c,0x00000005,0x00000034,
	0x00000001,0x00000003,0x0000001f,0x00000028,0x0000000d,0x0004002b,0x00000008,0x00000038,
	0x0000000d,0x00050036,0x00000005,0x0000000f,0x00000000,0x00000006,0x000200f8,0x00000010,
	0x0006000c,0x00000005,0x0000001b,0x00000001,0x00000017,0x00000012,0x000a000c,0x00000005,
	0x0000001c,0x00000001,0x00000067,0x00000013,0x00000015,0x00000015,0x0000000d,0x0000000d,
	0x0007000c,0x00000005,0x0000001a,0x00000001,0x00000065,0x00000012,0x0000000f,0x000a000c,
	0x00000005,0x00000036,0x00000001,0x00000067,0x00000013,0x00000026,0x00000026,0x0000000d,
	0x0000000d,0x00050041,0x00000033,0x00000035,0x0000002a,0x00000031,0x0003003e,0x00000035,
	0x00000032,0x000a000c,0x00000005,0x00000037,0x00000001,0x00000067,0x00000013,0x00000038,
	0x00000038,0x0000000d,0x0000000d,0x000100fd,0x00010038
};
