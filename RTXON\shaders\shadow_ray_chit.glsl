#version 460
#extension GL_EXT_ray_tracing : enable  // 启用光线追踪扩展
#extension GL_GOOGLE_include_directive : require  // 启用#include指令

#include "../shared_with_shaders.h"  // 包含共享定义

/**
 * 阴影射线命中着色器 - 处理阴影射线命中几何体的情况
 * 主要功能:
 * 1. 记录命中距离到payload
 * 2. 用于判断光源是否被遮挡
 */

// 阴影射线payload输入
layout(location = SWS_LOC_SHADOW_RAY) rayPayloadInEXT ShadowRayPayload ShadowRay;

/**
 * 主着色器函数 - 处理阴影射线命中
 * 只需记录命中距离，用于判断是否被遮挡
 */
void main() {
    ShadowRay.distance = gl_HitTEXT;  // 记录命中距离
}
