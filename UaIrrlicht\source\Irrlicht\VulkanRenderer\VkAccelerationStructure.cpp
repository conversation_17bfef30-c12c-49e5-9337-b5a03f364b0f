#include "VkAccelerationStructure.h"
#include "VkDriver.h"
#include "VkHardwareBuffer.h"
#include "os.h"

#ifdef _IRR_COMPILE_WITH_VULKAN_
#if VK_ENABLE_RAYTRACING

namespace irr {
namespace video {

// =============================================================================
// VkAccelerationStructureWrapper Implementation
// =============================================================================

VkAccelerationStructureWrapper::VkAccelerationStructureWrapper(VkDriver* driver)
    : driver(driver), handle(VK_NULL_HANDLE), buffer(nullptr), deviceAddress(0), isValid(false) {
}

VkAccelerationStructureWrapper::~VkAccelerationStructureWrapper() {
    destroy();
}

bool VkAccelerationStructureWrapper::initialize(VkAccelerationStructureTypeKHR type, VkDeviceSize size) {
    if (!driver || isValid) {
        return false;
    }

    // Create buffer for acceleration structure storage
    buffer = static_cast<VkHardwareBuffer*>(driver->createHardwareBuffer(
        EHBT_STORAGE,
        EHBA_DEFAULT,
        static_cast<u32>(size),
        EHBF_ACCEL_STRUCT_STORAGE | EHBF_DEVICE_ADDRESS,
        nullptr
    ));

    if (!buffer) {
        os::Printer::log("VkAccelerationStructureWrapper: Failed to create buffer", ELL_ERROR);
        return false;
    }

    // Create acceleration structure
    VkAccelerationStructureCreateInfoKHR createInfo = {};
    createInfo.sType = VK_STRUCTURE_TYPE_ACCELERATION_STRUCTURE_CREATE_INFO_KHR;
    createInfo.type = type;
    createInfo.size = size;
    createInfo.buffer = buffer->getBufferResource();

    VkResult result = driver->vkCreateAccelerationStructureKHR(driver->getDevice(), &createInfo, nullptr, &handle);
    if (result != VK_SUCCESS) {
        os::Printer::log("VkAccelerationStructureWrapper: Failed to create acceleration structure", ELL_ERROR);
        delete buffer;
        buffer = nullptr;
        return false;
    }

    // Get device address
    VkAccelerationStructureDeviceAddressInfoKHR addressInfo = {};
    addressInfo.sType = VK_STRUCTURE_TYPE_ACCELERATION_STRUCTURE_DEVICE_ADDRESS_INFO_KHR;
    addressInfo.accelerationStructure = handle;
    deviceAddress = driver->vkGetAccelerationStructureDeviceAddressKHR(driver->getDevice(), &addressInfo);

    isValid = true;
    return true;
}

void VkAccelerationStructureWrapper::destroy() {
    if (!driver) {
        return;
    }

    if (handle != VK_NULL_HANDLE) {
        driver->vkDestroyAccelerationStructureKHR(driver->getDevice(), handle, nullptr);
        handle = VK_NULL_HANDLE;
    }

    delete buffer;
    buffer = nullptr;
    
    deviceAddress = 0;
    isValid = false;
}

// =============================================================================
// VkBottomLevelAS Implementation
// =============================================================================

VkBottomLevelAS::VkBottomLevelAS(VkDriver* driver)
    : VkAccelerationStructureWrapper(driver), m_needsRebuild(true) {
}

VkBottomLevelAS::~VkBottomLevelAS() {
    destroy();
}

bool VkBottomLevelAS::initialize() {
    // Don't create AS with hardcoded size - buildFromMesh will handle AS creation
    // Just mark as ready for geometry building
    m_needsRebuild = true;
    return true;
}

bool VkBottomLevelAS::buildFromMesh(const void* vertices, u32 vertexCount, u32 vertexStride,
                                   const void* indices, u32 indexCount, bool isOpaque) {
    if (!driver || !vertices || !indices || vertexCount == 0 || indexCount == 0) {
        os::Printer::log("VkBottomLevelAS: Invalid parameters for buildFromMesh", ELL_ERROR);
        return false;
    }

    // Create vertex buffer
    VkHardwareBuffer* vertexBuffer = static_cast<VkHardwareBuffer*>(driver->createHardwareBuffer(
        EHBT_VERTEX,
        EHBA_DEFAULT,
        vertexCount * vertexStride,
        EHBF_DEVICE_ADDRESS | EHBF_ACCEL_STRUCT_BUILD_INPUT,
        nullptr
    ));

    if (!vertexBuffer) {
        os::Printer::log("VkBottomLevelAS: Failed to create vertex buffer", ELL_ERROR);
        return false;
    }

    // Create index buffer
    VkHardwareBuffer* indexBuffer = static_cast<VkHardwareBuffer*>(driver->createHardwareBuffer(
        EHBT_INDEX,
        EHBA_DEFAULT,
        indexCount * sizeof(u32),
        EHBF_DEVICE_ADDRESS | EHBF_ACCEL_STRUCT_BUILD_INPUT,
        nullptr
    ));

    if (!indexBuffer) {
        os::Printer::log("VkBottomLevelAS: Failed to create index buffer", ELL_ERROR);
        delete vertexBuffer;
        return false;
    }

    // Upload vertex data
    vertexBuffer->copyFromMemory(vertices, 0, vertexCount * vertexStride);

    // Upload index data
    indexBuffer->copyFromMemory(indices, 0, indexCount * sizeof(u32));

    // Setup geometry description
    VkAccelerationStructureGeometryKHR geometry = {};
    geometry.sType = VK_STRUCTURE_TYPE_ACCELERATION_STRUCTURE_GEOMETRY_KHR;
    geometry.geometryType = VK_GEOMETRY_TYPE_TRIANGLES_KHR;
    geometry.flags = isOpaque ? VK_GEOMETRY_OPAQUE_BIT_KHR : 0;

    geometry.geometry.triangles.sType = VK_STRUCTURE_TYPE_ACCELERATION_STRUCTURE_GEOMETRY_TRIANGLES_DATA_KHR;
    geometry.geometry.triangles.vertexFormat = VK_FORMAT_R32G32B32_SFLOAT;
    geometry.geometry.triangles.vertexData.deviceAddress = vertexBuffer->getDeviceAddress();
    geometry.geometry.triangles.vertexStride = vertexStride;
    geometry.geometry.triangles.maxVertex = vertexCount - 1;
    geometry.geometry.triangles.indexData.deviceAddress = indexBuffer->getDeviceAddress();
    geometry.geometry.triangles.indexType = VK_INDEX_TYPE_UINT32;

    // Setup build info
    VkAccelerationStructureBuildGeometryInfoKHR buildInfo = {};
    buildInfo.sType = VK_STRUCTURE_TYPE_ACCELERATION_STRUCTURE_BUILD_GEOMETRY_INFO_KHR;
    buildInfo.type = VK_ACCELERATION_STRUCTURE_TYPE_BOTTOM_LEVEL_KHR;
    buildInfo.mode = VK_BUILD_ACCELERATION_STRUCTURE_MODE_BUILD_KHR;
    buildInfo.flags = VK_BUILD_ACCELERATION_STRUCTURE_PREFER_FAST_TRACE_BIT_KHR;
    buildInfo.geometryCount = 1;
    buildInfo.pGeometries = &geometry;

    // Get build sizes
    u32 primitiveCount = indexCount / 3;
    VkAccelerationStructureBuildSizesInfoKHR sizeInfo = {};
    sizeInfo.sType = VK_STRUCTURE_TYPE_ACCELERATION_STRUCTURE_BUILD_SIZES_INFO_KHR;
    
    driver->vkGetAccelerationStructureBuildSizesKHR(
        driver->getDevice(),
        VK_ACCELERATION_STRUCTURE_BUILD_TYPE_DEVICE_KHR,
        &buildInfo,
        &primitiveCount,
        &sizeInfo
    );

    // Create buffer for BLAS BEFORE creating the acceleration structure (rtxON2 pattern)
    if (!isValid) {
        destroy(); // Clean up any existing AS
        
        // Create buffer first (like rtxON2)
        buffer = static_cast<VkHardwareBuffer*>(driver->createHardwareBuffer(
            EHBT_STORAGE,
            EHBA_DEFAULT,
            static_cast<u32>(sizeInfo.accelerationStructureSize),
            EHBF_ACCEL_STRUCT_STORAGE | EHBF_DEVICE_ADDRESS,
            nullptr
        ));

        if (!buffer) {
            os::Printer::log("VkBottomLevelAS: Failed to create AS buffer", ELL_ERROR);
            delete vertexBuffer;
            delete indexBuffer;
            return false;
        }

        // Create acceleration structure (like rtxON2)
        VkAccelerationStructureCreateInfoKHR createInfo = {};
        createInfo.sType = VK_STRUCTURE_TYPE_ACCELERATION_STRUCTURE_CREATE_INFO_KHR;
        createInfo.type = VK_ACCELERATION_STRUCTURE_TYPE_BOTTOM_LEVEL_KHR;
        createInfo.size = sizeInfo.accelerationStructureSize;
        createInfo.buffer = buffer->getBufferResource();

        VkResult result = driver->vkCreateAccelerationStructureKHR(driver->getDevice(), &createInfo, nullptr, &handle);
        if (result != VK_SUCCESS) {
            os::Printer::log("VkBottomLevelAS: Failed to create acceleration structure", ELL_ERROR);
            delete buffer;
            buffer = nullptr;
            delete vertexBuffer;
            delete indexBuffer;
            return false;
        }

        // Get device address
        VkAccelerationStructureDeviceAddressInfoKHR addressInfo = {};
        addressInfo.sType = VK_STRUCTURE_TYPE_ACCELERATION_STRUCTURE_DEVICE_ADDRESS_INFO_KHR;
        addressInfo.accelerationStructure = handle;
        deviceAddress = driver->vkGetAccelerationStructureDeviceAddressKHR(driver->getDevice(), &addressInfo);

        isValid = true;
    }

    // Create scratch buffer
    VkHardwareBuffer* scratchBuffer = static_cast<VkHardwareBuffer*>(driver->createHardwareBuffer(
        EHBT_STORAGE,
        EHBA_DEFAULT,
        static_cast<u32>(sizeInfo.buildScratchSize),
        EHBF_DEVICE_ADDRESS,
        nullptr
    ));

    if (!scratchBuffer) {
        os::Printer::log("VkBottomLevelAS: Failed to create scratch buffer", ELL_ERROR);
        delete vertexBuffer;
        delete indexBuffer;
        return false;
    }

    // Build acceleration structure
    buildInfo.scratchData.deviceAddress = scratchBuffer->getDeviceAddress();
    buildInfo.srcAccelerationStructure = VK_NULL_HANDLE;
    buildInfo.dstAccelerationStructure = handle;

    VkAccelerationStructureBuildRangeInfoKHR range = {};
    range.primitiveCount = primitiveCount;
    range.primitiveOffset = 0;
    range.firstVertex = 0;
    range.transformOffset = 0;

    // Use single-time command buffer for building (like rtxON2)
    VkCommandBuffer commandBuffer = driver->beginSingleTimeCommands();
    if (commandBuffer == VK_NULL_HANDLE) {
        os::Printer::log("VkBottomLevelAS: Failed to begin command buffer", ELL_ERROR);
        delete vertexBuffer;
        delete indexBuffer;
        delete scratchBuffer;
        return false;
    }

    // Build range (like rtxON2)
    const VkAccelerationStructureBuildRangeInfoKHR* ranges[1] = { &range };

    driver->vkCmdBuildAccelerationStructuresKHR(commandBuffer, 1, &buildInfo, ranges);

    // Add memory barrier (like rtxON2)
    VkMemoryBarrier memoryBarrier = {};
    memoryBarrier.sType = VK_STRUCTURE_TYPE_MEMORY_BARRIER;
    memoryBarrier.srcAccessMask = VK_ACCESS_ACCELERATION_STRUCTURE_WRITE_BIT_KHR;
    memoryBarrier.dstAccessMask = VK_ACCESS_ACCELERATION_STRUCTURE_READ_BIT_KHR;

    vkCmdPipelineBarrier(commandBuffer,
        VK_PIPELINE_STAGE_ACCELERATION_STRUCTURE_BUILD_BIT_KHR,
        VK_PIPELINE_STAGE_ACCELERATION_STRUCTURE_BUILD_BIT_KHR,
        0, 1, &memoryBarrier, 0, nullptr, 0, nullptr);

    driver->endSingleTimeCommands(commandBuffer);

    // Cleanup temporary buffers
    delete vertexBuffer;
    delete indexBuffer;
    delete scratchBuffer;

    m_needsRebuild = false;
    os::Printer::log("VkBottomLevelAS: Successfully built BLAS", ELL_INFORMATION);
    return true;
}

bool VkBottomLevelAS::needsRebuild() const {
    return m_needsRebuild;
}

bool VkBottomLevelAS::build() {
    // For now, just mark as not needing rebuild
    // In a full implementation, this would rebuild the AS
    m_needsRebuild = false;
    return true;
}

// =============================================================================
// VkTopLevelAS Implementation
// =============================================================================

VkTopLevelAS::VkTopLevelAS(VkDriver* driver)
    : VkAccelerationStructureWrapper(driver), m_needsRebuild(true) {
}

VkTopLevelAS::~VkTopLevelAS() {
    destroy();
}

bool VkTopLevelAS::initialize() {
    // Don't create AS with hardcoded size - build method will handle AS creation
    // Just mark as ready for instance building
    m_needsRebuild = true;
    return true;
}

u32 VkTopLevelAS::addInstance(VkBottomLevelAS* blas, const core::matrix4& transform, 
                             u32 instanceID, u32 hitGroupIndex, u8 mask) {
    if (!blas || !blas->isValidAS()) {
        return ~0u; // Invalid index
    }

    Instance instance;
    instance.blas = blas;
    instance.transform = transform;
    instance.instanceID = instanceID;
    instance.hitGroupIndex = hitGroupIndex;
    instance.mask = mask;

    instances.push_back(instance);
    m_needsRebuild = true;

    return instances.size() - 1;
}

bool VkTopLevelAS::updateInstanceTransform(u32 instanceIndex, const core::matrix4& transform) {
    if (instanceIndex >= instances.size()) {
        return false;
    }

    instances[instanceIndex].transform = transform;
    m_needsRebuild = true;
    return true;
}

void VkTopLevelAS::removeInstance(u32 instanceIndex) {
    if (instanceIndex >= instances.size()) {
        return;
    }

    instances.erase(instanceIndex);
    m_needsRebuild = true;
}

bool VkTopLevelAS::build() {
    if (!driver || instances.size() == 0) {
        os::Printer::log("VkTopLevelAS: No driver or no instances", ELL_ERROR);
        return false;
    }

    // Follow rtxON2 pattern exactly - create instances array
    core::array<VkAccelerationStructureInstanceKHR> vkInstances;
    vkInstances.set_used(instances.size());

    // Create identity transform like rtxON2
    const VkTransformMatrixKHR identityTransform = {
        1.0f, 0.0f, 0.0f, 0.0f,
        0.0f, 1.0f, 0.0f, 0.0f,
        0.0f, 0.0f, 1.0f, 0.0f,
    };

    for (u32 i = 0; i < instances.size(); ++i) {
        const Instance& inst = instances[i];
        VkAccelerationStructureInstanceKHR& vkInst = vkInstances[i];

        // Use identity transform for now (like rtxON2 sample)
        vkInst.transform = identityTransform;
        vkInst.instanceCustomIndex = inst.instanceID;
        vkInst.mask = inst.mask;
        vkInst.instanceShaderBindingTableRecordOffset = inst.hitGroupIndex;
        vkInst.flags = VK_GEOMETRY_INSTANCE_TRIANGLE_FACING_CULL_DISABLE_BIT_KHR;
        vkInst.accelerationStructureReference = inst.blas->getDeviceAddress();
    }

    // Create instances buffer - follow rtxON2 pattern exactly
    VkHardwareBuffer* instancesBuffer = static_cast<VkHardwareBuffer*>(driver->createHardwareBuffer(
        EHBT_STORAGE,
        EHBA_DEFAULT,
        vkInstances.size() * sizeof(VkAccelerationStructureInstanceKHR),
        EHBF_DEVICE_ADDRESS | EHBF_ACCEL_STRUCT_BUILD_INPUT,
        nullptr
    ));

    if (!instancesBuffer) {
        os::Printer::log("VkTopLevelAS: Failed to create instances buffer", ELL_ERROR);
        return false;
    }

    // Upload instance data
    instancesBuffer->copyFromMemory(vkInstances.pointer(), 0, vkInstances.size() * sizeof(VkAccelerationStructureInstanceKHR));

    // Setup TLAS geometry - follow rtxON2 pattern exactly
    VkAccelerationStructureGeometryInstancesDataKHR tlasInstancesInfo = {};
    tlasInstancesInfo.sType = VK_STRUCTURE_TYPE_ACCELERATION_STRUCTURE_GEOMETRY_INSTANCES_DATA_KHR;
    tlasInstancesInfo.data.deviceAddress = instancesBuffer->getDeviceAddress();

    VkAccelerationStructureGeometryKHR tlasGeoInfo = {};
    tlasGeoInfo.sType = VK_STRUCTURE_TYPE_ACCELERATION_STRUCTURE_GEOMETRY_KHR;
    tlasGeoInfo.geometryType = VK_GEOMETRY_TYPE_INSTANCES_KHR;
    tlasGeoInfo.geometry.instances = tlasInstancesInfo;

    // Setup build info - follow rtxON2 pattern exactly
    VkAccelerationStructureBuildGeometryInfoKHR buildInfo = {};
    buildInfo.sType = VK_STRUCTURE_TYPE_ACCELERATION_STRUCTURE_BUILD_GEOMETRY_INFO_KHR;
    buildInfo.type = VK_ACCELERATION_STRUCTURE_TYPE_TOP_LEVEL_KHR;
    buildInfo.mode = VK_BUILD_ACCELERATION_STRUCTURE_MODE_BUILD_KHR;
    buildInfo.flags = VK_BUILD_ACCELERATION_STRUCTURE_PREFER_FAST_TRACE_BIT_KHR;
    buildInfo.geometryCount = 1;
    buildInfo.pGeometries = &tlasGeoInfo;

    // Get build sizes
    const u32 numInstances = static_cast<u32>(instances.size());
    VkAccelerationStructureBuildSizesInfoKHR sizeInfo = { VK_STRUCTURE_TYPE_ACCELERATION_STRUCTURE_BUILD_SIZES_INFO_KHR };
    
    driver->vkGetAccelerationStructureBuildSizesKHR(
        driver->getDevice(),
        VK_ACCELERATION_STRUCTURE_BUILD_TYPE_DEVICE_KHR,
        &buildInfo,
        &numInstances,
        &sizeInfo
    );

    // Create buffer for TLAS BEFORE creating the acceleration structure (rtxON2 pattern)
    if (!isValid) {
        destroy(); // Clean up any existing AS
        
        // Create buffer first (like rtxON2)
        buffer = static_cast<VkHardwareBuffer*>(driver->createHardwareBuffer(
            EHBT_STORAGE,
            EHBA_DEFAULT,
            static_cast<u32>(sizeInfo.accelerationStructureSize),
            EHBF_ACCEL_STRUCT_STORAGE | EHBF_DEVICE_ADDRESS,
            nullptr
        ));

        if (!buffer) {
            os::Printer::log("VkTopLevelAS: Failed to create AS buffer", ELL_ERROR);
            delete instancesBuffer;
            return false;
        }

        // Create acceleration structure (like rtxON2)
        VkAccelerationStructureCreateInfoKHR createInfo = {};
        createInfo.sType = VK_STRUCTURE_TYPE_ACCELERATION_STRUCTURE_CREATE_INFO_KHR;
        createInfo.type = VK_ACCELERATION_STRUCTURE_TYPE_TOP_LEVEL_KHR;
        createInfo.size = sizeInfo.accelerationStructureSize;
        createInfo.buffer = buffer->getBufferResource();

        VkResult result = driver->vkCreateAccelerationStructureKHR(driver->getDevice(), &createInfo, nullptr, &handle);
        if (result != VK_SUCCESS) {
            os::Printer::log("VkTopLevelAS: Failed to create acceleration structure", ELL_ERROR);
            delete buffer;
            buffer = nullptr;
            delete instancesBuffer;
            return false;
        }

        // Get device address
        VkAccelerationStructureDeviceAddressInfoKHR addressInfo = {};
        addressInfo.sType = VK_STRUCTURE_TYPE_ACCELERATION_STRUCTURE_DEVICE_ADDRESS_INFO_KHR;
        addressInfo.accelerationStructure = handle;
        deviceAddress = driver->vkGetAccelerationStructureDeviceAddressKHR(driver->getDevice(), &addressInfo);

        isValid = true;
    }

    // Create scratch buffer
    VkHardwareBuffer* scratchBuffer = static_cast<VkHardwareBuffer*>(driver->createHardwareBuffer(
        EHBT_STORAGE,
        EHBA_DEFAULT,
        static_cast<u32>(sizeInfo.buildScratchSize),
        EHBF_DEVICE_ADDRESS,
        nullptr
    ));

    if (!scratchBuffer) {
        os::Printer::log("VkTopLevelAS: Failed to create scratch buffer", ELL_ERROR);
        delete instancesBuffer;
        return false;
    }

    // Setup build info with scratch buffer
    buildInfo.scratchData.deviceAddress = scratchBuffer->getDeviceAddress();
    buildInfo.srcAccelerationStructure = VK_NULL_HANDLE;
    buildInfo.dstAccelerationStructure = handle;

    // Create command buffer for building (like rtxON2)
    VkCommandBuffer commandBuffer = driver->beginSingleTimeCommands();
    if (commandBuffer == VK_NULL_HANDLE) {
        os::Printer::log("VkTopLevelAS: Failed to begin command buffer", ELL_ERROR);
        delete instancesBuffer;
        delete scratchBuffer;
        return false;
    }

    // Build range
    VkAccelerationStructureBuildRangeInfoKHR range = {};
    range.primitiveCount = numInstances;
    range.primitiveOffset = 0;
    range.firstVertex = 0;
    range.transformOffset = 0;

    const VkAccelerationStructureBuildRangeInfoKHR* ranges[1] = { &range };

    // Build acceleration structure
    driver->vkCmdBuildAccelerationStructuresKHR(commandBuffer, 1, &buildInfo, ranges);

    driver->endSingleTimeCommands(commandBuffer);

    // Cleanup temporary buffers
    delete instancesBuffer;
    delete scratchBuffer;

    m_needsRebuild = false;
    os::Printer::log("VkTopLevelAS: Successfully built TLAS", ELL_INFORMATION);
    return true;
}

void VkTopLevelAS::clear() {
    instances.clear();
    m_needsRebuild = true;
}

bool VkTopLevelAS::needsRebuild() const {
    return m_needsRebuild;
}

} // namespace video
} // namespace irr

#endif // VK_ENABLE_RAYTRACING
#endif // _IRR_COMPILE_WITH_VULKAN_ 