#include "VkDriver.h"

#ifdef _IRR_COMPILE_WITH_VULKAN_

#if VK_ENABLE_RAYTRACING

#include "VkRaytracingDemo.h"
#include "VkHardwareBuffer.h"

namespace irr {
namespace video {

bool VkDriver::initializeRaytracing() {
    // Check if raytracing is supported by calling the base class
    if (!VkDriverBase::initializeRaytracing()) {
        raytracingSupported = false;
        return false;
    }
    
    // Create raytracing manager
    raytracingManager = new VkRaytracingManager(this);
    if (!raytracingManager || !raytracingManager->initialize()) {
        delete raytracingManager;
        raytracingManager = nullptr;
        raytracingSupported = false;
        return false;
    }
    raytracingSupported = true;
    // Create raytracing shaders
    raytracingShaders = new VkRaytracingShaders();
    if (!raytracingShaders->initialize(this)) {
        DP(("VkDriver: Failed to initialize raytracing shaders"));
        delete raytracingShaders;
        raytracingShaders = nullptr;
        return false;
    }
    

    
    // Set default raytracing mode
    rtMode = ERM_RT_DISABLED;
    

    // Log successful initialization
    DP(("VkDriver: Raytracing initialized successfully"));
    
    return true;
}

const VkPhysicalDeviceRayTracingPipelinePropertiesKHR& VkDriver::getRaytracingProperties() const {
    return VkDriverBase::rtPipelineProperties;
}

void VkDriver::shutdownRaytracing() {
    // Clean up raytracing demo
    if (raytracingDemo) {
        delete raytracingDemo;
        raytracingDemo = nullptr;
    }
    
    // Clean up raytracing shaders
    if (raytracingShaders) {
        delete raytracingShaders;
        raytracingShaders = nullptr;
    }
    
    // Clean up raytracing manager
    if (raytracingManager) {
        delete raytracingManager;
        raytracingManager = nullptr;
    }
    
    // Clean up raytracing resources
    raytracingSupported = false;
    rtMode = ERM_RT_DISABLED;
    
    // Call base class cleanup
    VkDriverBase::shutdownRaytracing();
}



VkCommandBuffer VkDriver::beginSingleTimeCommands() {
    // Follow the existing pattern from VkTexture.cpp and VkDriverBase.cpp
    VkCommandPool pool;
    VkCommandBuffer commandBuffer = mDevice->createCommandBuffer(VK_COMMAND_BUFFER_LEVEL_PRIMARY, pool, true);
    
    if (commandBuffer == VK_NULL_HANDLE) {
        DP(("VkDriver: Failed to create single-time command buffer"));
        return VK_NULL_HANDLE;
    }
    
    // Command buffer is already begun by createCommandBuffer with begin=true
    return commandBuffer;
}

void VkDriver::endSingleTimeCommands(VkCommandBuffer commandBuffer) {
    if (commandBuffer == VK_NULL_HANDLE) {
        return;
    }
    
    // End the command buffer
    VkResult result = vkEndCommandBuffer(commandBuffer);
    if (result != VK_SUCCESS) {
        DP(("VkDriver: Failed to end command buffer: %d", result));
        return;
    }
    
    // Submit and wait for completion
    VkSubmitInfo submitInfo = {};
    submitInfo.sType = VK_STRUCTURE_TYPE_SUBMIT_INFO;
    submitInfo.commandBufferCount = 1;
    submitInfo.pCommandBuffers = &commandBuffer;
    
    result = vkQueueSubmit(queueRender(), 1, &submitInfo, VK_NULL_HANDLE);
    if (result != VK_SUCCESS) {
        DP(("VkDriver: Failed to submit command buffer: %d", result));
        return;
    }
    
    // Wait for completion
    result = vkQueueWaitIdle(queueRender());
    if (result != VK_SUCCESS) {
        DP(("VkDriver: Failed to wait for queue idle: %d", result));
    }
    
    // Free the command buffer
    // Note: The command pool will be cleaned up by mDevice
}

// Test raytracing functionality (called by F1 key)
VkRaytracingDemo* VkDriver::getRaytracingDemo(const VkRaytracingDemo::TestRtPm& pm) {

    if (!raytracingDemo) {

        // Create raytracing demo for testing
        raytracingDemo = new VkRaytracingDemo(this);
        if (!raytracingDemo->initialize()) {
            DP(("VkDriver: Failed to initialize raytracing demo"));
            delete raytracingDemo;
            raytracingDemo = nullptr;
            // Don't fail initialization - demo is optional
        }
        //else {
        //    DP(("VkDriver: Raytracing demo initialized successfully"));

        //    // Test the raytracing system
        //    if (raytracingDemo->testRaytracing(pm)) {
        //        DP(("VkDriver: Raytracing system test passed"));
        //    }
        //    else {
        //        DP(("VkDriver: Raytracing system test failed"));
        //    }
        //}


    }
    return raytracingDemo; 
}
 

// Enable/disable raytracing
void VkDriver::setRayTracingMode(E_RAYTRACING_MODE mode) {
    if (!isRaytracingSupported()) {
        DP(("VkDriver: Cannot set raytracing mode - raytracing not supported"));
        return;
    }
    
    rtMode = mode;
    DP(("VkDriver: Raytracing mode set to %d", (int)mode));
}



bool VkDriver::isRaytracingSupported() const {
    return raytracingSupported && raytracingCaps.supported;
}

 

} // namespace video
} // namespace irr

#endif // VK_ENABLE_RAYTRACING
#endif // _IRR_COMPILE_WITH_VULKAN_ 