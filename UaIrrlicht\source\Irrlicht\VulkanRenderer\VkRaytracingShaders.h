#ifndef VK_RAYTRACING_SHADERS_H
#define VK_RAYTRACING_SHADERS_H

#include "irrlicht.h"

#if VK_ENABLE_RAYTRACING
#include "VkHeader.h"
namespace irr {
namespace video {

// Forward declarations
class VkDriver;

// Raytracing shader types
enum ERaytracingShaderType {
    ERTST_RAY_GENERATION = 0,
    ERTST_MISS_PRIMARY,
    ERTST_CLOSEST_HIT_PRIMARY,
    ERTST_MISS_SHADOW,
    ERTST_CLOSEST_HIT_SHADOW,
    ERTST_ANY_HIT,
    ERTST_INTERSECTION,
    ERTST_CALLABLE,
    ERTST_COUNT
};

// Raytracing shader info
struct SRaytracingShaderData {
    const char* name;
    ERaytracingShaderType type;
    const unsigned char* spirvData;
    size_t spirvSize;
    const char* entryPoint;
};

// Class to manage raytracing shader compilation and loading
class VkRaytracingShaders {
public:
    VkRaytracingShaders();
    ~VkRaytracingShaders();

    // Initialize with VkDriver
    bool initialize(VkDriver* driver);

    // Get shader data by type
    const SRaytracingShaderData* getShaderData(ERaytracingShaderType type) const;
    
    // Get all available shaders
    const SRaytracingShaderData* getAllShaders(u32& count) const;

    // Check if raytracing shaders are available
    bool areShadersAvailable() const;

    // Load shader module from SPIR-V data
    VkShaderModule createShaderModule(const SRaytracingShaderData* shaderData) const;

private:
    VkDriver* Driver;
    bool Initialized;
    
    // Shader data storage
    static const SRaytracingShaderData ShaderDatabase[];
    static const u32 ShaderCount;
};



 

} // namespace video
} // namespace irr

#endif // VK_ENABLE_RAYTRACING

#endif // VK_RAYTRACING_SHADERS_H 