#pragma once

#include "IrrCompileConfig.h"
#ifdef _IRR_COMPILE_WITH_VULKAN_

#if VK_ENABLE_RAYTRACING
#include "irrlicht.h"
#include "VkHeader.h"
#include "ERaytracingTypes.h"

namespace irr {
namespace video {

// Forward declarations
class VkDriver;
class VkHardwareBuffer;

//! Raytracing shader types
enum E_RAYTRACING_SHADER_TYPE {
    ERST_RAY_GENERATION = 0,
    ERST_MISS,
    ERST_CLOSEST_HIT,
    ERST_ANY_HIT,
    ERST_INTERSECTION,
    ERST_CALLABLE,
    ERST_COUNT
};

//! Raytracing shader info
struct SRaytracingShaderInfo {
    E_RAYTRACING_SHADER_TYPE type;
    const void* shaderCode;
    u32 shaderSize;
    const c8* entryPoint;
    u32 groupIndex;
    
    SRaytracingShaderInfo() : type(ERST_RAY_GENERATION), shaderCode(nullptr), 
                             shaderSize(0), entryPoint("main"), groupIndex(0) {}
};

//! Shader Binding Table (SBT) wrapper
//! Manages the shader handles and their GPU memory layout
class VkShaderBindingTable {
private:
    VkDriver* driver;
    VkHardwareBuffer* buffer;
    VkStridedDeviceAddressRegionKHR region;
    u32 handleSize;
    u32 handleCount;
    u32 stride;
    bool isValid;

public:
    VkShaderBindingTable(VkDriver* driver);
    ~VkShaderBindingTable();

    //! Initialize the SBT with shader handles
    bool initialize(const void* handles, u32 handleCount, u32 handleSize);

    //! Get the strided device address region for tracing
    const VkStridedDeviceAddressRegionKHR& getRegion() const { return region; }

    //! Check if SBT is valid
    bool isValidSBT() const { return isValid && buffer != nullptr; }

    //! Destroy the SBT
    void destroy();
};

//! Raytracing pipeline wrapper
//! Manages the complete raytracing pipeline including shaders and SBTs
class VkRaytracingPipeline {
private:
    VkDriver* driver;
    VkPipeline pipeline;
    VkPipelineLayout pipelineLayout;
    
    // Descriptor set layouts
    core::array<VkDescriptorSetLayout> descriptorSetLayouts;
    
    // Shader modules
    core::array<VkShaderModule> shaderModules;
    core::array<VkPipelineShaderStageCreateInfo> shaderStages;
    core::array<VkRayTracingShaderGroupCreateInfoKHR> shaderGroups;
    
    // Shader Binding Tables
    VkShaderBindingTable* raygenSBT;
    VkShaderBindingTable* missSBT;
    VkShaderBindingTable* hitSBT;
    VkShaderBindingTable* callableSBT;
    
    u32 shaderHandleSize;
    bool isValid;

public:
    VkRaytracingPipeline(VkDriver* driver);
    ~VkRaytracingPipeline();

    //! Add a shader to the pipeline
    bool addShader(const SRaytracingShaderInfo& shaderInfo);

    //! Set descriptor set layouts for the pipeline
    void setDescriptorSetLayouts(const VkDescriptorSetLayout* layouts, u32 layoutCount);

    //! Build the complete raytracing pipeline
    bool build();

    //! Bind the pipeline for raytracing
    void bind(VkCommandBuffer commandBuffer);

    //! Trace rays with the current pipeline
    void traceRays(VkCommandBuffer commandBuffer, u32 width, u32 height, u32 depth = 1);

    //! Check if pipeline is valid
    bool isValidPipeline() const { return isValid && pipeline != VK_NULL_HANDLE; }

    //! Get the Vulkan pipeline handle
    VkPipeline getPipeline() const { return pipeline; }
    
    //! Get the pipeline layout
    VkPipelineLayout getPipelineLayout() const { return pipelineLayout; }

    //! Destroy the pipeline
    void destroy();

private:
    //! Create shader module from SPIR-V code
    VkShaderModule createShaderModule(const void* code, u32 size);

    //! Setup shader binding tables
    bool setupShaderBindingTables();

    //! Get shader group type from shader type
    VkRayTracingShaderGroupTypeKHR getGroupType(E_RAYTRACING_SHADER_TYPE shaderType);
};

//! Simple raytracing material for basic ray/material interaction
struct SRaytracingMaterial {
    core::vector3df albedo;
    f32 metallic;
    f32 roughness;
    f32 emissive;
    u32 materialID;
    
    SRaytracingMaterial() : albedo(0.7f, 0.7f, 0.7f), metallic(0.0f), 
                           roughness(0.5f), emissive(0.0f), materialID(0) {}
};

} // namespace video
} // namespace irr

#endif // VK_ENABLE_RAYTRACING
#endif // _IRR_COMPILE_WITH_VULKAN_ 