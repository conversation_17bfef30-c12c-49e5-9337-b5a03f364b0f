// RaytracingShared.h - Common definitions for raytracing shaders
// This file is included by both C++ code and GLSL shaders

#ifndef RAYTRACING_SHARED_H
#define RAYTRACING_SHARED_H

#ifdef __cplusplus
// C++ definitions
#include <glm/glm.hpp>
#include <glm/gtc/matrix_transform.hpp>

namespace irr {
namespace video {

// Define type aliases for C++ - scoped to this namespace only
using vec3 = glm::vec3;
using mat4 = glm::mat4;
using vec2 = glm::vec2;
using vec4 = glm::vec4;

#else
// GLSL already has vec2, vec3, vec4, mat4, float as built-in types
#endif

// Raytracing descriptor set bindings
#define RT_BINDING_OUTPUT_IMAGE     0
#define RT_BINDING_ACCELERATION_STRUCTURE 1
#define RT_BINDING_CAMERA_UBO      2
#define RT_BINDING_SKY_UBO         3
#define RT_BINDING_LIGHT_UBO       4

// Geometry descriptor set bindings (set 1)
#define RT_BINDING_VERTEX_BUFFER   0
#define RT_BINDING_INDEX_BUFFER    1
#define RT_BINDING_NORMAL_BUFFER   2
#define RT_BINDING_MATERIAL_BUFFER 3

// Ray payload locations
#define RT_PAYLOAD_PRIMARY         0
#define RT_PAYLOAD_SHADOW          1

// Raytracing limits
#define RT_MAX_RECURSION_DEPTH     3

// Shader binding table offsets
#define RT_SBT_RAYGEN_GROUP        0
#define RT_SBT_MISS_GROUP          0
#define RT_SBT_SHADOW_MISS_GROUP   1
#define RT_SBT_HIT_GROUP           0

#ifndef __cplusplus
// GLSL-only definitions

// Ray payload structure for primary rays
struct RayPayload {
    vec4 colorAndDist;    // rgb = color, w = hit distance (-1 if miss)
    vec4 normalAndObjId;  // xyz = world normal, w = object ID
    int recursionDepth;   // Current recursion depth for reflections
    float padding[3];     // Padding for alignment
};

// Shadow ray payload structure
struct ShadowRayPayload {
    bool inShadow;
};

#endif // __cplusplus

// Common structures that work for both C++ and GLSL
struct CameraUBO {
    mat4 viewMatrix;
    mat4 projMatrix;
    mat4 viewInverse;
    mat4 projInverse;
    vec3 cameraPos;
    float nearPlane;
    vec3 cameraDir;
    float farPlane;
    vec3 cameraUp;
    float fovY;
    vec3 cameraRight;
    float time;
    float padding1;
    float padding2;
    float padding3;
    float padding4;
};

// Sky/environment uniform buffer structure
struct SkyUBO {
    vec3 skyColor;
    vec3 horizonColor;
    vec3 sunDirection;
    float sunIntensity;
};

// Light uniform buffer structure
struct LightUBO {
    vec3 lightDirection;
    vec3 lightColor;
    float lightIntensity;
    vec3 ambientColor;
};

// Material structure (matches SimpleMaterial in C++ code)
struct Material {
    vec4 diffuseColor;   // RGBA diffuse color
    vec4 specularColor;  // RGBA specular color
    vec4 emissionColor;  // RGBA emission color
    float roughness;     // Roughness value
    float metallic;      // Metallic value
    vec2 padding;        // Padding for alignment
};

// Vertex attribute structure
struct VertexAttribute {
    vec3 position;
    vec3 normal;
    vec2 uv;
    vec4 color;
};

#ifdef __cplusplus
} // namespace video
} // namespace irr
#endif

#endif // RAYTRACING_SHARED_H 