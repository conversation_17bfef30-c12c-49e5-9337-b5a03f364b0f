#version 460
#extension GL_EXT_ray_tracing : enable  // 启用光线追踪扩展
#extension GL_GOOGLE_include_directive : require  // 启用#include指令

#include "../shared_with_shaders.h"  // 包含共享定义

// 场景加速结构(用于光线追踪)
layout(set = SWS_SCENE_AS_SET, binding = SWS_SCENE_AS_BINDING) uniform accelerationStructureEXT Scene;
// 输出图像(存储最终渲染结果)
layout(set = SWS_RESULT_IMAGE_SET, binding = SWS_RESULT_IMAGE_BINDING, rgba8) uniform image2D ResultImage;

// 相机参数统一缓冲区
layout(set = SWS_CAMDATA_SET, binding = SWS_CAMDATA_BINDING, std140) uniform AppData {
    UniformParams Params;  // 包含相机位置、方向、FOV等参数
};

// 定义两种射线payload:
layout(location = SWS_LOC_PRIMARY_RAY) rayPayloadEXT RayPayload PrimaryRay;  // 主射线payload
layout(location = SWS_LOC_SHADOW_RAY) rayPayloadEXT ShadowRayPayload ShadowRay;  // 阴影射线payload

const float kBunnyRefractionIndex = 1.0f / 1.31f; // 兔子材质的折射率(冰)

/**
 * 计算相机射线方向
 * @param screenUV 屏幕UV坐标(-1到1)
 *   - x: 水平方向，-1表示最左，1表示最右
 *   - y: 垂直方向，-1表示最下，1表示最上
 * @param aspect 屏幕宽高比(宽度/高度)
 * @return 归一化的射线方向
 * 
 * 该函数根据相机参数和屏幕坐标计算从相机出发的射线方向
 * 1. 获取相机坐标系基向量(右、上方向)
 * 2. 根据FOV计算视平面宽度
 * 3. 调整基向量长度考虑宽高比
 * 4. 组合得到最终射线方向
 */
vec3 CalcRayDir(vec2 screenUV, float aspect) {
    // 获取相机坐标系基向量
    vec3 u = Params.camSide.xyz;  // 相机右向量
    vec3 v = Params.camUp.xyz;    // 相机上向量

    // 计算视平面宽度(基于FOV)
    const float planeWidth = tan(Params.camNearFarFov.z * 0.5f);

    // 调整基向量长度
    u *= (planeWidth * aspect);  // 水平方向考虑宽高比
    v *= planeWidth;             // 垂直方向

    // 计算最终射线方向: 相机前方向 + UV偏移
    const vec3 rayDir = normalize(Params.camDir.xyz + (u * screenUV.x) - (v * screenUV.y));
    return rayDir;
}

/**
 * 主着色器函数 - 生成光线并进行追踪
 * 
 * 这是光线追踪的入口函数，负责:
 * 1. 计算当前像素对应的射线
 * 2. 进行递归光线追踪(反射/折射)
 * 3. 计算最终颜色并存储结果
 * 
 * 工作流程:
 * 1. 计算像素坐标和屏幕UV
 * 2. 设置射线起点(相机位置)和方向
 * 3. 进入递归追踪循环(最多SWS_MAX_RECURSION次)
 *    - 追踪主射线
 *    - 处理命中结果(背景/几何体)
 *    - 根据材质类型处理反射/折射
 * 4. 转换颜色空间并存储结果
 * 
 * 注意:
 * - 使用gl_LaunchIDEXT获取当前像素坐标
 * - 使用gl_LaunchSizeEXT获取屏幕尺寸
 * - 递归深度由SWS_MAX_RECURSION控制
 */
void main() {
    // 计算当前像素坐标和屏幕尺寸
    const vec2 curPixel = vec2(gl_LaunchIDEXT.xy);  // 当前像素坐标
    const vec2 bottomRight = vec2(gl_LaunchSizeEXT.xy - 1);  // 屏幕右下角坐标

    // 将像素坐标转换为标准化UV坐标(-1到1)
    const vec2 uv = (curPixel / bottomRight) * 2.0f - 1.0f;

    // 计算屏幕宽高比
    const float aspect = float(gl_LaunchSizeEXT.x) / float(gl_LaunchSizeEXT.y);

    // 设置射线起点(相机位置)和方向
    vec3 origin = Params.camPos.xyz;
    vec3 direction = CalcRayDir(uv, aspect);

    // 设置射线标志:
    const uint rayFlags = gl_RayFlagsOpaqueEXT;  // 主射线标志(不透明物体)
    const uint shadowRayFlags = gl_RayFlagsOpaqueEXT | gl_RayFlagsTerminateOnFirstHitEXT;  // 阴影射线标志(遇到第一个命中即终止)

    const uint cullMask = 0xFF;  // 剔除掩码(所有几何体)
    const uint stbRecordStride = 1;  // 着色器绑定表记录步长

    // 设置射线距离范围
    const float tmin = 0.0f;  // 最小距离
    const float tmax = Params.camNearFarFov.y;  // 最大距离(远平面)

    vec3 finalColor = vec3(0.0f);  // 初始化最终颜色

    // 递归光线追踪循环(最多SWS_MAX_RECURSION次)
    for (int i = 0; i < SWS_MAX_RECURSION; ++i) {
        // 追踪主射线
        traceRayEXT(
            Scene,                    // 场景加速结构
            rayFlags,                 // 射线标志(不透明物体)
            cullMask,                 // 剔除掩码(所有几何体)
            SWS_PRIMARY_HIT_SHADERS_IDX,  // 命中着色器索引
            stbRecordStride,          // 着色器绑定表记录步长
            SWS_PRIMARY_MISS_SHADERS_IDX, // 未命中着色器索引
            origin,                   // 射线起点(世界坐标)
            tmin,                     // 最小距离(避免自相交)
            direction,                // 射线方向(归一化)
            tmax,                     // 最大距离(远平面)
            SWS_LOC_PRIMARY_RAY       // payload位置
        );

        // 从payload获取命中颜色和距离
        const vec3 hitColor = PrimaryRay.colorAndDist.rgb;
        const float hitDistance = PrimaryRay.colorAndDist.w;

        // 检查是否命中背景
        if (hitDistance < 0.0f) {
            // 命中背景(环境贴图)，累加颜色并终止递归
            finalColor += hitColor;
            break;
        } else {
            // 命中几何体，获取法线和物体ID
            const vec3 hitNormal = PrimaryRay.normalAndObjId.xyz;
            const float objectId = PrimaryRay.normalAndObjId.w;

            // 计算命中位置
            const vec3 hitPos = origin + direction * hitDistance;

            // 根据物体ID处理不同材质
            if (objectId == OBJECT_ID_TEAPOT) {
                // 茶壶材质(镜面反射)
                // 调整新射线起点(防止自相交)，计算反射方向
                origin = hitPos + hitNormal * 0.001f;
                direction = reflect(direction, hitNormal);
            } else if (objectId == OBJECT_ID_BUNNY) {
                // 兔子材质(折射)
                const float NdotD = dot(hitNormal, direction);  // 计算法线和入射方向点积

                vec3 refrNormal = hitNormal;
                float refrEta;  // 折射率

                // 判断是从外部还是内部进入
                if(NdotD > 0.0f) {
                    // 从外部进入，翻转法线并使用外部折射率
                    refrNormal = -hitNormal;
                    refrEta = 1.0f / kBunnyRefractionIndex;
                } else {
                    // 从内部进入，保持法线方向并使用内部折射率
                    refrNormal = hitNormal;
                    refrEta = kBunnyRefractionIndex;
                }

                // 调整新射线起点，计算折射方向
                origin = hitPos + direction * 0.001f;
                direction = refract(direction, refrNormal, refrEta);
            } else {
                // 漫反射材质(简单Lambertian着色)
                const vec3 toLight = normalize(Params.sunPosAndAmbient.xyz);  // 光源方向
                const vec3 shadowRayOrigin = hitPos + hitNormal * 0.001f;  // 阴影射线起点

                // 追踪阴影射线(检查是否被遮挡)
                traceRayEXT(Scene,
                            shadowRayFlags,
                            cullMask,
                            SWS_SHADOW_HIT_SHADERS_IDX,
                            stbRecordStride,
                            SWS_SHADOW_MISS_SHADERS_IDX,
                            shadowRayOrigin,
                            0.0f,
                            toLight,
                            tmax,
                            SWS_LOC_SHADOW_RAY);

                // 计算光照: 如果有遮挡则使用环境光，否则计算漫反射
                const float lighting = (ShadowRay.distance > 0.0f) ? 
                    Params.sunPosAndAmbient.w :  // 使用环境光
                    max(Params.sunPosAndAmbient.w, dot(hitNormal, toLight));  // 漫反射+环境光

                // 累加最终颜色并终止递归(漫反射不继续追踪)
                finalColor += hitColor * lighting;
                break;
            }
        }
    }

    // 将线性空间颜色转换为sRGB并存储到输出图像
    imageStore(ResultImage, ivec2(gl_LaunchIDEXT.xy), vec4(LinearToSrgb(finalColor), 1.0f));
}
