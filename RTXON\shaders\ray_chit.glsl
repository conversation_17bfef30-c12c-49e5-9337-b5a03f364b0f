#version 460
#extension GL_EXT_ray_tracing : enable  // 启用光线追踪扩展
#extension GL_GOOGLE_include_directive : require  // 启用#include指令
#extension GL_EXT_nonuniform_qualifier : require  // 启用动态数组索引

#include "../shared_with_shaders.h"  // 包含共享定义

/**
 * 主射线命中着色器 - 处理射线与几何体的交点
 * 主要功能:
 * 1. 获取材质ID
 * 2. 插值顶点属性(法线、UV)
 * 3. 采样纹理
 * 4. 填充射线payload数据
 */

// 材质ID缓冲区(每个图元对应一个材质ID)
layout(set = SWS_MATIDS_SET, binding = 0, std430) readonly buffer MatIDsBuffer {
    uint MatIDs[];  // 材质ID数组
} MatIDsArray[];  // 每个实例一个缓冲区

// 顶点属性缓冲区(位置、法线、UV等)
layout(set = SWS_ATTRIBS_SET, binding = 0, std430) readonly buffer AttribsBuffer {
    VertexAttribute VertexAttribs[];  // 顶点属性数组
} AttribsArray[];  // 每个实例一个缓冲区

// 面索引缓冲区(三角形顶点索引)
layout(set = SWS_FACES_SET, binding = 0, std430) readonly buffer FacesBuffer {
    uvec4 Faces[];  // 面数据(每个面3个顶点索引+填充)
} FacesArray[];  // 每个实例一个缓冲区

// 纹理数组(每个材质对应一个纹理)
layout(set = SWS_TEXTURES_SET, binding = 0) uniform sampler2D TexturesArray[];

// 输入输出定义
layout(location = SWS_LOC_PRIMARY_RAY) rayPayloadInEXT RayPayload PrimaryRay;  // 主射线payload
                                       hitAttributeEXT vec2 HitAttribs;  // 命中点属性(重心坐标)

/**
 * 主着色器函数 - 处理射线命中
 */
void main() {
    // 计算重心坐标(用于属性插值)
    const vec3 barycentrics = vec3(1.0f - HitAttribs.x - HitAttribs.y, HitAttribs.x, HitAttribs.y);

    // 获取当前图元的材质ID
    const uint matID = MatIDsArray[nonuniformEXT(gl_InstanceCustomIndexEXT)].MatIDs[gl_PrimitiveID];

    // 获取当前三角形的面数据(顶点索引)
    const uvec4 face = FacesArray[nonuniformEXT(gl_InstanceCustomIndexEXT)].Faces[gl_PrimitiveID];

    // 获取三个顶点的属性数据
    VertexAttribute v0 = AttribsArray[nonuniformEXT(gl_InstanceCustomIndexEXT)].VertexAttribs[int(face.x)];
    VertexAttribute v1 = AttribsArray[nonuniformEXT(gl_InstanceCustomIndexEXT)].VertexAttribs[int(face.y)];
    VertexAttribute v2 = AttribsArray[nonuniformEXT(gl_InstanceCustomIndexEXT)].VertexAttribs[int(face.z)];

    // 插值顶点属性:
    // 1. 插值法线向量并归一化
    const vec3 normal = normalize(BaryLerp(v0.normal.xyz, v1.normal.xyz, v2.normal.xyz, barycentrics));
    // 2. 插值UV坐标
    const vec2 uv = BaryLerp(v0.uv.xy, v1.uv.xy, v2.uv.xy, barycentrics);

    // 根据材质ID采样纹理
    const vec3 texel = textureLod(TexturesArray[nonuniformEXT(matID)], uv, 0.0f).rgb;

    // 获取物体ID(来自实例自定义索引)
    const float objId = float(gl_InstanceCustomIndexEXT);

    // 填充射线payload:
    PrimaryRay.colorAndDist = vec4(texel, gl_HitTEXT);  // 颜色和命中距离
    PrimaryRay.normalAndObjId = vec4(normal, objId);    // 法线和物体ID
}
