#pragma once
#include "IrrCompileConfig.h"
#ifdef _IRR_COMPILE_WITH_VULKAN_
#define USE_IMGUI   1



#include "VkHeader.h"

#include <array>
#include <cmath>

#include "irrMap.h"
#include "base/VulkanSwapChain.hpp"
#include "base/VulkanTexture.hpp"
#include "VkShaderMan/VkFxBase.h"

//#include "VkShaderMan/VkFxConstantBuffer.h"

#include "../Helpers/UpVtxIdxCache.h"
#include "VkTexture.h"

#include "VkHardwareBuffer.h"
#include "../CNullDriver.h"
#include "IMaterialRendererServices.h"


#include "SIrrCreationParameters.h"
#if USE_IMGUI
namespace vks {
	class UIOverlay;
}
#endif
namespace irr {
namespace video {

class VkDriverBase : public CNullDriver, public IMaterialRendererServices
{
public:



	VkDriverBase(const irr::SIrrlichtCreationParameters& params, io::IFileSystem* io);
	virtual ~VkDriverBase();
	virtual void updateUI(float deltaTime) override;
	void onRelease();

	
	//const VkCommandBuffer& getCurCmdBuf() { return drawCmdBuffers[currentBuffer]; }
	VkPipelineShaderStageCreateInfo loadShader(const uint32_t * code, const uint32_t length, VkShaderStageFlagBits stage);
	VkCmdBuf allocCmdBuf(VkCommandBufferLevel level, bool begin = false);
	void freeCmdBuf(VkCmdBuf& cb);
	VkCommandBuffer currentCmdBuffer(int paraId = -1) {
		if (paraId >= 0)
			return threadData[paraId % threadPool.getThreadCount()].commandBuffer[0];
		if (_currentRT)
			return _currentRT->rtCmdBuf();
		else
			return drawCmdBuffers[currentBuffer];
	}
	// Find and create a compute capable device queue
	VkQueue getComputeQueue();
	VkCommandPool getComputeCommandPool() {return cmdPoolCompute; }

	//VkCommandBuffer vk(VkCommandBufferLevel level, bool begin, bool compute = false);
	//void vkFlushCommandBuffer(VkCommandBuffer commandBuffer, VkQueue queue, bool free, bool compute = false);
	
	virtual ITexture* getPickRT(int w, int h) override;


	void createPipelineCache();
	bool isOffScreen() { return OffScreen; }
	virtual bool toggleUI() override { 
		renderUI = !renderUI; 
		renderCursor = renderUI;
		return renderUI;
	}
	virtual bool supportDynStateExt() override { return VK_USE_DYNAMIC_STATE_EXT; }
public:
	irr::SIrrlichtCreationParameters CreatePms;


	vks::VulkanDevice *mDevice = nullptr;

	VkDevice Device = VK_NULL_HANDLE;
	VulkanSwapChain SwapChain;

#if USE_IMGUI

	vks::UIOverlay* UIOverlay{};

	std::function<void(vks::UIOverlay*)> OnUpdateUIOverlay;
#endif
	bool isUIEventHandled();
	bool uiReady = false;

	// Depth buffer format (selected during Vulkan initialization)
	VkFormat depthFormat;
	VkTexture* ssaoRT{};

	// Stores physical device properties (for e.g. checking device limits)
	VkPhysicalDeviceProperties deviceProperties;
	// Stores the features available on the selected physical device (for e.g. checking if a feature is available)
	VkPhysicalDeviceFeatures deviceFeatures;

#if VK_ENABLE_RAYTRACING
	// Raytracing capabilities and features
	struct VkRaytracingCapabilities {
		bool supported = false;
		bool pipelineSupported = false;
		bool querySupported = false;
		u32 maxRecursionDepth = 0;
		u32 shaderGroupHandleSize = 0;
		u32 maxShaderGroupStride = 0;
		u32 maxGeometryCount = 0;
		u32 maxInstanceCount = 0;
	} raytracingCaps;

	// Raytracing feature structures
	VkPhysicalDeviceRayTracingPipelinePropertiesKHR rtPipelineProperties{};
	VkPhysicalDeviceAccelerationStructurePropertiesKHR accelStructProperties{};
	VkPhysicalDeviceRayTracingPipelineFeaturesKHR rtPipelineFeatures{};
	VkPhysicalDeviceAccelerationStructureFeaturesKHR accelStructFeatures{};
	VkPhysicalDeviceBufferDeviceAddressFeaturesKHR bufferDeviceAddressFeatures{};

	// Raytracing function pointers
	PFN_vkCreateAccelerationStructureKHR vkCreateAccelerationStructureKHR = nullptr;
	PFN_vkDestroyAccelerationStructureKHR vkDestroyAccelerationStructureKHR = nullptr;
	PFN_vkGetAccelerationStructureBuildSizesKHR vkGetAccelerationStructureBuildSizesKHR = nullptr;
	PFN_vkCmdBuildAccelerationStructuresKHR vkCmdBuildAccelerationStructuresKHR = nullptr;
	PFN_vkCreateRayTracingPipelinesKHR vkCreateRayTracingPipelinesKHR = nullptr;
	PFN_vkGetRayTracingShaderGroupHandlesKHR vkGetRayTracingShaderGroupHandlesKHR = nullptr;
	PFN_vkCmdTraceRaysKHR vkCmdTraceRaysKHR = nullptr;
	PFN_vkGetAccelerationStructureDeviceAddressKHR vkGetAccelerationStructureDeviceAddressKHR = nullptr;
	PFN_vkGetBufferDeviceAddressKHR vkGetBufferDeviceAddressKHR = nullptr;
#endif

	// Global render pass for frame buffer writes

	
	VkTexture* _currentRT = nullptr;// , *mStaticBgTex;
	VkRenderPass curRenderPass(bool system = false);
	VkFramebuffer curFrameBuffer(bool system = false);
	// Pipeline cache object
	VkPipelineCache PipelineCache;


	VkSampleCountFlagBits getMaxUsableSampleCount();

	virtual void paraRenderBegin(u32 paraCount) override;
	virtual void paraRenderEnd() override ;
	virtual void onParaThreadBegin(int paraId) override;
	virtual void onParaThreadEnd(int paraId) override;
protected:
	VkRenderPass RenderPass1, //1pass
		RenderPass;//2pass
	
	struct
	{
		VkImage image{};
		VkDeviceMemory mem{};
		VkImageView view{};
	} depthStencil;

	VkResult createInstance(bool enableValidation);
	void initSwapchain(void* hwnd);
	void createCmdBufs();

	void destroyCommandBuffers();



	//void buildCommandBuffers();

	void setupDepthStencil();

	void setupRenderPass(bool isExtDev);

	void setupFrameBuffer();
	struct FrameBufferAttachment {
		VkImage image;
		VkDeviceMemory mem;
		VkImageView view;
		VkFormat format;
	} ;
	void createAttachment(VkFormat format, VkImageUsageFlags usage, FrameBufferAttachment* attachment);
	void destroyAttachment(FrameBufferAttachment* attachment);
	
	
	void prepareParaRenderRes();

#if VK_ENABLE_RAYTRACING
	// Raytracing initialization and cleanup
	bool initializeRaytracing();
	void shutdownRaytracing();
	bool checkRaytracingSupport();
	void loadRaytracingFunctions();
#endif

	struct ThreadData {
		VkCommandPool commandPool;
		// One command buffer per render object
		std::vector<VkCommandBuffer> commandBuffer;
		bool needBegin = false;
		VkCommandBufferInheritanceInfo inheritanceInfo;
		// One push constant block per render object
		//std::vector<ThreadPushConstantBlock> pushConstBlock;
		// Per object information (position, rotation, etc.)
		//std::vector<ObjectData> objectData;
	};
	std::vector<ThreadData> threadData;

	void prepareUI();

	void drawUI(const VkCommandBuffer commandBuffer);

	// Vulkan instance, stores all per-application states
	VkInstance instance;
	// Physical device (GPU) that Vulkan will ise
	VkPhysicalDevice physicalDevice;

	// Stores all available memory (type) properties for the physical device
	VkPhysicalDeviceMemoryProperties deviceMemoryProperties;
	/**
	* Set of physical device features to be enabled for this example (must be set in the derived constructor)
	*
	* @note By default no phyiscal device features are enabled
	*/
	VkPhysicalDeviceFeatures enabledFeatures{};
	/** @brief Set of device extensions to be enabled for this example (must be set in the derived constructor) */
	std::vector<const char*> enabledDeviceExtensions;
	std::vector<const char*> enabledInstanceExtensions;
	/** @brief Logical device, application's view of the physical device (GPU) */
	//VkDevice device;
	// Handle to the device graphics queue that command buffers are submitted to
	//VkQueue queue;

	// Command buffer pool
	VkCommandPool cmdPool{};  //present pool
	VkCommandPool cmdPoolCompute{};
	/** @brief Pipeline stages used to wait at for graphics queue submissions */
	VkPipelineStageFlags submitPipelineStages = VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT;
	// Contains command buffers and semaphores to be presented to the queue
	VkSubmitInfo submitInfo;
	// Command buffers used for rendering
	std::vector<VkCommandBuffer> drawCmdBuffers;
	VkCommandBuffer curParaCmb = VK_NULL_HANDLE;
	u32 paraBeginThreadNum = 0;
	u32 paraItemNum = 0;
	// List of available frame buffers (same as number of swap chain images)
	std::vector<VkFramebuffer>frameBuffers; 
	VkFramebuffer fbExt{};
	// Active frame buffer index
	uint32_t currentBuffer = 0;
	// Descriptor set pool
	VkDescriptorPool descriptorPool = VK_NULL_HANDLE;
	// List of shader modules created (stored for cleanup)
	std::vector<VkShaderModule> shaderModules;

	// Wraps the swap chain to present images (framebuffers) to the windowing system
	//VulkanSwapChain swapChain;
	// Synchronization semaphores
	struct {
		// Swap chain image presentation
		VkSemaphore presentComplete;
		// Command buffer submission and execution
		VkSemaphore renderComplete;
	} semaphores;
	std::vector<VkFence> waitFences;

	bool isExtDev = false;
	bool OffScreen= false;
	bool inRenderPass = false;
	int curSubPass = 0;
#if defined(_WIN32)
	//HWND window;
	//HINSTANCE windowInstance;
#elif defined(VK_USE_PLATFORM_ANDROID_KHR)
	// true if application has focused, false if moved to background
	bool focused = false;
	struct TouchPos {
		int32_t x;
		int32_t y;
	} touchPos;
	bool touchDown = false;
	double touchTimer = 0.0;
	int64_t lastTapTime = 0;
	/** @brief Product model and manufacturer of the Android device (via android.Product*) */
	std::string androidProduct;
#endif



	public:
		VkQueue queueCompute() {		return _queueCompute;	}
		VkQueue queueCopy() { return queueRender(); }
		VkQueue queueRender()
		{ 
			if (mDevice->isMultiQueue) {
				if (std::this_thread::get_id() == mDevice->rendererTid)
					return _queueRender;
				else
				{
					//DP(("queue NOT RENDER THREAD							!!!!!!"));
					return _queueRenderT1;
				}
			}else
			return _queueRender;
#
		}

		std::mutex commandPoolMutex;

		VkSampleCountFlagBits getSampleCountFlagBits() { return SampleCount; }
	protected:
		VkQueue _queueRender = VK_NULL_HANDLE, _queueRenderT1 = VK_NULL_HANDLE, _queueCopy = VK_NULL_HANDLE, _queueCompute = VK_NULL_HANDLE;
		FrameBufferAttachment fbaHdr{};


		void copyVkImage(uint32_t width, uint32_t height, VkImage srcImage, VkImageLayout srclayoutFrom, VkImageLayout srclayoutTo,
			VkImage dstImage, VkImageLayout dstlayoutFrom, VkImageLayout dstlayoutTo);

		VkSampleCountFlagBits SampleCount= VK_SAMPLE_COUNT_1_BIT;

};


}
}
#endif 