#pragma once

#include "IrrCompileConfig.h"
#ifdef _IRR_COMPILE_WITH_VULKAN_

#include "VkHeader.h"
#if VK_ENABLE_RAYTRACING

#include "irrTypes.h"
#include "irrArray.h"
#include "matrix4.h"
#include "vector3d.h"
#include "SColor.h"

namespace irr {
namespace scene {
    class ISceneNode;
    class IMeshSceneNode;
    class ISceneManager;
}
namespace video {

// Forward declarations
class VkDriver;
class VkRaytracingManager;
class VkBottomLevelAS;

//! Material properties for raytracing
struct SRaytracingMaterial2 {
    core::vector3df diffuseColor;
    f32 roughness;
    f32 metallic;
    f32 reflectivity;
    u32 materialType; // 0=diffuse, 1=mirror, 2=glass, etc.
    
    SRaytracingMaterial2() 
        : diffuseColor(0.8f, 0.8f, 0.8f)
        , roughness(0.5f)
        , metallic(0.0f)
        , reflectivity(0.0f)
        , materialType(0)
    {}
};

//! Raytracing object instance
struct SRaytracingObject {
    scene::IMeshSceneNode* sceneNode;
    VkBottomLevelAS* blas;
    u32 tlasInstanceIndex;
    SRaytracingMaterial2 material;
    bool needsUpdate;
    
    SRaytracingObject()
        : sceneNode(nullptr)
        , blas(nullptr)
        , tlasInstanceIndex(~0u)
        , needsUpdate(true)
    {}
};

//! Raytracing scene manager
//! Manages scene nodes for raytracing acceleration structures
class VkRaytracingSceneManager {
private:
    VkDriver* driver;
    VkRaytracingManager* rtManager;
    scene::ISceneManager* sceneManager;
    
    core::array<SRaytracingObject> rtObjects;
    bool needsTLASRebuild;
    
public:
    VkRaytracingSceneManager(VkDriver* driver, scene::ISceneManager* sceneManager);
    ~VkRaytracingSceneManager();
    
    //! Initialize the raytracing scene manager
    bool initialize();
    
    //! Shutdown and cleanup
    void shutdown();
    
    //! Add a scene node to raytracing
    u32 addSceneNode(scene::IMeshSceneNode* node, const SRaytracingMaterial2& material = SRaytracingMaterial2());
    
    //! Remove a scene node from raytracing
    void removeSceneNode(u32 objectIndex);
    
    //! Update all raytracing objects (call before rendering)
    void updateObjects();
    
    //! Create a cube mirror scene node and add it to raytracing
    scene::IMeshSceneNode* createCubeMirror(const core::vector3df& position = core::vector3df(0,0,0),
                                           const core::vector3df& rotation = core::vector3df(0,0,0),
                                           const core::vector3df& scale = core::vector3df(1,1,1),
                                           f32 size = 10.0f);
    
    //! Create a reflective sphere scene node and add it to raytracing
    scene::IMeshSceneNode* createReflectiveSphere(const core::vector3df& position = core::vector3df(0,0,0),
                                                  const core::vector3df& rotation = core::vector3df(0,0,0),
                                                  const core::vector3df& scale = core::vector3df(1,1,1),
                                                  f32 radius = 5.0f,
                                                  s32 polyCount = 16,
                                                  const core::vector3df& color = core::vector3df(0.8f, 0.2f, 0.2f));
    
    //! Get raytracing object count
    u32 getObjectCount() const { return rtObjects.size(); }
    
    //! Get raytracing object by index
    const SRaytracingObject* getObject(u32 index) const;
    
    //! Check if needs TLAS rebuild
    bool needsRebuild() const { return needsTLASRebuild; }
    
private:
    //! Extract mesh data from scene node
    bool extractMeshData(scene::IMeshSceneNode* node, 
                        core::array<u8>& vertexData, 
                        core::array<u32>& indexData,
                        u32& vertexCount, u32& indexCount);
    
    //! Create BLAS from mesh data
    VkBottomLevelAS* createBLAS(const core::array<u8>& vertexData, 
                               const core::array<u32>& indexData,
                               u32 vertexCount, u32 indexCount);
    
    //! Update TLAS with current objects
    void rebuildTLAS();
};

} // namespace video
} // namespace irr

#endif // VK_ENABLE_RAYTRACING
#endif // _IRR_COMPILE_WITH_VULKAN_
