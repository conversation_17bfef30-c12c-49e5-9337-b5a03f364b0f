
#ifndef __C_DG_HARDWARE_BUFFER_H_INCLUDED__
#define __C_DG_HARDWARE_BUFFER_H_INCLUDED__

#include "IrrCompileConfig.h"
#ifdef _IRR_COMPILE_WITH_VULKAN_

#include "IHardwareBuffer.h"
#include "VkHeader.h"


namespace irr
{
namespace video
{

class VkDriver;

class VkHardwareBuffer : public IHardwareBuffer
{
	// Implementation of public methods
public:
	VkHardwareBuffer(VkDriver* driver, E_HARDWARE_BUFFER_TYPE type, E_HARDWARE_BUFFER_ACCESS accessType, 
									u32 size, u32 flags, const void* initialData = 0);

	~VkHardwareBuffer();

	//! Lock function.
	virtual void* lock(bool needRead = false, u32 offset = 0, u32 length = 0);

	//! Unlock function. Must be called after a lock() to the buffer.
	virtual void unlock();

	//! Copy data from system memory
	virtual void copyFromMemory(const void* sysData, u32 offset, u32 length);

	//! Copy data from another buffer
	virtual void copyFromBuffer(IHardwareBuffer* buffer, u32 srcOffset, u32 descOffset, u32 length);

	void CopyFromVkBuffer(const irr::u32 &srcOffset, const irr::u32 &destOffset, const irr::u32 &length, const VkBuffer &srcBuf);

	void CopyToVkBuffer(const irr::u32& srcOffset, const irr::u32& destOffset, const irr::u32& length, const VkBuffer& dstBuf);

	void beginCopyCmdBuf();
	void flushCopyCmdBuf();

	bool swapBindingMemory(VkHardwareBuffer* vkbuf);
	//! Get size of buffer in bytes
	virtual u32 size() const;

	//! Get driver type of buffer.
	virtual E_DRIVER_TYPE getDriverType() const;

	//! Get type of buffer.
	virtual E_HARDWARE_BUFFER_TYPE getType() const;

	//! Get flags
	virtual u32 getFlags() const;

	// Methods for Direct3D 11 implementation
public:
	//! return VkBuffer
	VkBuffer getBufferResource() const;
	VkBuffer getBuffer() const { return getBufferResource(); }

#if VK_ENABLE_RAYTRACING
	//! Get device address for raytracing
	VkDeviceAddress getDeviceAddress() const;
#endif



	VkDescriptorBufferInfo Descriptor{};

	void setCurrentCommandBuffer();
	
	//! return unordered access view
	//ID3D11UnorderedAccessView* getUnorderedAccessView() const;

	//! return shader resource view
	//VkBufferView getShaderResourceView() const;

	void resize(u32 size);
private:
	friend class VkDriver;
	vks::Buffer mBuffer;
	//VkBuffer hBuffer{};
	VkDevice Device{};
	//ID3D11UnorderedAccessView* UAView;
	//VkBufferView SRView{};

	// staging texture used for lock/unlock


	VkDriver* Driver;
	VkBufferUsageFlags mUsageFlags;
	//bool UseTempStagingBuffer;
	//MAP_TYPE LastMapDirection = (Diligent::MAP_TYPE)0;
	u32 Size;
	E_HARDWARE_BUFFER_TYPE Type;
	E_HARDWARE_BUFFER_ACCESS AccessType;
	u32 CrFlags;
	VkFence mFence = VK_NULL_HANDLE;
	bool mNeedStaging = true;
	vks::Buffer mStagingBuffer;
	int mStagingCreateCount = 0;
	VkCommandBuffer mCmdBuf = VK_NULL_HANDLE; VkCommandPool cmbPool = VK_NULL_HANDLE;
	bool isExtCmdBuf=false;
	void setupDescriptor(VkDeviceSize size = VK_WHOLE_SIZE, VkDeviceSize offset = 0);
	bool createInternalBuffer(const void* initialData);
	void freeCmdBufAndFence();

	bool lockNeedRead = false;
};


	VkMemoryPropertyFlags GetMemoryFlagsForUsage(E_HARDWARE_BUFFER_ACCESS acctype);
}
}

#endif
#endif