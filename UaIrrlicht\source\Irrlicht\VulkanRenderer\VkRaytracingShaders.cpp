#include "VkRaytracingShaders.h"

#if VK_ENABLE_RAYTRACING
#define INCLUDE_COMPILED_SHADERS

#include "VkDriver.h"
#include "os.h"

// Compiled shader data - these will be populated by the build system
// The actual SPIR-V data will be included from generated header files
#include "shader/Compiled/basic_rt_rchit_SpirV.h"
#include "shader/Compiled/basic_rt_rgen_SpirV.h"
#include "shader/Compiled/basic_rt_rmiss_SpirV.h"
#include "shader/Compiled/shadow_rt_rmiss_SpirV.h"
#include "shader/Compiled/shadow_rt_rchit_SpirV.h"
// Define this to include the compiled shader headers

 
namespace irr {
namespace video {

// Shader database - populated with compiled SPIR-V data
const SRaytracingShaderData VkRaytracingShaders::ShaderDatabase[] = {
#ifdef INCLUDE_COMPILED_SHADERS
    // Ray generation shader
    {
        "basic_rt.rgen",
        ERTST_RAY_GENERATION,
        reinterpret_cast<const unsigned char*>(SpirV_basic_rt_rgen),
        sizeof(SpirV_basic_rt_rgen),
        "main"
    },
    // Primary miss shader
    {
        "basic_rt.rmiss",
        ERTST_MISS_PRIMARY,
        reinterpret_cast<const unsigned char*>(SpirV_basic_rt_rmiss),
        sizeof(SpirV_basic_rt_rmiss),
        "main"
    },
    // Primary closest hit shader
    {
        "basic_rt.rchit",
        ERTST_CLOSEST_HIT_PRIMARY,
        reinterpret_cast<const unsigned char*>(SpirV_basic_rt_rchit),
        sizeof(SpirV_basic_rt_rchit),
        "main"
    },
    // Shadow miss shader
    {
        "shadow_rt.rmiss",
        ERTST_MISS_SHADOW,
        reinterpret_cast<const unsigned char*>(SpirV_shadow_rt_rmiss),
        sizeof(SpirV_shadow_rt_rmiss),
        "main"
    },
    // Shadow closest hit shader
    {
        "shadow_rt.rchit",
        ERTST_CLOSEST_HIT_SHADOW,
        reinterpret_cast<const unsigned char*>(SpirV_shadow_rt_rchit),
        sizeof(SpirV_shadow_rt_rchit),
        "main"
    }
#endif
};

const u32 VkRaytracingShaders::ShaderCount = sizeof(ShaderDatabase) / sizeof(SRaytracingShaderData);

VkRaytracingShaders::VkRaytracingShaders()
    : Driver(nullptr), Initialized(false)
{
}

VkRaytracingShaders::~VkRaytracingShaders()
{
}

bool VkRaytracingShaders::initialize(VkDriver* driver)
{
    if (!driver) {
        os::Printer::log("VkRaytracingShaders: Invalid driver provided", ELL_ERROR);
        return false;
    }

    Driver = driver;
    
    // Check if raytracing is supported
    if (!Driver->isRaytracingSupported()) {
        os::Printer::log("VkRaytracingShaders: Raytracing not supported on this device", ELL_WARNING);
        return false;
    }

#ifdef INCLUDE_COMPILED_SHADERS
    // Verify that all shaders are available
    if (ShaderCount == 0) {
        os::Printer::log("VkRaytracingShaders: No compiled shaders available", ELL_ERROR);
        return false;
    }

    // Validate shader data
    for (u32 i = 0; i < ShaderCount; ++i) {
        const auto& shader = ShaderDatabase[i];
        if (!shader.spirvData || shader.spirvSize == 0) {
            os::Printer::log("VkRaytracingShaders: Invalid shader data", ELL_ERROR);
            return false;
        }
    }

    Initialized = true;
    os::Printer::log("VkRaytracingShaders: Successfully initialized with compiled shaders", ELL_INFORMATION);
    return true;
#else
    os::Printer::log("VkRaytracingShaders: Compiled shaders not available", ELL_WARNING);
    return false;
#endif
}

const SRaytracingShaderData* VkRaytracingShaders::getShaderData(ERaytracingShaderType type) const
{
    if (!Initialized) {
        return nullptr;
    }

    for (u32 i = 0; i < ShaderCount; ++i) {
        if (ShaderDatabase[i].type == type) {
            return &ShaderDatabase[i];
        }
    }

    return nullptr;
}

const SRaytracingShaderData* VkRaytracingShaders::getAllShaders(u32& count) const
{
    count = Initialized ? ShaderCount : 0;
    return Initialized ? ShaderDatabase : nullptr;
}

bool VkRaytracingShaders::areShadersAvailable() const
{
    return Initialized && ShaderCount > 0;
}

VkShaderModule VkRaytracingShaders::createShaderModule(const SRaytracingShaderData* shaderData) const
{
    if (!Driver || !Initialized || !shaderData) {
        return VK_NULL_HANDLE;
    }

    VkShaderModuleCreateInfo createInfo = {};
    createInfo.sType = VK_STRUCTURE_TYPE_SHADER_MODULE_CREATE_INFO;
    createInfo.codeSize = shaderData->spirvSize;
    createInfo.pCode = reinterpret_cast<const uint32_t*>(shaderData->spirvData);

    VkShaderModule shaderModule = VK_NULL_HANDLE;
    VkResult result = vkCreateShaderModule(Driver->getDevice(), &createInfo, nullptr, &shaderModule);
    
    if (result != VK_SUCCESS) {
        os::Printer::log("VkRaytracingShaders: Failed to create shader module", ELL_ERROR);
        return VK_NULL_HANDLE;
    }

    return shaderModule;
}

} // namespace video
} // namespace irr

#endif // VK_ENABLE_RAYTRACING 