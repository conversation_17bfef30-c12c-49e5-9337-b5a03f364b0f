
#include "VkDriverBase.h"

#ifdef _IRR_COMPILE_WITH_VULKAN_ 
#if USE_IMGUI
#include "base/VulkanUIOverlay.h"
#include "base/shader/uioverlay_vs.h"
#include "base/shader/uioverlay_fs.h"
#endif

#include "VkTexture.h"
#include "vulkanRenderPass.h"
using namespace irr;
using namespace irr::video;
#ifdef _WIN32
#define STORE_DEPTH 1
#define IRR_APP_FPS 60
#else
#define STORE_DEPTH 0
#define IRR_APP_FPS 30
#endif
VkPipelineShaderStageCreateInfo VkDriverBase::loadShader(const uint32_t *code, const uint32_t length, VkShaderStageFlagBits stage)
{

	// Create a new shader module that will be used for pipeline creation
	VkShaderModuleCreateInfo moduleCreateInfo{};
	moduleCreateInfo.sType = VK_STRUCTURE_TYPE_SHADER_MODULE_CREATE_INFO;
	moduleCreateInfo.codeSize = length;
	assert(length % 4 == 0);
	moduleCreateInfo.pCode = code;

	VkShaderModule shaderModule;
	VK_CHECK_RESULT(vkCreateShaderModule(Device, &moduleCreateInfo, NULL, &shaderModule));


	VkPipelineShaderStageCreateInfo shaderStage = {};
	shaderStage.sType = VK_STRUCTURE_TYPE_PIPELINE_SHADER_STAGE_CREATE_INFO;
	shaderStage.stage = stage;

	shaderStage.module = shaderModule;

	shaderStage.pName = "main"; // todo : make param
	assert(shaderStage.module != VK_NULL_HANDLE);

	return shaderStage;
}


// Find and create a compute capable device queue

VkQueue irr::video::VkDriverBase::getComputeQueue()
{
	if (_queueCompute != VK_NULL_HANDLE)
		return queueCompute();

#if !VKDRIVER_USE_DEDICATED_COMPUTE_QUEUE
	assert(mDevice->queueFamilyIndices.compute == mDevice->queueFamilyIndices.graphics);
	if (mDevice->queueFamilyIndices.compute != mDevice->queueFamilyIndices.graphics)
	{
		throw "different queue?";//to
	}
#endif

	// Get a compute queue from the device
	vkGetDeviceQueue(Device, mDevice->queueFamilyIndices.compute, 0, &_queueCompute);


#if VKDRIVER_USE_DEDICATED_COMPUTE_QUEUE || VKDRIVER_USE_DEDICATED_COMPUTE_COMMAND_POOL
	VkCommandPoolCreateInfo cmdPoolInfo = {};
	cmdPoolInfo.sType = VK_STRUCTURE_TYPE_COMMAND_POOL_CREATE_INFO;
	cmdPoolInfo.queueFamilyIndex = mDevice->queueFamilyIndices.compute;
	cmdPoolInfo.flags = VK_COMMAND_POOL_CREATE_RESET_COMMAND_BUFFER_BIT;
	VK_CHECK_RESULT(vkCreateCommandPool(Device, &cmdPoolInfo, nullptr, &cmdPoolCompute));
#else
	cmdPoolCompute = mDevice->commandPool;
#endif


	return _queueCompute;
}



VkDriverBase::VkDriverBase(const irr::SIrrlichtCreationParameters& params, io::IFileSystem* io)
	: CNullDriver(io, params.WindowSize), OffScreen(params.OffScreen), CreatePms(params)

{ 
#if USE_IMGUI
	if (!OffScreen)
	UIOverlay = new vks::UIOverlay();

	UIOverlay->setupGizmo();
	UIOverlay->setGizmoStyle(3.0f, 8.0f);

#endif
	renderUI = CreatePms.DebugUIOverlay;
	useTexRT = params.bUseTexRt;
	useShadow = params.bUseShadow;
}

VkDriverBase::~VkDriverBase()
{


}


bool VkDriverBase::isUIEventHandled() {
#if USE_IMGUI
	ImGuiIO& io = ImGui::GetIO();
	return  io.WantCaptureMouse;
#else
	return false;
#endif
}
void RenderMouse()
{
	ImGuiIO& io = ImGui::GetIO();
	ImDrawList* drawList = ImGui::GetForegroundDrawList();

	// Get current mouse position
	ImVec2 mousePos = io.MousePos;
	bool hoverItem = ImGui::IsAnyItemHovered();
	// Mouse cursor colors
	ImColor cursorColor(255, 255, 255, hoverItem?255:200);        // White
	ImColor cursorOutlineColor(0, 0, 0, 255);       // Black outline
	ImColor clickColor(255, 0, 0, 128);             // Red for clicks

	// Cursor size configuration
	float cursorSize = hoverItem?22.f:20.0f;
	float outlineThickness = 1.5f;
	bool down = io.MouseDown[0];
	if (down) mousePos.x+= down, mousePos.y+= down;
	// Draw cursor outline
	drawList->AddTriangleFilled(
		ImVec2(mousePos.x, mousePos.y),
		ImVec2(mousePos.x + cursorSize*0.75f, mousePos.y + cursorSize * 0.75f),
		ImVec2(mousePos.x + cursorSize * 0.f, mousePos.y + cursorSize),
		cursorOutlineColor
	);

	// Draw cursor interior (slightly smaller)
	drawList->AddTriangleFilled(
		ImVec2(mousePos.x + outlineThickness, mousePos.y + outlineThickness),
		ImVec2(mousePos.x + cursorSize * 0.75f - outlineThickness, mousePos.y + cursorSize * 0.75f - outlineThickness),
		ImVec2(mousePos.x + outlineThickness , mousePos.y + cursorSize - outlineThickness),
		cursorColor
	);

	// Render click effects
	if (io.MouseDown[0]) // Left click
	{
		drawList->AddCircleFilled(mousePos, cursorSize * 0.5f, clickColor);
	}
	//TODO : draw mouse position text beside cursor
	static char postext[32];	
	sprintf(postext, "x:%.0f y:%.0f", mousePos.x, mousePos.y);
	drawList->AddText(ImVec2(mousePos.x + cursorSize, mousePos.y), IM_COL32(255, 255, 255, 255), postext);

	
	//// Optional: Add hover effect for UI elements
	//if (ImGui::IsAnyItemHovered())
	//{
	//	drawList->AddCircle(
	//		mousePos,
	//		cursorSize * 0.5f,
	//		cursorColor,
	//		12,          // Number of segments
	//		2.0f        // Thickness
	//	);
	//}
}
void VkDriverBase::updateUI(float deltaTime)
{
#if USE_IMGUI
	if (!renderUI || !OnUpdateUIOverlay || !uiReady)
		return;

	ImGuiIO& io = ImGui::GetIO();

	io.DisplaySize = ImVec2((float)ScreenSize.Width, (float)ScreenSize.Height);
	io.DeltaTime = 1.f/IRR_APP_FPS;
#if DRIVER_HAS_UI_INPUT

	//io.KeyCtrl = dsd.mouseModifiers[0];  // no effect
	//io.KeyShift = dsd.mouseModifiers[1];
	//io.KeyAlt = dsd.mouseModifiers[2];
#endif
	ImGui::NewFrame();
	UIOverlay->beginGizmoFrame();
	UIOverlay->setupViewManipulator();
	ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, 0);
	ImGui::PushStyleColor(ImGuiCol_WindowBg, ImVec4(0.2f, 0.2f, 0.2f, 0.6f)); // Set color with alpha

 
	ImGui::SetNextWindowSize(ImVec2(0, 0), 0);
	ImGui::Begin("DEBUG", nullptr, ImGuiWindowFlags_AlwaysAutoResize);// | ImGuiWindowFlags_NoResize | ImGuiWindowFlags_NoMove);
	//ImGui::TextUnformatted("[title]");
	//ImGui::TextUnformatted(deviceProperties.deviceName);
	//ImGui::Text("%.2f ms  (%.1d fps)", (1000.0f / getFPS()), getFPS());

#if defined(VK_USE_PLATFORM_ANDROID_KHR)
	ImGui::PushStyleVar(ImGuiStyleVar_ItemSpacing, ImVec2(0.0f, 5.0f * UIOverlay->scale));
#endif
	//ImGui::PushItemWidth(110.0f * UIOverlay->scale);
	if (OnUpdateUIOverlay)
		OnUpdateUIOverlay(UIOverlay);
	//ImGui::PopItemWidth();
#if defined(VK_USE_PLATFORM_ANDROID_KHR)
	ImGui::PopStyleVar();
#endif

	ImGui::End();
	ImGui::PopStyleColor();
	ImGui::PopStyleVar();
	if (renderCursor)	RenderMouse();
	ImGui::Render();

	if (UIOverlay->update() || UIOverlay->updated) {
		//do Onchanged
		UIOverlay->updated = false;
	}

#if defined(VK_USE_PLATFORM_ANDROID_KHR)
	//if (mouseButtons.left) 		mouseButtons.left = false;

#endif
#endif
}
namespace vks {
	extern bool releasing;
}
void VkDriverBase::onRelease()
{
	vks::releasing = true;
#if USE_IMGUI
	if (UIOverlay) UIOverlay->freeResources();
#endif
	if (OffScreen)
	{
		vkDestroyRenderPass(Device, RenderPass1, nullptr);
		vkDestroyRenderPass(Device, RenderPass, nullptr);
		vkDestroyPipelineCache(Device, PipelineCache, nullptr);
		vkDestroyCommandPool(Device, cmdPool, nullptr);
	}
	else
	{
		SwapChain.cleanup();
		//compute
#if VKDRIVER_USE_DEDICATED_COMPUTE_QUEUE || VKDRIVER_USE_DEDICATED_COMPUTE_COMMAND_POOL
		vkDestroyCommandPool(Device, cmdPoolCompute, nullptr);
#endif

		if (descriptorPool != VK_NULL_HANDLE)
		{
			vkDestroyDescriptorPool(Device, descriptorPool, nullptr);
		}
		destroyCommandBuffers();
		vkDestroyRenderPass(Device, RenderPass1, nullptr);
		vkDestroyRenderPass(Device, RenderPass, nullptr);
		for (uint32_t i = 0; i < frameBuffers.size(); i++)
		{
			vkDestroyFramebuffer(Device, frameBuffers[i], nullptr);
		}

		vkDestroyImageView(Device, depthStencil.view, nullptr);
		vkDestroyImage(Device, depthStencil.image, nullptr);
		vkFreeMemory(Device, depthStencil.mem, nullptr);

		vkDestroyPipelineCache(Device, PipelineCache, nullptr);

		vkDestroyCommandPool(Device, cmdPool, nullptr);
		vkDestroySemaphore(Device, semaphores.presentComplete, nullptr);
		vkDestroySemaphore(Device, semaphores.renderComplete, nullptr);
		for (auto& fence : waitFences) {
			vkDestroyFence(Device, fence, nullptr);
		}

	}

	//FreeLibrary(D3DLibrary);


	delete mDevice; mDevice = nullptr;

	if (!OffScreen)
	{
		vks::debug::freeDebugCallback(instance);

		vkDestroyInstance(instance, nullptr);
	}
}




VkRenderPass irr::video::VkDriverBase::curRenderPass(bool system) {
 
	if (_currentRT)		return _currentRT->getVRP()->getRenderPass();
 
 
	if (useTexRT && !system && TexDrvRT)
		return ((VkTexture*)(TexDrvRT))->getVRP()->getRenderPass();
 

	return MultiPassMode > 0 ?	RenderPass : RenderPass1;
}
VkFramebuffer irr::video::VkDriverBase::curFrameBuffer(bool system) {
	if (_currentRT)
		return _currentRT->getVRP()->getFrameBuffer();
	if (useTexRT && !system && TexDrvRT)
		return ((VkTexture*)(TexDrvRT))->getVRP()->getFrameBuffer();
 
	return isExtDev ? fbExt : frameBuffers[currentBuffer];
}
VkSampleCountFlagBits irr::video::VkDriverBase::getMaxUsableSampleCount()
{
	VkSampleCountFlags counts = std::min(deviceProperties.limits.framebufferColorSampleCounts, deviceProperties.limits.framebufferDepthSampleCounts);
	if (counts & VK_SAMPLE_COUNT_64_BIT) { return VK_SAMPLE_COUNT_64_BIT; }
	if (counts & VK_SAMPLE_COUNT_32_BIT) { return VK_SAMPLE_COUNT_32_BIT; }
	if (counts & VK_SAMPLE_COUNT_16_BIT) { return VK_SAMPLE_COUNT_16_BIT; }
	if (counts & VK_SAMPLE_COUNT_8_BIT) { return VK_SAMPLE_COUNT_8_BIT; }
	if (counts & VK_SAMPLE_COUNT_4_BIT) { return VK_SAMPLE_COUNT_4_BIT; }
	if (counts & VK_SAMPLE_COUNT_2_BIT) { return VK_SAMPLE_COUNT_2_BIT; }
	return VK_SAMPLE_COUNT_1_BIT;
}

VkResult VkDriverBase::createInstance(bool enableValidation)
{

	// Validation can also be forced via a define
#if defined(_VALIDATION)
	enableValidation = true;

#endif	

	VkApplicationInfo appInfo = {};
	appInfo.sType = VK_STRUCTURE_TYPE_APPLICATION_INFO;
	appInfo.pApplicationName = "[appname]";
	appInfo.pEngineName = "[enginename]";
	appInfo.apiVersion = VK_API_VERSION_1_3;

	std::vector<const char*> instanceExtensions = { VK_KHR_SURFACE_EXTENSION_NAME };

	// Enable surface extensions depending on os
#if defined(_WIN32)
	instanceExtensions.push_back(VK_KHR_WIN32_SURFACE_EXTENSION_NAME);
#elif defined(VK_USE_PLATFORM_ANDROID_KHR)
	instanceExtensions.push_back(VK_KHR_ANDROID_SURFACE_EXTENSION_NAME);
#elif defined(_DIRECT2DISPLAY)
	instanceExtensions.push_back(VK_KHR_DISPLAY_EXTENSION_NAME);
#elif defined(VK_USE_PLATFORM_WAYLAND_KHR)
	instanceExtensions.push_back(VK_KHR_WAYLAND_SURFACE_EXTENSION_NAME);
#elif defined(VK_USE_PLATFORM_XCB_KHR)
	instanceExtensions.push_back(VK_KHR_XCB_SURFACE_EXTENSION_NAME);
#elif defined(VK_USE_PLATFORM_IOS_MVK)
	instanceExtensions.push_back(VK_MVK_IOS_SURFACE_EXTENSION_NAME);
#elif defined(VK_USE_PLATFORM_MACOS_MVK)
	instanceExtensions.push_back(VK_MVK_MACOS_SURFACE_EXTENSION_NAME);
#endif

	if (enabledInstanceExtensions.size() > 0) {
		for (auto enabledExtension : enabledInstanceExtensions) {
			instanceExtensions.push_back(enabledExtension);
		}
	}

	VkInstanceCreateInfo instanceCreateInfo = {};
	instanceCreateInfo.sType = VK_STRUCTURE_TYPE_INSTANCE_CREATE_INFO;
	instanceCreateInfo.pNext = NULL;
	instanceCreateInfo.pApplicationInfo = &appInfo;
	if (instanceExtensions.size() > 0)
	{
		if (enableValidation)
		{
			DP(("DEBUG LAYER enableValidation "));
			instanceExtensions.push_back(VK_EXT_DEBUG_REPORT_EXTENSION_NAME);
			//instanceExtensions.push_back(VK_EXT_DEBUG_UTILS_EXTENSION_NAME);
		}
		instanceCreateInfo.enabledExtensionCount = (uint32_t)instanceExtensions.size();
		instanceCreateInfo.ppEnabledExtensionNames = instanceExtensions.data();
	}
	if (enableValidation)
	{
		DP(("DEBUG LAYER enabledLayerCount=%d ", vks::debug::validationLayerCount));
		instanceCreateInfo.enabledLayerCount = vks::debug::validationLayerCount;
		instanceCreateInfo.ppEnabledLayerNames = vks::debug::validationLayerNames;
	}
	DP(("vkCreateInstance+++++++++++++++++++++++++ %p", vkCreateInstance));
	VkResult vr = vkCreateInstance(&instanceCreateInfo, nullptr, &instance);
	DP(("vkCreateInstance = %d",vr));
	return vr;
}

void irr::video::VkDriverBase::initSwapchain(void* hwnd)
{
#if defined(_WIN32)
	SwapChain.initSurface(GetModuleHandle(NULL), hwnd);
#elif defined(VK_USE_PLATFORM_ANDROID_KHR)	
	SwapChain.initSurface((ANativeWindow*)hwnd);
#elif (defined(VK_USE_PLATFORM_IOS_MVK) || defined(VK_USE_PLATFORM_MACOS_MVK))
	SwapChain.initSurface(view);
#elif defined(_DIRECT2DISPLAY)
	SwapChain.initSurface(width, height);
#elif defined(VK_USE_PLATFORM_WAYLAND_KHR)
	SwapChain.initSurface(display, surface);
#elif defined(VK_USE_PLATFORM_XCB_KHR)
	SwapChain.initSurface(connection, window);
#endif
}

void VkDriverBase::createCmdBufs()
{
	// Create one command buffer for each swap chain image and reuse for rendering
	drawCmdBuffers.resize(SwapChain.imageCount);

	VkCommandBufferAllocateInfo cmdBufAllocateInfo =
		vks::initializers::commandBufferAllocateInfo(
			cmdPool,
			VK_COMMAND_BUFFER_LEVEL_PRIMARY,
			static_cast<uint32_t>(drawCmdBuffers.size()));

	VK_CHECK_RESULT(vkAllocateCommandBuffers(Device, &cmdBufAllocateInfo, drawCmdBuffers.data()));
}
void VkDriverBase::destroyCommandBuffers()
{
	vkFreeCommandBuffers(Device, cmdPool, static_cast<uint32_t>(drawCmdBuffers.size()), drawCmdBuffers.data());
}

VkCmdBuf VkDriverBase::allocCmdBuf(VkCommandBufferLevel level, bool begin)
{
	commandPoolMutex.lock();
	VkCommandPool cmdPool = mDevice->thCmdPool();

	VkCommandBufferAllocateInfo cmdBufAllocateInfo = vks::initializers::commandBufferAllocateInfo(cmdPool, level, 1);

	VkCommandBuffer cmdBuffer;
	VK_CHECK_RESULT(vkAllocateCommandBuffers(Device, &cmdBufAllocateInfo, &cmdBuffer));
	commandPoolMutex.unlock();
	// If requested, also start recording for the new command buffer
	if (begin)
	{
		VkCommandBufferBeginInfo cmdBufInfo = vks::initializers::commandBufferBeginInfo();
		VK_CHECK_RESULT(vkBeginCommandBuffer(cmdBuffer, &cmdBufInfo));
	}
	VkCmdBuf cb;
	cb.cmdBuf = cmdBuffer;
	cb.cmdPool = cmdPool;
	return cb;
}
void VkDriverBase::freeCmdBuf(VkCmdBuf& cb)
{
	if (cb.cmdBuf)
	{
		commandPoolMutex.lock();
		vkFreeCommandBuffers(Device, cb.cmdPool, 1, &cb.cmdBuf);
		commandPoolMutex.unlock();
	}
	cb.cmdBuf = VK_NULL_HANDLE;
}

ITexture* irr::video::VkDriverBase::getPickRT(int w, int h)
{
	core::dimension2du size(w, h);
	if (w>0 && TexPick && TexPick->getSize() != size) {
		removeTexture(TexPick); TexPick = nullptr;
	}
	if (!TexPick)
		TexPick = (VkTexture*)addRenderTargetTexture(size, "ScrRT", ECF_RGBA32UI);// ECF_R32UI);
	return TexPick;
}

void irr::video::VkDriverBase::createPipelineCache()
{
	VkPipelineCacheCreateInfo pipelineCacheCreateInfo = {};
	pipelineCacheCreateInfo.sType = VK_STRUCTURE_TYPE_PIPELINE_CACHE_CREATE_INFO;
	VK_CHECK_RESULT(vkCreatePipelineCache(Device, &pipelineCacheCreateInfo, nullptr, &PipelineCache));
}
void VkDriverBase::setupDepthStencil()
{

	VkImageCreateInfo image = vks::initializers::imageCreateInfo();
	image.imageType = VK_IMAGE_TYPE_2D;
	image.format = depthFormat;
	image.extent = { ScreenSize.Width, ScreenSize.Height, 1 };
	image.mipLevels = 1;
	image.arrayLayers = 1;
	image.samples = SampleCount;
	image.tiling = VK_IMAGE_TILING_OPTIMAL;
	image.usage = VK_IMAGE_USAGE_DEPTH_STENCIL_ATTACHMENT_BIT | VK_IMAGE_USAGE_TRANSFER_SRC_BIT | (STORE_DEPTH?VK_IMAGE_USAGE_SAMPLED_BIT:0);
	image.flags = 0;

	VkMemoryAllocateInfo mem_alloc = {};
	mem_alloc.sType = VK_STRUCTURE_TYPE_MEMORY_ALLOCATE_INFO;
	mem_alloc.pNext = NULL;
	mem_alloc.allocationSize = 0;
	mem_alloc.memoryTypeIndex = 0;

	VkImageViewCreateInfo depthStencilView = {};
	depthStencilView.sType = VK_STRUCTURE_TYPE_IMAGE_VIEW_CREATE_INFO;
	depthStencilView.pNext = NULL;
	depthStencilView.viewType = VK_IMAGE_VIEW_TYPE_2D;
	depthStencilView.format = depthFormat;
	depthStencilView.flags = 0;
	depthStencilView.subresourceRange = {};
	depthStencilView.subresourceRange.aspectMask = VK_IMAGE_ASPECT_DEPTH_BIT ;
	depthStencilView.subresourceRange.baseMipLevel = 0;
	depthStencilView.subresourceRange.levelCount = 1;
	depthStencilView.subresourceRange.baseArrayLayer = 0;
	depthStencilView.subresourceRange.layerCount = 1;

	VkMemoryRequirements memReqs;

	VK_CHECK_RESULT(vkCreateImage(Device, &image, nullptr, &depthStencil.image));
	DP(("crtex drbds %p", depthStencil.image));
	vkGetImageMemoryRequirements(Device, depthStencil.image, &memReqs);
	mem_alloc.allocationSize = memReqs.size;
	mem_alloc.memoryTypeIndex = mDevice->getMemoryType(memReqs.memoryTypeBits, VK_MEMORY_PROPERTY_DEVICE_LOCAL_BIT);
	VK_CHECK_RESULT(vkAllocateMemory(Device, &mem_alloc, nullptr, &depthStencil.mem));
	VK_CHECK_RESULT(vkBindImageMemory(Device, depthStencil.image, depthStencil.mem, 0));

	depthStencilView.image = depthStencil.image;
	VK_CHECK_RESULT(vkCreateImageView(Device, &depthStencilView, nullptr, &depthStencil.view));
}
void VkDriverBase::setupRenderPass(bool isExtDev)
{

	std::array<VkAttachmentDescription, 3> attachments = {};
	// Color attachment
	attachments[0].format = SwapChain.colorFormat;
	attachments[0].samples = SampleCount;
	attachments[0].loadOp =  isExtDev?VK_ATTACHMENT_LOAD_OP_LOAD:VK_ATTACHMENT_LOAD_OP_CLEAR;// VK_ATTACHMENT_LOAD_OP_DONT_CARE;//
	attachments[0].storeOp = VK_ATTACHMENT_STORE_OP_STORE;
	attachments[0].stencilLoadOp = VK_ATTACHMENT_LOAD_OP_DONT_CARE;
	attachments[0].stencilStoreOp = VK_ATTACHMENT_STORE_OP_DONT_CARE;
	attachments[0].initialLayout = VK_IMAGE_LAYOUT_UNDEFINED;
	attachments[0].finalLayout = VK_IMAGE_LAYOUT_PRESENT_SRC_KHR;
	// Depth attachment
	attachments[1].format = depthFormat;
	attachments[1].samples = SampleCount;
	attachments[1].loadOp = isExtDev ? VK_ATTACHMENT_LOAD_OP_LOAD : VK_ATTACHMENT_LOAD_OP_CLEAR;//VK_ATTACHMENT_LOAD_OP_DONT_CARE;
	attachments[1].storeOp = STORE_DEPTH? VK_ATTACHMENT_STORE_OP_STORE : VK_ATTACHMENT_STORE_OP_DONT_CARE;
	attachments[1].stencilLoadOp = VK_ATTACHMENT_LOAD_OP_DONT_CARE; //VK_ATTACHMENT_LOAD_OP_CLEAR;
	attachments[1].stencilStoreOp = VK_ATTACHMENT_STORE_OP_DONT_CARE;
	attachments[1].initialLayout = VK_IMAGE_LAYOUT_UNDEFINED;
	attachments[1].finalLayout = STORE_DEPTH ? VK_IMAGE_LAYOUT_DEPTH_STENCIL_READ_ONLY_OPTIMAL: VK_IMAGE_LAYOUT_DEPTH_STENCIL_ATTACHMENT_OPTIMAL;


	attachments[2].format = VK_FORMAT_R16G16B16A16_SFLOAT;
	attachments[2].samples = SampleCount;
	attachments[2].loadOp = VK_ATTACHMENT_LOAD_OP_DONT_CARE;//VK_ATTACHMENT_LOAD_OP_CLEAR;//
	attachments[2].storeOp = VK_ATTACHMENT_STORE_OP_STORE;
	attachments[2].stencilLoadOp = VK_ATTACHMENT_LOAD_OP_DONT_CARE; //VK_ATTACHMENT_LOAD_OP_CLEAR;
	attachments[2].stencilStoreOp = VK_ATTACHMENT_STORE_OP_DONT_CARE;
	attachments[2].initialLayout = VK_IMAGE_LAYOUT_UNDEFINED;
	attachments[2].finalLayout = VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL;

	VkAttachmentReference colorReference = {};
	colorReference.attachment = 0;
	colorReference.layout = VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL;
	VkAttachmentReference colorReferenceF = {};
	colorReferenceF.attachment = 2;
	colorReferenceF.layout = VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL;
	VkAttachmentReference depthReference = {};
	depthReference.attachment = 1;
	depthReference.layout = VK_IMAGE_LAYOUT_DEPTH_STENCIL_ATTACHMENT_OPTIMAL;

	VkAttachmentReference inputReferences[1];
	inputReferences[0] = {2, VK_IMAGE_LAYOUT_SHADER_READ_ONLY_OPTIMAL };


	VkSubpassDescription subpassDescription1 = {};
	subpassDescription1.pipelineBindPoint = VK_PIPELINE_BIND_POINT_GRAPHICS;
	subpassDescription1.colorAttachmentCount = 1;
	subpassDescription1.pColorAttachments = &colorReference;
	subpassDescription1.pDepthStencilAttachment = &depthReference;
	subpassDescription1.inputAttachmentCount = 0;
	subpassDescription1.pInputAttachments = nullptr;
	subpassDescription1.preserveAttachmentCount = 0;
	subpassDescription1.pPreserveAttachments = nullptr;
	subpassDescription1.pResolveAttachments = nullptr;


	VkSubpassDescription subpassDescription[2] = {};
	subpassDescription[0].pipelineBindPoint = VK_PIPELINE_BIND_POINT_GRAPHICS;
	subpassDescription[0].colorAttachmentCount = 1;
	subpassDescription[0].pColorAttachments = &colorReferenceF;
	subpassDescription[0].pDepthStencilAttachment = &depthReference;
	subpassDescription[0].inputAttachmentCount = 0;
	subpassDescription[0].pInputAttachments = nullptr;
	subpassDescription[0].preserveAttachmentCount = 0;
	subpassDescription[0].pPreserveAttachments = nullptr;
	subpassDescription[0].pResolveAttachments = nullptr;

	subpassDescription[1].pipelineBindPoint = VK_PIPELINE_BIND_POINT_GRAPHICS;
	subpassDescription[1].colorAttachmentCount = 1;
	subpassDescription[1].pColorAttachments = &colorReference;
	subpassDescription[1].pDepthStencilAttachment = &depthReference;
	subpassDescription[1].inputAttachmentCount = 1;
	subpassDescription[1].pInputAttachments = inputReferences;
	subpassDescription[1].preserveAttachmentCount = 0;
	subpassDescription[1].pPreserveAttachments = nullptr;
	subpassDescription[1].pResolveAttachments = nullptr;


	// Subpass dependencies for layout transitions

	std::array<VkSubpassDependency, 3> dependencies1;
	dependencies1[0].srcSubpass = VK_SUBPASS_EXTERNAL;
	dependencies1[0].dstSubpass = 0;
	dependencies1[0].srcStageMask = VK_PIPELINE_STAGE_BOTTOM_OF_PIPE_BIT;
	dependencies1[0].dstStageMask = VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT;
	dependencies1[0].srcAccessMask = VK_ACCESS_MEMORY_READ_BIT;
	dependencies1[0].dstAccessMask = VK_ACCESS_COLOR_ATTACHMENT_READ_BIT | VK_ACCESS_COLOR_ATTACHMENT_WRITE_BIT;
	dependencies1[0].dependencyFlags = VK_DEPENDENCY_BY_REGION_BIT;	
	dependencies1[1].srcSubpass = 0;
	dependencies1[1].dstSubpass = VK_SUBPASS_EXTERNAL;
	dependencies1[1].srcStageMask = VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT;
	dependencies1[1].dstStageMask = VK_PIPELINE_STAGE_BOTTOM_OF_PIPE_BIT;
	dependencies1[1].dstAccessMask = VK_ACCESS_MEMORY_READ_BIT;
	dependencies1[1].srcAccessMask = VK_ACCESS_COLOR_ATTACHMENT_READ_BIT | VK_ACCESS_COLOR_ATTACHMENT_WRITE_BIT;
	dependencies1[1].dependencyFlags = VK_DEPENDENCY_BY_REGION_BIT;
	dependencies1[2] = { 0, 0,
	VK_PIPELINE_STAGE_FRAGMENT_SHADER_BIT,
	VK_PIPELINE_STAGE_FRAGMENT_SHADER_BIT, /* XXX */
	 VK_ACCESS_SHADER_WRITE_BIT,
	VK_ACCESS_SHADER_READ_BIT , VK_DEPENDENCY_BY_REGION_BIT };
	std::array<VkSubpassDependency, 3> dependencies;
	dependencies[0].srcSubpass = VK_SUBPASS_EXTERNAL;
	dependencies[0].dstSubpass = 0;
	dependencies[0].srcStageMask = VK_PIPELINE_STAGE_BOTTOM_OF_PIPE_BIT;
	dependencies[0].dstStageMask = VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT;
	dependencies[0].srcAccessMask = VK_ACCESS_MEMORY_READ_BIT;
	dependencies[0].dstAccessMask = VK_ACCESS_COLOR_ATTACHMENT_READ_BIT | VK_ACCESS_COLOR_ATTACHMENT_WRITE_BIT;
	dependencies[0].dependencyFlags = VK_DEPENDENCY_BY_REGION_BIT;

	dependencies[1].srcSubpass = 0;
	dependencies[1].dstSubpass = 1;
	dependencies[1].srcStageMask = VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT;
	dependencies[1].dstStageMask = VK_PIPELINE_STAGE_FRAGMENT_SHADER_BIT;
	dependencies[1].srcAccessMask = VK_ACCESS_COLOR_ATTACHMENT_WRITE_BIT;
	dependencies[1].dstAccessMask = VK_ACCESS_SHADER_READ_BIT;
	dependencies[1].dependencyFlags = VK_DEPENDENCY_BY_REGION_BIT;

	dependencies[2].srcSubpass = 1;
	dependencies[2].dstSubpass = VK_SUBPASS_EXTERNAL;
	dependencies[2].srcStageMask = VK_PIPELINE_STAGE_COLOR_ATTACHMENT_OUTPUT_BIT;
	dependencies[2].dstStageMask = VK_PIPELINE_STAGE_BOTTOM_OF_PIPE_BIT;
	dependencies[2].dstAccessMask = VK_ACCESS_MEMORY_READ_BIT;
	dependencies[2].srcAccessMask = VK_ACCESS_COLOR_ATTACHMENT_READ_BIT | VK_ACCESS_COLOR_ATTACHMENT_WRITE_BIT;
	dependencies[2].dependencyFlags = VK_DEPENDENCY_BY_REGION_BIT;

	VkRenderPassCreateInfo renderPassInfo1 = {};
	renderPassInfo1.sType = VK_STRUCTURE_TYPE_RENDER_PASS_CREATE_INFO;
	renderPassInfo1.attachmentCount = 2;
	renderPassInfo1.pAttachments = attachments.data();
	renderPassInfo1.subpassCount = 1;
	renderPassInfo1.pSubpasses = &subpassDescription1;
	renderPassInfo1.dependencyCount = static_cast<uint32_t>(dependencies1.size());
	renderPassInfo1.pDependencies = dependencies1.data();

	VkRenderPassCreateInfo renderPassInfo = {};
	renderPassInfo.sType = VK_STRUCTURE_TYPE_RENDER_PASS_CREATE_INFO;
	renderPassInfo.attachmentCount = 3;
	renderPassInfo.pAttachments = attachments.data();
	renderPassInfo.subpassCount = 2;
	renderPassInfo.pSubpasses = subpassDescription;
	renderPassInfo.dependencyCount = static_cast<uint32_t>(dependencies.size());
	renderPassInfo.pDependencies = dependencies.data();

	VK_CHECK_RESULT(vkCreateRenderPass(Device, &renderPassInfo1, nullptr, &RenderPass1));
	VK_CHECK_RESULT(vkCreateRenderPass(Device, &renderPassInfo, nullptr, &RenderPass));
}



void VkDriverBase::setupFrameBuffer()
{
	createAttachment(VK_FORMAT_R16G16B16A16_SFLOAT, VK_IMAGE_USAGE_COLOR_ATTACHMENT_BIT, &fbaHdr);
	dsd.drvMidTexFormat = ECF_A8R8G8B8;
	if (useTexRT) {
		if (TexDrvRT)
			removeTexture(TexDrvRT);
		TexDrvRT = (VkTexture*)addRenderTargetTexture(ScreenSize, "ScrRT",
			1 ? ECF_A16B16G16R16F :	ECF_A16B16G16R16UN
		);
		dsd.drvMidTexFormat = TexDrvRT->getColorFormat();
	}
 
	if (useShadow) {
		TexSdDepMap = addRenderTargetTexture(core::dimension2du(2048,2048), "Drv<DepthRT>", TexDrvRT ? TexDrvRT->getColorFormat() : ECF_A8R8G8B8);
		//TexSdDepC = addTexture(getScreenSize(), "dp" );
	}

#if IRR_MTR_SSAO
	if (ssaoRT)
		removeTexture(ssaoRT);
	ssaoRT = (VkTexture*)addRenderTargetTexture(ScreenSize, "<SSAO_RT>");
	ssaoRT->getVRP();
#endif

	VkImageView attachmentViews[3];

	// Depth/Stencil attachment is the same for all frame buffers
	attachmentViews[1] = depthStencil.view;

	VkFramebufferCreateInfo frameBufferCreateInfo = {};
	frameBufferCreateInfo.sType = VK_STRUCTURE_TYPE_FRAMEBUFFER_CREATE_INFO;
	frameBufferCreateInfo.pNext = NULL;
	frameBufferCreateInfo.renderPass = MultiPassMode > 0 ? RenderPass: RenderPass1;
	frameBufferCreateInfo.attachmentCount = MultiPassMode>0?3:2;
	frameBufferCreateInfo.pAttachments = attachmentViews;
	frameBufferCreateInfo.width = ScreenSize.Width;
	frameBufferCreateInfo.height = ScreenSize.Height;
	frameBufferCreateInfo.layers = 1;

	// Create frame buffers for every swap chain image
	frameBuffers.resize(SwapChain.imageCount);
	for (uint32_t i = 0; i < frameBuffers.size(); i++)
	{
		attachmentViews[0] = SwapChain.buffers[i].view;
		attachmentViews[2] = fbaHdr.view;
		VK_CHECK_RESULT(vkCreateFramebuffer(Device, &frameBufferCreateInfo, nullptr, &frameBuffers[i]));
	}

}



void VkDriverBase::createAttachment(VkFormat format, VkImageUsageFlags usage, VkDriverBase::FrameBufferAttachment* attachment)
{
	destroyAttachment(attachment);

	VkImageAspectFlags aspectMask = 0;
	VkImageLayout imageLayout;

	attachment->format = format;

	if (usage & VK_IMAGE_USAGE_COLOR_ATTACHMENT_BIT)
	{
		aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
		imageLayout = VK_IMAGE_LAYOUT_COLOR_ATTACHMENT_OPTIMAL;
	}
	if (usage & VK_IMAGE_USAGE_DEPTH_STENCIL_ATTACHMENT_BIT)
	{
		aspectMask = VK_IMAGE_ASPECT_DEPTH_BIT | VK_IMAGE_ASPECT_STENCIL_BIT;
		imageLayout = VK_IMAGE_LAYOUT_DEPTH_STENCIL_ATTACHMENT_OPTIMAL;
	}

	assert(aspectMask > 0);

	VkImageCreateInfo image = vks::initializers::imageCreateInfo();
	image.imageType = VK_IMAGE_TYPE_2D;
	image.format = format;
	image.extent.width = ScreenSize.Width;
	image.extent.height = ScreenSize.Height;
	image.extent.depth = 1;
	image.mipLevels = 1;
	image.arrayLayers = 1;
	image.samples = SampleCount;
	image.tiling = VK_IMAGE_TILING_OPTIMAL;
	image.usage = usage | VK_IMAGE_USAGE_INPUT_ATTACHMENT_BIT| VK_IMAGE_USAGE_TRANSFER_SRC_BIT| VK_IMAGE_USAGE_TRANSFER_DST_BIT;	// VK_IMAGE_USAGE_INPUT_ATTACHMENT_BIT flag is required for input attachments;
	image.initialLayout = VK_IMAGE_LAYOUT_UNDEFINED;

	VkMemoryAllocateInfo memAlloc = vks::initializers::memoryAllocateInfo();
	VkMemoryRequirements memReqs;

	VK_CHECK_RESULT(vkCreateImage(Device, &image, nullptr, &attachment->image));
	DP(("crtex ata %p", attachment->image));
	vkGetImageMemoryRequirements(Device, attachment->image, &memReqs);
	memAlloc.allocationSize = memReqs.size;
	memAlloc.memoryTypeIndex = mDevice->getMemoryType(memReqs.memoryTypeBits, VK_MEMORY_PROPERTY_DEVICE_LOCAL_BIT);
	VK_CHECK_RESULT(vkAllocateMemory(Device, &memAlloc, nullptr, &attachment->mem));
	VK_CHECK_RESULT(vkBindImageMemory(Device, attachment->image, attachment->mem, 0));

	VkImageViewCreateInfo imageView = vks::initializers::imageViewCreateInfo();
	imageView.viewType = VK_IMAGE_VIEW_TYPE_2D;
	imageView.format = format;
	imageView.subresourceRange = {};
	imageView.subresourceRange.aspectMask = aspectMask;
	imageView.subresourceRange.baseMipLevel = 0;
	imageView.subresourceRange.levelCount = 1;
	imageView.subresourceRange.baseArrayLayer = 0;
	imageView.subresourceRange.layerCount = 1;
	imageView.image = attachment->image;
	VK_CHECK_RESULT(vkCreateImageView(Device, &imageView, nullptr, &attachment->view));
}

void VkDriverBase::destroyAttachment(FrameBufferAttachment* attachment) {
	vkDestroyImageView(Device, attachment->view, nullptr);
	vkDestroyImage(Device, attachment->image, nullptr);
	vkFreeMemory(Device, attachment->mem, nullptr);
}

void VkDriverBase::prepareParaRenderRes()
{
 
	int	numObjectsPerThread = 1;
	
	u32 numThreads = (u32) threadPool.getThreadCount();
	threadData.resize(numThreads);
	for (u32 i = 0; i < numThreads; i++) {
		ThreadData* thread = &threadData[i];
		 
		// Create one command pool for each thread
		VkCommandPoolCreateInfo cmdPoolInfo = vks::initializers::commandPoolCreateInfo();
		cmdPoolInfo.queueFamilyIndex = SwapChain.queueNodeIndex;
		cmdPoolInfo.flags = VK_COMMAND_POOL_CREATE_RESET_COMMAND_BUFFER_BIT;
		VK_CHECK_RESULT(vkCreateCommandPool(Device, &cmdPoolInfo, nullptr, &thread->commandPool));

		// One secondary command buffer per object that is updated by this thread
		thread->commandBuffer.resize(numObjectsPerThread);
		// Generate secondary command buffers for each thread
		VkCommandBufferAllocateInfo secondaryCmdBufAllocateInfo =
			vks::initializers::commandBufferAllocateInfo(
				thread->commandPool,
				VK_COMMAND_BUFFER_LEVEL_SECONDARY,
				(u32)thread->commandBuffer.size());
		VK_CHECK_RESULT(vkAllocateCommandBuffers(Device, &secondaryCmdBufAllocateInfo, thread->commandBuffer.data()));

		//thread->pushConstBlock.resize(numObjectsPerThread);
		//thread->objectData.resize(numObjectsPerThread);

		//for (uint32_t j = 0; j < numObjectsPerThread; j++) {		}
	}

}
void VkDriverBase::paraRenderBegin(u32 paraCount)
{
	// Inheritance info for the secondary command buffers
	VkCommandBufferInheritanceInfo inheritanceInfo = vks::initializers::commandBufferInheritanceInfo();
	inheritanceInfo.renderPass = curRenderPass();
	// Secondary command buffer also use the currently active framebuffer
	inheritanceInfo.framebuffer = curFrameBuffer();
	paraBeginThreadNum = std::min(paraCount, threadPool.getThreadCount());
	paraItemNum = paraCount;
	for (u32 threadIndex = 0; threadIndex < paraBeginThreadNum; threadIndex++)
	{
		threadData[threadIndex].needBegin = true;
		threadData[threadIndex].inheritanceInfo = inheritanceInfo;
	}
}
void VkDriverBase::onParaThreadBegin(int paraId)
{
	assert(paraId >= 0);
	int threadIndex = paraId % threadPool.getThreadCount();

	//DP(("PARA B %d",paraId));
	if (paraId< threadPool.getThreadCount()) {
		//DP(("PARA B %d BC",paraId));
		ThreadData* thread = &threadData[threadIndex];
		VkCommandBufferBeginInfo commandBufferBeginInfo = vks::initializers::commandBufferBeginInfo();
		commandBufferBeginInfo.flags = VK_COMMAND_BUFFER_USAGE_RENDER_PASS_CONTINUE_BIT;
		commandBufferBeginInfo.pInheritanceInfo = &thread->inheritanceInfo;

		VkCommandBuffer cmdBuffer = thread->commandBuffer[0];

		VK_CHECK_RESULT(vkBeginCommandBuffer(cmdBuffer, &commandBufferBeginInfo));
		int w, h;
		if (_currentRT) {
			w = _currentRT->getSize().Width;
			h = _currentRT->getSize().Height;
		}
		else {
			w = ScreenSize.Width;
			h = ScreenSize.Height;
		}
		VkViewport viewport = { 0.f, (float)h,(float)w,	-(float)h, 0.0f, 1.0f };	//VkViewport viewport = { 0.f, 0,(float)ScreenSize.Width, (float)ScreenSize.Height, 0.0f, 1.0f };
		vkCmdSetViewport(cmdBuffer, 0, 1, &viewport);

		VkRect2D scissor = vks::initializers::rect2D(w, h, 0, 0);
		vkCmdSetScissor(cmdBuffer, 0, 1, &scissor);
	}
}
void VkDriverBase::onParaThreadEnd(int paraId)
{
	assert(paraId >= 0);
	int threadIndex = paraId % threadPool.getThreadCount();
	//DP(("PARA E %d",paraId));
	if (paraId + threadPool.getThreadCount() >= paraItemNum ) {
		//DP(("PARA E %d EC", paraId));
		ThreadData* thread = &threadData[threadIndex];

		VkCommandBuffer cmdBuffer = thread->commandBuffer[0];

		VK_CHECK_RESULT(vkEndCommandBuffer(cmdBuffer));
	}
}
void VkDriverBase::paraRenderEnd()
{
 


	threadPool.wait();
	if (_currentRT) {

		std::vector<VkCommandBuffer>& commandBuffers = _currentRT->getVRP()->multiThreadCMBs;

		// Only submit if object is within the current view frustum
		for (uint32_t t = 0; t < paraBeginThreadNum; t++)
		{
			commandBuffers.push_back(threadData[t].commandBuffer[0]);
		}
	}
	else throw "not support";
	// Execute render commands from the secondary command buffer
	//vkCmdExecuteCommands(currentCmdBuffer(-1), commandBuffers.size(), commandBuffers.data());

}


void VkDriverBase::prepareUI()
{
#if USE_IMGUI
	if (CreatePms.DebugUIOverlay && !OffScreen)
	{
		vks::ImGuiFontManager::InitializeFonts();
		ImGuiStyle& style = ImGui::GetStyle();
		style.AntiAliasedLines = true;
		style.AntiAliasedFill = true;

		// Adjust for better text rendering
		ImGui::GetIO().FontGlobalScale = 1.0f;
		ImGui::GetIO().FontAllowUserScaling = false;
		UIOverlay->device = mDevice;
		UIOverlay->queue = queueRender();
		UIOverlay->shaders = {
			loadShader(uioverlay_vs,sizeof(uioverlay_vs), VK_SHADER_STAGE_VERTEX_BIT),
			loadShader(uioverlay_fs,sizeof(uioverlay_fs), VK_SHADER_STAGE_FRAGMENT_BIT),
		};
		UIOverlay->prepareResources();
		UIOverlay->preparePipeline(PipelineCache, RenderPass1);
		uiReady = true;
	}
#endif
}

void VkDriverBase::drawUI(const VkCommandBuffer commandBuffer)
{
#if USE_IMGUI	
	if (!renderUI || !uiReady)
		return;

	const VkViewport viewport = vks::initializers::viewport((float)ScreenSize.Width, (float)ScreenSize.Height, 0.0f, 1.0f);
	const VkRect2D scissor = vks::initializers::rect2D(ScreenSize.Width, ScreenSize.Height, 0, 0);
	vkCmdSetViewport(commandBuffer, 0, 1, &viewport);
	vkCmdSetScissor(commandBuffer, 0, 1, &scissor);

	UIOverlay->draw(commandBuffer);
#endif
	
}

void irr::video::VkDriverBase::copyVkImage(uint32_t width, uint32_t height, 
	VkImage srcImage, VkImageLayout srclayoutFrom, VkImageLayout srclayoutTo, 
	VkImage dstImage, VkImageLayout dstlayoutFrom, VkImageLayout dstlayoutTo) {
	VkCommandPool pool;
	VkCommandBuffer copyCmd = mDevice->createCommandBuffer(VK_COMMAND_BUFFER_LEVEL_PRIMARY,pool, true);

	// Transition destination image to transfer destination layout
	vks::tools::insertImageMemoryBarrier(
		copyCmd,
		dstImage,
		0,
		VK_ACCESS_TRANSFER_WRITE_BIT,
		VK_IMAGE_LAYOUT_UNDEFINED,
		VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL,
		VK_PIPELINE_STAGE_TRANSFER_BIT,
		VK_PIPELINE_STAGE_TRANSFER_BIT,
		VkImageSubresourceRange{ VK_IMAGE_ASPECT_COLOR_BIT, 0, 1, 0, 1 });

	// Transition swapchain image from present to transfer source layout
	vks::tools::insertImageMemoryBarrier(
		copyCmd,
		srcImage,
		VK_ACCESS_MEMORY_READ_BIT,
		VK_ACCESS_TRANSFER_READ_BIT,
		VK_IMAGE_LAYOUT_UNDEFINED,
		VK_IMAGE_LAYOUT_TRANSFER_SRC_OPTIMAL,
		VK_PIPELINE_STAGE_TRANSFER_BIT,
		VK_PIPELINE_STAGE_TRANSFER_BIT,
		VkImageSubresourceRange{ VK_IMAGE_ASPECT_COLOR_BIT, 0, 1, 0, 1 });

	{
		// Otherwise use image copy (requires us to manually flip components)
		VkImageCopy imageCopyRegion{};
		imageCopyRegion.srcSubresource.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
		imageCopyRegion.srcSubresource.layerCount = 1;
		imageCopyRegion.dstSubresource.aspectMask = VK_IMAGE_ASPECT_COLOR_BIT;
		imageCopyRegion.dstSubresource.layerCount = 1;
		imageCopyRegion.extent.width = width;
		imageCopyRegion.extent.height = height;
		imageCopyRegion.extent.depth = 1;

		// Issue the copy command
		vkCmdCopyImage(
			copyCmd,
			srcImage, VK_IMAGE_LAYOUT_TRANSFER_SRC_OPTIMAL,
			dstImage, VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL,
			1,
			&imageCopyRegion);
	}

	// Transition destination image to general layout, which is the required layout for mapping the image memory later on
	vks::tools::insertImageMemoryBarrier(
		copyCmd,
		dstImage,
		VK_ACCESS_TRANSFER_WRITE_BIT,
		VK_ACCESS_MEMORY_READ_BIT,
		VK_IMAGE_LAYOUT_TRANSFER_DST_OPTIMAL,
		dstlayoutTo,
		VK_PIPELINE_STAGE_TRANSFER_BIT,
		VK_PIPELINE_STAGE_TRANSFER_BIT,
		VkImageSubresourceRange{ VK_IMAGE_ASPECT_COLOR_BIT, 0, 1, 0, 1 });

	// Transition back the swap chain image after the blit is done
	vks::tools::insertImageMemoryBarrier(
		copyCmd,
		srcImage,
		VK_ACCESS_TRANSFER_READ_BIT,
		VK_ACCESS_MEMORY_READ_BIT,
		VK_IMAGE_LAYOUT_TRANSFER_SRC_OPTIMAL,
		srclayoutTo,
		VK_PIPELINE_STAGE_TRANSFER_BIT,
		VK_PIPELINE_STAGE_TRANSFER_BIT,
		VkImageSubresourceRange{ VK_IMAGE_ASPECT_COLOR_BIT, 0, 1, 0, 1 });
	
	mDevice->flushCommandBuffer(copyCmd,pool, queueCopy());
}

#if VK_ENABLE_RAYTRACING
bool VkDriverBase::initializeRaytracing() {
    // Check if raytracing is supported
    if (!checkRaytracingSupport()) {
        raytracingCaps.supported = false;
        return false;
    }
    
    // Load raytracing function pointers
    loadRaytracingFunctions();
    
    raytracingCaps.supported = true;
    return true;
}

void VkDriverBase::shutdownRaytracing() {
    // Cleanup will be handled by derived classes
    raytracingCaps.supported = false;
}

bool VkDriverBase::checkRaytracingSupport() {
    // Check if required extensions are available
    uint32_t extensionCount;
    vkEnumerateDeviceExtensionProperties(physicalDevice, nullptr, &extensionCount, nullptr);
    
    std::vector<VkExtensionProperties> availableExtensions(extensionCount);
    vkEnumerateDeviceExtensionProperties(physicalDevice, nullptr, &extensionCount, availableExtensions.data());
    
    bool hasRTPipeline = false;
    bool hasAccelStruct = false;
    bool hasBufferDeviceAddress = false;
    
    for (const auto& extension : availableExtensions) {
        if (strcmp(extension.extensionName, VK_KHR_RAY_TRACING_PIPELINE_EXTENSION_NAME) == 0) {
            hasRTPipeline = true;
        }
        if (strcmp(extension.extensionName, VK_KHR_ACCELERATION_STRUCTURE_EXTENSION_NAME) == 0) {
            hasAccelStruct = true;
        }
        if (strcmp(extension.extensionName, VK_KHR_BUFFER_DEVICE_ADDRESS_EXTENSION_NAME) == 0) {
            hasBufferDeviceAddress = true;
        }
    }
    
    if (!hasRTPipeline || !hasAccelStruct || !hasBufferDeviceAddress) {
        return false;
    }
    
    // Check features - follow rtxON2 pattern exactly (NO ray query)
    rtPipelineFeatures.sType = VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_RAY_TRACING_PIPELINE_FEATURES_KHR;
    accelStructFeatures.sType = VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_ACCELERATION_STRUCTURE_FEATURES_KHR;
    bufferDeviceAddressFeatures.sType = VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_BUFFER_DEVICE_ADDRESS_FEATURES_KHR;
    
    // Chain the feature structures exactly like rtxON2
    rtPipelineFeatures.pNext = &bufferDeviceAddressFeatures;
    accelStructFeatures.pNext = &rtPipelineFeatures;
    
    VkPhysicalDeviceFeatures2 deviceFeatures2{};
    deviceFeatures2.sType = VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_FEATURES_2;
    deviceFeatures2.pNext = &accelStructFeatures;
    
    vkGetPhysicalDeviceFeatures2(physicalDevice, &deviceFeatures2);
    
    raytracingCaps.pipelineSupported = rtPipelineFeatures.rayTracingPipeline;
    raytracingCaps.querySupported = false;  // Not using ray query in this implementation
    
    // Get properties
    rtPipelineProperties.sType = VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_RAY_TRACING_PIPELINE_PROPERTIES_KHR;
    accelStructProperties.sType = VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_ACCELERATION_STRUCTURE_PROPERTIES_KHR;
    
    rtPipelineProperties.pNext = &accelStructProperties;
    
    VkPhysicalDeviceProperties2 deviceProperties2{};
    deviceProperties2.sType = VK_STRUCTURE_TYPE_PHYSICAL_DEVICE_PROPERTIES_2;
    deviceProperties2.pNext = &rtPipelineProperties;
    
    vkGetPhysicalDeviceProperties2(physicalDevice, &deviceProperties2);
    
    raytracingCaps.maxRecursionDepth = rtPipelineProperties.maxRayRecursionDepth;
    raytracingCaps.shaderGroupHandleSize = rtPipelineProperties.shaderGroupHandleSize;
    raytracingCaps.maxShaderGroupStride = rtPipelineProperties.maxShaderGroupStride;
    raytracingCaps.maxGeometryCount = accelStructProperties.maxGeometryCount;
    raytracingCaps.maxInstanceCount = accelStructProperties.maxInstanceCount;
    
    return rtPipelineFeatures.rayTracingPipeline && 
           accelStructFeatures.accelerationStructure && 
           bufferDeviceAddressFeatures.bufferDeviceAddress;
}

void VkDriverBase::loadRaytracingFunctions() {
    // Load raytracing function pointers
    vkCreateAccelerationStructureKHR = (PFN_vkCreateAccelerationStructureKHR)vkGetDeviceProcAddr(Device, "vkCreateAccelerationStructureKHR");
    vkDestroyAccelerationStructureKHR = (PFN_vkDestroyAccelerationStructureKHR)vkGetDeviceProcAddr(Device, "vkDestroyAccelerationStructureKHR");
    vkGetAccelerationStructureBuildSizesKHR = (PFN_vkGetAccelerationStructureBuildSizesKHR)vkGetDeviceProcAddr(Device, "vkGetAccelerationStructureBuildSizesKHR");
    vkCmdBuildAccelerationStructuresKHR = (PFN_vkCmdBuildAccelerationStructuresKHR)vkGetDeviceProcAddr(Device, "vkCmdBuildAccelerationStructuresKHR");
    vkCreateRayTracingPipelinesKHR = (PFN_vkCreateRayTracingPipelinesKHR)vkGetDeviceProcAddr(Device, "vkCreateRayTracingPipelinesKHR");
    vkGetRayTracingShaderGroupHandlesKHR = (PFN_vkGetRayTracingShaderGroupHandlesKHR)vkGetDeviceProcAddr(Device, "vkGetRayTracingShaderGroupHandlesKHR");
    vkCmdTraceRaysKHR = (PFN_vkCmdTraceRaysKHR)vkGetDeviceProcAddr(Device, "vkCmdTraceRaysKHR");
    vkGetAccelerationStructureDeviceAddressKHR = (PFN_vkGetAccelerationStructureDeviceAddressKHR)vkGetDeviceProcAddr(Device, "vkGetAccelerationStructureDeviceAddressKHR");
    vkGetBufferDeviceAddressKHR = (PFN_vkGetBufferDeviceAddressKHR)vkGetDeviceProcAddr(Device, "vkGetBufferDeviceAddressKHR");
    
    // Verify all functions were loaded
    if (!vkCreateAccelerationStructureKHR || !vkDestroyAccelerationStructureKHR ||
        !vkGetAccelerationStructureBuildSizesKHR || !vkCmdBuildAccelerationStructuresKHR ||
        !vkCreateRayTracingPipelinesKHR || !vkGetRayTracingShaderGroupHandlesKHR ||
        !vkCmdTraceRaysKHR || !vkGetAccelerationStructureDeviceAddressKHR ||
        !vkGetBufferDeviceAddressKHR) {
        raytracingCaps.supported = false;
    }
}
#endif // VK_ENABLE_RAYTRACING

#endif