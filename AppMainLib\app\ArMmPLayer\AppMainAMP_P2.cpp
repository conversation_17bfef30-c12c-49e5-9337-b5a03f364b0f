﻿// Associated header: AppMainAMP.h
#include "AppGlobal.h"
#include "cppIncDefine.h"
#include "AppMainAMP.h"
#include "ardatcam.inc"
#include "VulkanRenderer/VkMr2D.h"
#include "irrmmd/irrmmd.h"
#include "uautils.h"
#include "irrmmd/PhyObjMan.h"
#if USE_OXR
#include "oxr/BasicXrApp/OxrMan.h"
#endif
#include "sns/SnVkFuild/SnVkFluid.h"


#define DPT(x) DP(x) // this->testTextFw(true,  x )

using namespace ualib; using namespace irr; using namespace irr::core; using namespace irr::video; using namespace irr::scene; using namespace EQVisual; using namespace AppNameSpace;  using namespace saba;
using namespace glm;

void AppMainAMP::initData()
{
#if 0
	auto img = Driver->createImageFromFile("res/ardatcam.png");
	Driver->writeImageToFile(img, "out/ardatcam.inc", 0);
	img->drop();
#endif


	auto img = Driver->createImageFromData(ECF_A8R8G8B8, { ardatcamBgraDatW ,ardatcamBgraDatH },ardatcamBgraDat);
	//Driver->writeImageToFile(img, "out/ardatcam.inc", 0);
	texWaterMark = Driver->addTexture("texWaterMark", img);
	img->drop();
	if (Ctx->isApp(APPID_WinTest))
	copyEqvdef();
}

void AppMainAMP::copyEqvdef()
{
#if IS_WIN
	bool copyed = Ctx->UaCopyFile(irr::io::path(

		MMD_TREE_FW?"eqv/mf/a treefw.eqvdef":  "eqv/mf/a aaaaaaaaaaaaaaaaaaaaa.eqvdef"

	), Ctx->getDataFilePath("style.eqvdef"));
	//assert(copyed);
#endif
}

void AppMainAMP::arUdpate()
{
#if IS_WIN
	if (Ctx->gd.fpsMode)
	{
		SetCursorPos(-300, 0);
	}
#endif
	if (SphereCamShot >= 0) {
		bool ok = false;
		while (!ok) {
			if (SphereCamShot >= BALL_SHOT_COUNT) {
				SphereCamShot = -1;
				stopRecord();
				vp.writeFrameToFile = false;
				break;
			}
			auto cam = Ctx->gd.CamNormal; auto sb = curChar();
			core::vector3df FibonacciSphere(float num, float i);
			core::vector3df cpos = FibonacciSphere(BALL_SHOT_COUNT, SphereCamShot);
			auto node = sb->Pmx->GetNodeManager()->FindNode(BALL_SHOT_TGTNODE);
			auto m = node ? node->GetGlobalTransform() : glm::mat4(1);

			m = sb->mmdBaseMat * m;
			vp.ballShotPos = glm::vec3(m[3]) + glm::vec3(0, 300, 0);

			cam->setPosition(vp.ballShotPos + cpos * BALL_SHOT_DISTANCE * 0.6f);
			cam->setTarget(vp.ballShotPos);
			auto deg = cpos.normalize().angleBetweenInDegree(-core::matrix4(m).getRotationDegrees().rotationToDirection());
			DP(("deg %f", deg));
			ok = deg < 90;

			SphereCamShot++;
			vp.writeFrameToFile = true;
}
	}

#if HAS_ARCORE
	uint64_t addr = 0;
	struct AhBufParam apm = {
			(u32)Ctx->gd.scrWidth, (u32)Ctx->gd.scrHeight, 0, 0,
			&addr,

			//((video::VkTexture*)(Ctx->texMidRT[0]))->getSysHardwareBuffer()
	};
	apm.cb = [this](int w, int h, int fmt, char* pb[], int len[], int numPlane) {
		if (w < 2 || h < 2 || vp.working) return;
		UP_LOCK_GUARD(DrvOfs->dsd.driverLock);
		auto& fv = vp.fvAR;

		if (fv.texY && fv.texY->getSize() != dimension2du(w, h)) {
			DrvOfs->freeTexture(fv.texY); fv.texY = nullptr;
			DrvOfs->freeTexture(fv.texUV); fv.texUV = nullptr;
			DrvOfs->freeTexture(fv.texVideo); fv.texVideo = nullptr;
			DrvOfs->freeTexture(fv.texVideoRT); fv.texVideoRT = nullptr;
		}
		bool c21 = 1;//fr.colorFormat == 21;
		//DP(("texy %p",fv.texY));
		if (!fv.texY) {
			IImage* imageY = DrvOfs->createImageFromData(ECF_R8, core::dimension2du(w, h),
				(void*)pb[0], true, false);
			//imageY->setPitch(w);
			fv.texY = DrvOfs->addTexture("texY", imageY);
			IImage* imageUV = DrvOfs->createImageFromData(c21 ? ECF_R8G8 : ECF_R8,
				core::dimension2du(w / 2, c21 ? h / 2 : h), (void*)pb[1], true, false);
			fv.texUV = DrvOfs->addTexture("texUV", imageUV);
			imageY->drop();	imageUV->drop();

			fv.texVideo = DrvOfs->addTexture({ (u32)w, (u32)h }, "<TsfSrcDbg>videoTex");

			fv.texVideoRT = DrvOfs->addRenderTargetTexture({ (u32)w, (u32)h });

			DP(("arcbimg %d %d pl %d", w, h, numPlane));
			Eqv->ARCamRotation = 90;
			vp.ivpm.w = w; vp.ivpm.h = h;
			arRoot->onArDatLoaded(true);
		}
		else {
			void* pbY = fv.texY->lock(ETLM_WRITE_ONLY); memcpy(pbY, pb[0], w * h); fv.texY->unlock();
			void* pbUV = fv.texUV->lock(ETLM_WRITE_ONLY); memcpy(pbUV, pb[1], (w / 2) * (c21 ? h / 2 : h) * (c21 ? 2 : 1)); fv.texUV->unlock();
		}
		if (vp.mtYuv2rgb == irr::video::EMT_NONE)
			vp.mtYuv2rgb = Ctx->getVkDrv(1)->getMT(c21 ? Fx2DIdEnum::Fx2D_Yuv420spCvt : Fx2DIdEnum::Fx2D_Yuv420pCvt);


		//if (vp.mediaListId >= 0)
		{


			if (vp.mFF->getNeedVideo() && fv.texY && fv.texUV)
			{
				DrvOfs->setRenderTarget(fv.texVideoRT, true, true, 0);
				video::SMaterial mr;
				mr.MaterialType = vp.mtYuv2rgb;// VkDrv->getMT(vp.colorFormat == 21 ? Fx2DIdEnum::Fx2D_Yuv420spCvt : Fx2DIdEnum::Fx2D_Yuv420pCvt);;
				mr.setTexture(0, fv.texY);
				mr.setTexture(1, fv.texUV);
				SColor col[4] = { 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF, 0xFFFFFFFF };
				DrvOfs->draw2DImageMr(mr, fv.texVideoRT->getRectI(), recti(0, 0, w, h), nullptr, col);
				DrvOfs->setRenderTarget(0, true, true);
				//UP_LOCK_GUARD(Driver->dsd.driverLock);

				fv.texVideoRT->copyTo(fv.texVideo);

			}
		}
		};
	while (vp.working) { ualib::SleepMs(100); }
	while (!arDatUpdated) {
		ualib::SleepMs(3);
		//DP(("waiup"));
	}
	arcoreUpdateFrame(&apm);
	if (apm.gotAR)
	{
		MatrixRecorder::DrDataStruct ds;
		bool por = 1;//Ctx->gd.scrHeight > Ctx->gd.scrWidth;
		if (por) Eqv->ARCamRotation = 90;
		//Ctx->gd.CamNormal->setFOV(Eqv->JsonToDS(us, js, ds, 21, false));
		ds.d.pos = apm.campos;
		quaternion qt = apm.camQt;
		vector3df rtt;//=apm.camrtt;
		qt.toEuler(rtt);
		rtt *= RADTODEG;
		rtt.set(-(rtt.X), -(rtt.Y), rtt.Z);
		ds.d.rtt = rtt;
		ds.d.scaleAdd = 1.f;
		//static matrix4 mcrCAMY_ADDRTT;mcrCAMY_ADDRTT.setRotationDegrees({0, CAMY_ADDRTT, 0});mcrCAMY_ADDRTT.transformVect(ds.d.pos);
		ds.d.pos.Z = -ds.d.pos.Z;

		//ds.d.pos.Y=-ds.d.pos.Y;

		if (setAROriginPoint) {
			setAROriginPoint = false;
			arOriginPoint = ds.d.pos;
			arOriginPoint += rtt.rotationToDirection() * 0.1f;

		}
		if (hello_ar::gAr->hasHit) {
			hello_ar::gAr->hasHit = false; arRoot->Cs.resetOrigin = 0;
			arOriginPoint = glm::vec3(0, 0, 0);
			quaternion qt(hello_ar::gAr->attiHit.rqt);
			auto& rtt = arOriginRtt;
			qt.toEuler(rtt);
			rtt *= RADTODEG;
			rtt.set(-(rtt.X), -(rtt.Y), rtt.Z);
			if (arRoot->curArSn) arRoot->curArSn->setRotation(arOriginRtt);
		}
		ds.d.pos -= arOriginPoint;
		Eqv->UpdateCamState(&ds, por);
		arDatUpdated = false;
		if (1)
		{
			float asW = apm.mp[0][0], asH = apm.mp[1][1];// asW = matP[0], asH = matP[5];
			float as = asH / asW;
			float fovH = (atan(1.f / asH)) * 2;//(atan(1.f /  ((portrait) ? asW : asH))) * 2;
			Ctx->gd.CamNormal->setFOV(fovH);
		}
		//DP(("gotAR %3.7f,%3.7f,%3.7f  %3.7f,%3.7f,%3.7f",ds.d.pos.X,ds.d.pos.Y,ds.d.pos.Z, rtt.X,rtt.Y,rtt.Z));

	}
	/*    if (!texAHB)
		{
			DP(("addr %p", addr));
			char szTn[128] = "midrt";
			snprintf(szTn, 128, "<EHB> %llu", addr);

			texAHB = Driver->addRenderTargetTexture(core::dimension2du(apm.w, apm.h), szTn,
													irr::video::ECF_A8R8G8B8);
			//auto ah=(uint64_t)VKTEX(texAHB)->getSysHardwareBuffer();
			//assert(ah==addr);
		}*/
#endif


}

void AppMainAMP::phyFrameUpdate()
{
  for (auto sb:mmd->sabas) {
		MMDNode* nds[] = { sb->ndHandL, sb->ndHandR,  };
		for (int j = 0; j < 2; j++) {
			auto nd = nds[j];
			if (!nd || !nd->rb0) continue;
			 

			addFluidParticlesGrid(2, 2, 2, -1, -1, -1, nd->transformVec({ 0,-1,0 }), nd->exd.irrSpeed/100.f);
		}

	}

#if MA_CATCH_DROPLETS
	//rain
	if (0) for (int i = 0; i < 3; i++) {
		vec3 pos = vec3(0, 37, 0) + ualib::UaRandVec3() * vec3(1, 0, 1) * 30.f;

		addFluidParticlesGrid(2, 2, 2, -1, -1, -1, pos, { 0,10,0 });
	}

	//if (Ctx->gd.frameCount % 2) return;
	static int Stages[32] = { 0 };
	static float StageTimer[32] = { 0 };
	static vec3 Targets[32] = { vec3(0) };
	static int Locked[32] = { 0 };
	static int minI[32] = {};
	static float R[32] = { 0 };
	static float rtMul[32] = {};
	const float T = 0.99f;


	for (int i = 0; i < mmd->sabas.size();i++) {
		auto sb = mmd->sabas[i];
		auto& stg = Stages[i];
		auto& stm = StageTimer[i];
		stm += gFrameTime;
		vec3 src = sb->ndUpper2->rb0->getPosition();
		switch (stg) {
		case 0:
		{
			if (stm < 0.25f) break;
			mmd->fluidNode->findDropletsAsync([=](const std::vector<glm::vec4>& dpsR, const glm::vec4&) {
				if (dpsR.empty()) return;
				auto dps = dpsR;
				
				for (auto dp : dps)
					sbFw2LineD("sw1s", dp, vec3(dp) + vec3(0, dp.w, 0), 0xFFFFFFFF, 60);
				sbFw2LineD("sw21s", dps[0], vec3(dps[0]) + vec3(0, dps[0].w, 0), 0xFFFF00FF, 60);
				if (dps.size() > mmd->sabas.size())
					dps.resize(std::max(1ull, std::min(mmd->sabas.size()*2,dps.size())));
				vec4 dpR = dps[0];
				vec3 dp = vec3(dpR.x, dpR.y, dpR.z);
				//find closest droplet
				float minDis = 1000.f;
				for (int id = 0; id < dps.size(); id++) {
					if (Locked[id]) continue;
					vec3 d = dps[id]; float dis = glm::length(d - src);
					if (dis < minDis) {
						dp = d; minDis = dis; minI[i] = id;
					}

				}
				Locked[minI[i]] = 1;
				R[i] = dps[minI[i]].w + 0.5f;
				Targets[i] = dp;
				auto& stg = Stages[i];
				auto& stm = StageTimer[i];
				stg = 1; stm = 0;
				});
		}
			break;
		case 1:
		{
			sb->ndCtr->rb0->addRotationToMatOnNode_MatRttResetXZ(glm::mat4(1), 6000.f*stm, -piFloat / 2, 0);
			sb->ndUpper2->rb0->addRotationToMatOnNode_MatRttResetXZ(glm::mat4(1), 6000.f * stm, -piFloat *0.3f, 0);
			if (stm < 0.3f) {
				MMDNode* nds[] = { sb->ndArm1L, sb->ndArm1R, sb->ndLegL, sb->ndLegR };
				for (int j = 0; j < 4; j++) {
					auto nd = nds[j];
					if (!nd || !nd->rb0) continue;
					nd->rb0->addRotationToMatOnNode_MatRttResetXZ(500.f+2000.f*stm );
				}
				
			}
			else if (  stm<0.35f) {
				//sb->ndCtr->rb0->addLinearVel(vec3(0, -60 * stm, 0));
				sb->ndLegL->rb0->addLinearVel(vec3(0, -360 * stm, 0));
				sb->ndLegR->rb0->addLinearVel(vec3(0, -360 * stm, 0));
				//sb->ndUpper2->rb0->addLinearVel(vec3(0, -116 * stm, 0));
				sb->ndArm1L->rb0->addLinearVel(vec3(0, -60 * stm, 0));
				sb->ndArm1R->rb0->addLinearVel(vec3(0, -60 * stm, 0));

			}
			else {
				// Extended stage selection with more action modes
				if (R[i] < 0.f) {
					stg = 12;
				} else {
					float rand = UaRandF();
					if (rand < 0.2f) {	stg = 11; DP((L"Original jump attack"));	}      // Original jump attack
					//else if (rand < 0.4f) { stg = 12; DPT((L"Original dive attack")); }
					//else if (rand < 0.45f) { stg = 13; DPT((L"Spinning attack")); }      // Spinning attack
					//else if (rand < 0.55f) { stg = 14; DPT((L"Multi-hit combo")); }       // Multi-hit combo
					else { stg = 15; rtMul[i] = UaRandF() < 0.5 ? -1.6: 1.1; DP((L"High roll")); }        
				}
				stm = 0;
			}
		}
		break;

		case 11:
		{
			auto dp = Targets[i];
			vec3 dir = glm::normalize(vec3(dp) - src);
			quat rtq = vec3(vector3df(dp - sb->Rb0()->getPosition()).getHorizontalAngleRad());
			if (stm < T*0.5f) {


				auto v = glh::calcVelocityP2PinTimeGuess(src, vec3(dp), GRAVITY_VEC, 1.f * (T - stm * 0.5f), 0.5f, 60 * SABA_PHYSICS_FRAMESTEP);
				//sb->ndCtr->rb0->addRotationToMatOnNode(mat4(rtq * glm::rotation(vec3(0, 0, -1), dir		)), 1000.f );
				if (stm < T * 0.05f)
				{
					sb->ndUpper2->rb0->setLinearVel(v*0.5f);
					sb->ndUpper2->rb0->setLinearVel(v);
				}
				else if (stm < T * 0.25f) {
					sb->Pmx->setBodyVel(v*0.5f, stm > 0.15f);
					sb->ndHead->rb0->setLinearVel(v);
					sb->ndCtr->rb0->setLinearVel(v);
					sb->ndUpper->rb0->setLinearVel(v);
					sb->ndUpper2->rb0->setLinearVel(v);
					sb->ndLower->rb0->setLinearVel(v);
				}
				else {
 
					sb->ndCtr->rb0->setLinearVel(v);
					sb->ndUpper->rb0->setLinearVel(v);
					sb->ndUpper2->rb0->setLinearVel(v);
 

				}
			}
			if (stm<T *0.8f){
				sb->Pmx->scaleBodyVel(vec3(0.98, 1, 0.98), 0);
				MMDNode* nds[] = {sb->ndHandL, sb->ndHandR, sb->ndFootL, sb->ndFootR};
				vec3 ofs[] = { vec3(-9, 6, -1), vec3(9,6, -1), vec3(-10, -16, 0), vec3(10, -16, 0) };
				sbFw2LineD("sw2", sb->Rb0()->getPosition(), dp, 0xFF00FFFF, 60);
				for (int j = 0; j < 4; j++) {
					auto nd = nds[j];
					if (!nd || !nd->rb0) continue;
					vec3 pos = nd->rb0->getPosition();
					
					vec3 tgt = dp + rtq*ofs[j];
					sbFw2LineD("sw", pos, tgt, 0xFF00FF00, 30);
					vec3 dir = glm::normalize(tgt - pos);
					nd->rb0->addLinearVel(dir * 60.f*(0.5f+0.5f*(stm-0.5f)));
					 
				}
			}
			sb->ndUpper2->rb0->rotateLocalDirTo(vec3(0, 0, -1), dir, 2000);
			if (stm > T)
				stg = 0, stm = 0, Locked[minI[i]] = 0;;
		}
			break;
		 
		case 12:
		{
			auto dp = Targets[i];
			vec3 dir = glm::normalize(vec3(dp) - src);
			quat rtq = vec3(vector3df(dp - sb->Rb0()->getPosition()).getHorizontalAngleRad())  ;
			if (stm < T * 0.5f) {


				auto v = glh::calcVelocityP2PinTimeGuess(src, vec3(dp), GRAVITY_VEC, 1.f * (T - stm * 0.5f), 0.5f, 60 * SABA_PHYSICS_FRAMESTEP);
				// sb->ndCtr->rb0->addRotationToMatOnNode(mat4(rtq * glm::rotation(vec3(0,-1, 0), dir		)), 1000.f );
				if (stm < T * 0.05f)
				{
					sb->ndUpper2->rb0->setLinearVel(v * 0.5f);
					sb->ndUpper2->rb0->setLinearVel(v);
				}
				else if (stm < T * 0.25f) {
					sb->Pmx->setBodyVel(v * 0.5f, stm > 0.15f);
					sb->ndHead->rb0->setLinearVel(v);
					sb->ndCtr->rb0->setLinearVel(v);
					sb->ndUpper->rb0->setLinearVel(v);
					sb->ndUpper2->rb0->setLinearVel(v);
					sb->ndLower->rb0->setLinearVel(v);
				}
				else {

					sb->ndCtr->rb0->setLinearVel(v);
					sb->ndUpper->rb0->setLinearVel(v);
					sb->ndUpper2->rb0->setLinearVel(v);


				}
			}
			if (stm < T * 0.8f) {
				sb->Pmx->scaleBodyVel(vec3(0.98, 1, 0.98), 0);
				MMDNode* nds[] = { sb->ndHandL, sb->ndHandR, sb->ndFootL, sb->ndFootR,sb->ndCtr };
				vec3 ofs[] = { vec3(-9, 16, 16), vec3(9,16, 16), vec3(-10, 6 , -9), vec3(10,6 , -9),vec3(0,0,0)};
				float mul[] = { 0.95f, 0.95f, 0.5f, 0.5f, 1.f };
				sbFw2LineD("sw2", sb->Rb0()->getPosition(), dp, 0xFF00FFFF, 60);
				for (int j = 0; j < 5; j++) {
					auto nd = nds[j];
					if (!nd || !nd->rb0) continue;
					vec3 pos = nd->rb0->getPosition();

					vec3 tgt = dp + rtq * ofs[j];
					sbFw2LineD("sw", pos, tgt, 0xFF00FF00, 30);
					vec3 dir = glm::normalize(tgt - pos);
					nd->rb0->addLinearVel(dir * 10.f * (0.5f + 0.5f * (stm - 0.5f)));

				}
			}
			sb->ndCtr->rb0->rotateLocalDirTo(vec3(0, -1, 0), dir, 2000);
			sb->ndUpper2->rb0->rotateLocalDirTo(vec3(0, -1, 0), dir, 2000);
			if (stm > T)
				stg = 0, stm = 0, Locked[minI[i]] = 0;;
		}
		break;

		case 13: // Spinning attack
		{
			auto dp = Targets[i];
			vec3 dir = glm::normalize(vec3(dp) - src);
			quat rtq = vec3(vector3df(dp - sb->Rb0()->getPosition()).getHorizontalAngleRad());

			if (stm < T * 0.3f) {
				// Initial spin-up phase
				sb->ndCtr->rb0->addRotationToMatOnNode_MatRttResetXZ(glm::mat4(1), 8000.f * stm, 0, piFloat * 4 * stm);
				sb->ndUpper2->rb0->addRotationToMatOnNode_MatRttResetXZ(glm::mat4(1), 8000.f * stm, 0, piFloat * 4 * stm);

				// Extend arms for spinning
				MMDNode* armNodes[] = { sb->ndArm1L, sb->ndArm1R };
				for (int j = 0; j < 2; j++) {
					auto nd = armNodes[j];
					if (!nd || !nd->rb0) continue;
					nd->rb0->addRotationToMatOnNode_MatRttResetXZ(1000.f + 3000.f * stm);
				}
			}
			else if (stm < T * 0.7f) {
				// Launch towards target with spin
				auto v = glh::calcVelocityP2PinTimeGuess(src, vec3(dp), GRAVITY_VEC, 1.f * (T - stm * 0.3f), 0.5f, 60 * SABA_PHYSICS_FRAMESTEP);
				sb->Pmx->setBodyVel(v * 0.8f, true);

				// Continue spinning
				sb->ndCtr->rb0->addRotationToMatOnNode_MatRttResetXZ(glm::mat4(1), 12000.f, 0, piFloat * 8);
				sb->ndUpper2->rb0->addRotationToMatOnNode_MatRttResetXZ(glm::mat4(1), 12000.f, 0, piFloat * 8);

				// Spinning limbs for damage
				MMDNode* nds[] = { sb->ndHandL, sb->ndHandR, sb->ndFootL, sb->ndFootR };
				for (int j = 0; j < 4; j++) {
					auto nd = nds[j];
					if (!nd || !nd->rb0) continue;
					nd->rb0->addLinearVel(vec3(cos(stm * piFloat * 8), 0, sin(stm * piFloat * 8)) * 40.f);
				}
			}
			else {
				// Landing phase
				sb->Pmx->scaleBodyVel(vec3(0.95, 1, 0.95), 0);
			}

			sbFw2LineD("spin", sb->Rb0()->getPosition(), dp, 0xFFFF8000, 60);
			if (stm > T)
				stg = 0, stm = 0, Locked[minI[i]] = 0;
		}
		break;

		case 14: // Multi-hit combo
		{
			auto dp = Targets[i];
			vec3 dir = glm::normalize(vec3(dp) - src);
			quat rtq = vec3(vector3df(dp - sb->Rb0()->getPosition()).getHorizontalAngleRad());

			float comboPhase = fmod(stm * 3.0f, 1.0f); // 3 hits per second
			int hitCount = (int)(stm * 3.0f);

			if (stm < T * 0.8f && hitCount < 5) {
				// Multiple quick strikes
				if (comboPhase < 0.3f) {
					// Strike preparation
					vec3 strikeDir = dir + vec3(sin(hitCount * piFloat * 0.5f), 0, cos(hitCount * piFloat * 0.5f)) * 0.3f;
					auto v = glh::calcVelocityP2PinTimeGuess(src, vec3(dp), GRAVITY_VEC, 0.3f, 0.5f, 60 * SABA_PHYSICS_FRAMESTEP);

					sb->ndUpper2->rb0->setLinearVel(v * 0.6f);
					sb->ndCtr->rb0->setLinearVel(v * 0.6f);

					// Alternate between left and right strikes
					MMDNode* strikeNode = (hitCount % 2 == 0) ? sb->ndHandL : sb->ndHandR;
					if (strikeNode && strikeNode->rb0) {
						vec3 strikePos = vec3(dp) + strikeDir * 15.f;
						strikeNode->rb0->addLinearVel(glm::normalize(strikePos - strikeNode->rb0->getPosition()) * 80.f);
					}
				}
				else if (comboPhase < 0.6f) {
					// Strike execution
					sb->Pmx->scaleBodyVel(vec3(0.9, 1, 0.9), 0);
				}

				// Visual feedback for combo
				vec3 comboOffset = vec3(sin(hitCount * piFloat), cos(hitCount * piFloat), 0) * 5.f;
				sbFw2LineD("combo", sb->Rb0()->getPosition(), dp + comboOffset, 0xFF8000FF, 30);
			}
			else {
				// Final recovery
				sb->Pmx->scaleBodyVel(vec3(0.95, 1, 0.95), 0);
			}

			if (stm > T)
				stg = 0, stm = 0, Locked[minI[i]] = 0;
		}
		break;

		case 15: // jump high roll
		{
			auto dp = Targets[i];
			vec3 dir = glm::normalize(vec3(dp) - src);
			quat rtq = vec3(vector3df(dp - sb->Rb0()->getPosition()).getHorizontalAngleRad());

			 if (stm < T * 0.3f) {
				// Launch high into air
				auto v = glh::calcVelocityP2PinTimeGuess(src, vec3(dp) + vec3(0, 1, 0), GRAVITY_VEC, 1.2f, 0.5f, 60 * SABA_PHYSICS_FRAMESTEP);
				sb->Pmx->setBodyVel(v, true);

			}
			 if (stm < T * 0.5f) {
				 sb->ndCtr->rb0->addTorqueLocal(vec3(200, 0, 0)* rtMul[i]);
				 sb->ndUpper2->rb0->addTorqueLocal(vec3(100, 0, 0)* rtMul[i]);

			 }else if (stm < T * 0.97f) {
				// Aerial control and dive
				vec3 airDir = glm::normalize(vec3(dp) - src);
				sb->ndUpper2->rb0->rotateLocalDirTo(vec3(0, -1, 0), airDir, 3000); 

			}
 

			sbFw2LineD("pt1s", sb->Rb0()->getPosition(), dp, 0xFF0080FF, 60);
			if (stm > T)
				stg = 0, stm = 0, Locked[minI[i]] = 0;
		}
		break;

		case 16: // Ground slam
		{
		 
		}
		break;
		}
	}


#endif 


#if MA_POLE_DANCE
	if (poleDance) for (auto sb0: mmd->sabas)
	{
		float3 axis = float3(0,  1.f, 0);
		float ar = sb0->ndRbRoot->phyAnim==0?1:sb0->rd.ratio; DP(("ar %f", ar));
		float bdRate = 0.5f;
		sb0->ndUpper2->rb0->addVelToRotateOnAxis(float3(0, 0, 0), axis, 20.f,30.f * ar *bdRate);
		sb0->ndCtr->rb0->addVelToRotateOnAxis(float3(0,0,0),axis,10.f,10.f * ar * bdRate);
		//sb0->ndUpper2->rb0->addLinearVel(float3(0, 3, 0));
		//sb0->ndFootR->rb0->addLinearVel(float3(0,10, 0));
		
		
		//sb0->ndLeg1R->rb0->setLinearVelToPos(sb0->ndLegR->getGlobalPos(), 1.f * ar);
		sb0->ndArm1R->rb0->addVelToRotateOnAxis(float3(0, 0, 0), axis,1.f, -360.f * ar);

		sb0->ndArm1L->rb0->addVelToRotateOnAxis(float3(0, 0, 0), axis, 10.f, 8* ar);
		sb0->ndFootL->rb0->addVelToRotateOnAxis(float3(0, 0, 0), axis, 3.1f, 8 * ar);

		sb0->ndLegR->rb0->addVelToRotateOnAxis(float3(0, 0, 0), axis, -10.1f, -30 -90.f * ar);
		sb0->ndLeg1R->rb0->addVelToRotateOnAxis(float3(0, 0, 0), axis, -10.1f,-30  -90.f * ar);
		sb0->ndFootR->rb0->addVelToRotateOnAxis(float3(0, 0, 0), axis, -30.1f, -96.f );
 
	{
		float v = 0.1f + 0.65f * sin(gPhyTime * piFloat * 2 * 3);
 
		sb0->Rb0()->addLinearVel(glm::vec3(0, v, 0) * 10.f);
		sb0->mpE->addWeight = (std::clamp(v, 0.f, 1.95f));
	}

	}
#endif
}

void AppMainAMP::phyModifyUpdate()
{
	if (!frameAddObj || Ctx->scenePaused) return;
	static int cc = 0; cc++;
	if (cc%1!=0) return;
	float3 size( 3.2, 2.44,  2.66);
	float sc =   0.25f;
	size *= sc;
	float xl = 2.25/ sc, yl = 1, zl =2.25/ sc;
	int maxi = 1 / sc + 0.5f;
	for (int i=0;i< maxi;i++)
	{
		bool last = i == maxi - 1; bool last2 = i == maxi - 2;
		float3 pos = (
			//last ? sb0->ndUpper2->rb0: 
			sb0->Rb0())->getPosition()
			+ float3(0,-100,0)
			+ float3(-xl / 2 + UaRandF() * xl, size.y / 2 + UaRandF() * yl, -zl / 2 + UaRandF() * zl) * sc *(last  ? 0.5f : 1);
		pos = float3(0, -6, 0)+ UaRandVec3()*1.f;
		pos = glh::matTransformVec(sb0->Rb0()->GetTransform(), pos);
		if (pos.y < size.y / 2) pos.y = size.y / 2;
		PhyObjParam pm{ 32 ,30.f* sc* sc* sc, size ,pos,{},{ 0,0,0 }, };
		pm.operation = 1;    
		pm.pmxrb.m_repulsion = 0.1f;
		pm.pmxrb.m_friction = 100.f;
		pm.pmxrb.m_collideMask32 = -1;// 0x0000fffe;
		pm.tag = 0;
		static auto sms = SceneManager->getMesh("data/kd.obj"); 
		pm.meshScale = float3(1.6) * sc;
		pm.mesh = sms;
		pm.vel = float3(0, 0, 0) + sb0->ndCtr->exd.irrSpeed* 0.0f  +glm::normalize(UaRandVec3()) * 10.f;
		glh::matRotateVec(sb0->Rb0()->GetTransform(), pm.vel);
		pm.timer = 10.f;
		pm.color = 0xFFFFFFFF;
		//pm.pmxrb.frameChgMask = 1; pm.pmxrb.frameAddLinearVel = { 0,frameAddObjSpdMul * 1.7f,0 };
		auto rb = sb0->Pom->addObj(pm)->rb;

		rb->usrDat.callHitCb = true; rb->cbHit = [&](saba::PhysicsEngineObjectUserData* hit) {
			static int cc = 0;
			float implen = glm::length(hit->hitImp);
			if (implen < 0.1f) return;
			DP(("imp2 %f", implen));
			Ctx->Midi.playNote(Driver->dsd.time, 10 + (cc++ % 6), core::s32_clamp(66 + UaRand(20), 0, 100) * (1.f + gTimeMul) / 2,
				glm::clamp(implen * 2.f, 0.1f, 1.f), 0
			);
			};
	}

 
}

void AppMainAMP::oxrBegin()
{
#if USE_OXR
	OxmCreateParam ocp;
	oudRhPos = new ExponentialMovingAverageXYZ(8);
	oudRhRtt = new ExponentialMovingAverageXYZ(8);
	ocp.onOxrUpdate = [=](OxmUpdateData& oud) {
		curOud = origOud = oud;
		curOud.h[1].t = origOud.h[1].t - ctrOud.h[1].t;
		curOud.h[1].r = origOud.h[1].r * glm::inverse(ctrOud.h[1].r);
		oudRhPos->addAndFloatValues(curOud.h[1].t.x, curOud.h[1].t.y, curOud.h[1].t.z);
		glm::vec3 rtt = glm::eulerAngles(curOud.h[1].r);
		oudRhRtt->addAndFloatValues(rtt.x, rtt.y, rtt.z);
		curOud.h[1].r = rtt;
		return 0;
		};
	ocp.onOxrButtonEvent = [=](XRBtnEnum bt, int handid) {

		Ctx->getLib()->libPostCommand(801, bt, handid);
		return 0;
		};
	OxrManInit(ocp);
#endif
}

void AppNameSpace::AppMainAMP::oxrUpdate()
{
#if USE_OXR
	for (int i = 1; i < 2; i++) {
		uint32_t oxrCtrl = Ctx->gd.oxrControl[i];
		auto& co = curOud.h[i];



		if (oxrCtrl & 0x10) arRoot->forEachArChild([=](SnArItem* sn) {
			if (!sn->saba || sn->saba->rootLocked) return;

			auto cam = Ctx->gd.CamNormal;
			auto mc = glm::mat4(cam->getViewMatrix());
			auto sb = sn->saba;
			auto pmx = sb->Pmx;
			auto mmc = glm::mat4(sb->matAbsInv * mc);
			auto mci = glm::inverse(mc);
			auto mmci = glm::inverse(mmc);

			glm::vec3 tr = curOud.h[i].t;
			if (oxrCtrl == 0x11)
			{
				pmx->rt1Tr = tr * 20.f;
				pmx->rt1Rt = glm::eulerAngles(curOud.h[i].r);
			}
			else if (sn->saba->xrMode == 0)
			{
				float mul = 10000.f;
				pmx->rootTr = tr * mul;
				pmx->rootTr =
					(glm::mat4(glm::mat3(sb->matAbsInv))
						//*mci// glm::mat4(glm::quat(mci))
						//* glm::rotate(glm::mat4(1), -core::PI, glm::vec3(0, 1, 0))
						* glm::translate(glm::mat4(1), pmx->rootTr)
						//* mc// glm::mat4(glm::quat(mc))
						//* glm::vec4(pmx->rootTr, 1) 
						)[3];
				pmx->rootRt = glm::eulerAngles(curOud.h[1].r);

				DP(("sn->pos %f,%f", sn->saba->getAbsolutePosition().X, pmx->GetNodeManager()->getRootNode()->GetGlobalTransform()[3][0]));
			}
			else if (sb->xrMode & 0x10000)
			{
				sb->oud = curOud;
			}
			});

		else if (oxrCtrl == 0)
		{
			auto cam = Ctx->gd.CamNormal;
			cam->setPosition(co.t * 3200.f);
			cam->setRotation(glm::eulerAngles(co.r) * core::RADTODEG);
			cam->updateAbsolutePosition();
			Eqv->LaunchFw3D(cam->getAbsolutePosition() + vector3df(0, -100, 0), Eqv->getCurPtrFwIdx(1), cam->getRotation().rotationToDirection() * 10000.f);
		}

		if (curOud.h[i].buttons[xb_select])
			arRoot->launchPhyObj(0.6);
	}
#endif
}
void AppNameSpace::AppMainAMP::djiUpdate()
{
#if DRONE_AR
	if (dsFrame >= 0) //updated from json string msg
	{
		MatrixRecorder::DrDataStruct ds = lastDs;
		float ratio = dsFrame / 3.f;
		if (bDelayFrame) {
			ds.d.rtt = ds.d.rtt.interpolate(lastDs.d.rtt, lastDs1.d.rtt, ratio);
			ds.d.pos = ds.d.pos.interpolate(lastDs.d.pos, lastDs1.d.pos, ratio);
			ds.d.vec2 = ds.d.vec2.interpolate(lastDs.d.vec2, lastDs1.d.vec2, ratio);
		}
		else {
			ds.d.rtt = ds.d.rtt.interpolate(curDs.d.rtt, lastDs.d.rtt, ratio);
			ds.d.pos = ds.d.pos.interpolate(curDs.d.pos, lastDs.d.pos, ratio);
			ds.d.vec2 = ds.d.vec2.interpolate(curDs.d.vec2, lastDs.d.vec2, ratio);
		}
		Eqv->snLW->acYaw = ds.d.rtt;//ds.d.vec2;
		Eqv->snLW->vel = (curDs.d.pos - lastDs.d.pos) * .1f;
		Eqv->UpdateCamState(&ds, false);
		dsFrame++;
	}
#endif
}

 
void AppNameSpace::AppMainAMP::drawSsaoRT()
{
#if IRR_MTR_SSAO 
	if (VkDrv->ssaoRT)
	{
		Driver->draw2DImageRect(VkDrv->ssaoRT, VkDrv->ssaoRT->getRectI(), VkDrv->ssaoRT->getRectI());
#if 1
		Driver->draw2DImageRect(VkDrv->ssaoRT->getVRP()->ssao.tex.normal, recti(0, 0, 640, 360), VkDrv->ssaoRT->getVRP()->ssao.tex.normal->getRectI());
		Driver->draw2DImageRect(VkDrv->ssaoRT->getVRP()->ssao.tex.ssaoBlur, recti(640, 0, 1280, 360), VkDrv->ssaoRT->getVRP()->ssao.tex.ssaoBlur->getRectI());
		Driver->draw2DImageRect(VkDrv->ssaoRT->getVRP()->ssao.tex.albedo, recti(1280, 0, 1280 + 640, 360), VkDrv->ssaoRT->getVRP()->ssao.tex.albedo->getRectI());
#endif
	}
#endif
}

void AppMainAMP::drawBloom()
{
	
#define USE_2PassBlur			1
	if (mmd->hasBloom && (editMode == 2 || IS_WIN))
	{
		assert(0); //to test ev_bloom modify
		Ctx->ensureMidRT(ev_bloom, 2); Ctx->ensureMidRT(ev_bloom1, 2);
		Driver->setRenderTarget(Ctx->texMidRT[USE_2PassBlur ? 1 : 2], 1, 1, 0);
		VkDrv->resetFrameCache();
		SceneManager->drawPassType(IrrPassType_GlowBase);
		VkDrv->UploadSharedBuffers();
		Driver->setRenderTarget(Ctx->texMidRT[USE_2PassBlur ? 2 : 1], 1, 1, 0);

		if (IS_WIN_DBG && toSaveHLBase) { toSaveHLBase = 0; Driver->saveTexture(Ctx->texMidRT[USE_2PassBlur ? ev_bloom : ev_bloom1], "r:/tex.png"); }

#if !USE_2PassBlur
		video::SMaterial mr;
		mr.MaterialType = VkDrv->getMT(Fx2DIdEnum::Fx2D_Blur);
		mr.setTexture(0, Ctx->texMidRT[2]);
		CbMr2D* cb = VkDrv->getCB(Fx2DIdEnum::Fx2D_Blur);
		int size = 32;
		cb->blur.blurSize = size;
		cb->blur.blurWeightMul = 32;
		cb->blur.Directions = 16;
		cb->blur.Quality = 8;
		cb->blur.sigma = size / 2.f; cb->blur.sigma2 = cb->blur.sigma * cb->blur.sigma;
		Driver->draw2DImageMr(mr, Ctx->texMidRT[1]->getRectI(), Ctx->texMidRT[2]->getRectI());
		Driver->setRenderTarget(nullptr, 0, 0, 0);
#else
		video::SMaterial mr;
		mr.MaterialType = VkDrv->getMT(Fx2DIdEnum::Fx2D_BlurV);
		mr.setTexture(0, Ctx->texMidRT[ev_bloom]);
		CbMr2D* cb = VkDrv->getCB(Fx2DIdEnum::Fx2D_BlurV);
		int size = 32; // 128;// 
		cb->blur.blurWeightMul = 1;
		cb->blur.blurSize = size;
		cb->blur.sigma = size / 2.f; cb->blur.sigma2 = cb->blur.sigma * cb->blur.sigma;
		Driver->draw2DImageMr(mr, Ctx->texMidRT[ev_bloom]->getRectI(), Ctx->texMidRT[ev_bloom1]->getRectI());

		Driver->setRenderTarget(Ctx->texMidRT[ev_bloom], 1, 1, 0);
		mr.MaterialType = VkDrv->getMT(Fx2DIdEnum::Fx2D_BlurH);
		mr.setTexture(0, Ctx->texMidRT[ev_bloom1]);
		cb = VkDrv->getCB(Fx2DIdEnum::Fx2D_BlurH);
		cb->blur.blurWeightMul = 16; // 128;// 
		cb->blur.blurSize = size;
		cb->blur.sigma = size / 2.f; cb->blur.sigma2 = cb->blur.sigma * cb->blur.sigma;
		Driver->draw2DImageMr(mr, Ctx->texMidRT[ev_bloom1]->getRectI(), Ctx->texMidRT[ev_bloom1]->getRectI());
		Driver->setRenderTarget(nullptr, 0, 0, 0);
#endif
	}
}


void AppMainAMP::saveMidiWithLyric(double bpm, int ofs)
{

#ifdef SYNC_MIDI_FROM
#ifdef _WIN32 

	InputMediaInfo imi;
	imi.startS = 0;
	imi.startSeekS = 0;
	vp.mediaListId = 0; vp.mediaListCount = 1;
	vp.mFF->startDecodeFile(imi, "", 1, SUBTITLE_PATH);

	//midilib::MidiOutFile mof; mof.newFile();
	midilib::MidiMan midi;
	midilib::MidiData* md{};
	bool finish = false;
	int ms = 0;
	midi.startGettingEvt(SYNC_MIDI_FROM, 1.0);
	midi.recordBegin(bpm);
	SubtitleData sd;
	struct SS {
		int ms; std::wstring txt;
	};
	std::vector<SS> lyrics;
	while (!finish) {
		if (vp.mFF->getSubtitle(ms, sd, &finish)) {
			int rgc = (int)sd.ex.ranges.size();

			for (int i = 0; i < rgc; i++)
			{
				auto rg = sd.ex.ranges[i];
				DPWCS((L"  %03d, t = %3d %s", i, ms + rg.timeOfs, rg.txt.c_str()));
				lyrics.push_back({ ms + rg.timeOfs,rg.txt });
			}
		}
		ms += 1;
	}

	finish = false; ms = 0;
	int li = 0, ni = 0;  //lyric, note	
	int ch = 0, ldkey = 0, lastLi = -1;
	while (!midi.giFinished)
	{
		double time = ms / 1000.0;
		midi.setRecTime(time);
		midi.getEvtAtTime(0, time);

		if (li < lyrics.size() - 1 && lyrics[li + 1].ms < ms + ofs) li++;
		midilib::MidiMan::GetEvtInfo& gi = midi.geinfo[0];

		if (gi.act == 1) {
			if (ldkey) {
				midi.keyOp(ch, ldkey, 0, 0);
				ldkey = 0;
			}
			midi.keyOp(ch, ldkey = gi.keyDn, 1, 100);
			midi.lyricOp(ch, lastLi == li ? L"-" : lyrics[li].txt); lastLi = li;
		}
		else if (gi.act == 2)
		{
			midi.keyOp(ch, ldkey, 0, 0);
			ldkey = 0;
		}
		ms += 1;
	}
	midi.keyOp(ch, ldkey, 0, 0);
	midi.recordEnd(ualib::strFmt("r:/midiKaraoke_%03d.mid", ofs));

	MessageBox(0, L"MIDI SAVED", L"OK", MB_OK);

#endif
#endif
}

void AppMainAMP::scaleMmdNode(irr::scene::IrrSaba* sb, MMDNode* node, float sc, int resetRB)
{

	node->SetScale({ sc,sc,sc });
	//it0->saba->Pmx->resetRigidBodies();
	if (resetRB == 2) {
		sb->setAllDynRbActive(false, 1);
	}
	else if (resetRB == 1) {
		sb->Pmx->resetRigidBodies();
	}
	else if (resetRB == 10) {
		sb->Pmx->resetDynBodies();
		//sb->getRb0()->ResetRigidbody(0);
		//sb->ndHead->rb0->ResetRigidbody(0);
	}
	else if (resetRB == 11) {
		sb->Rb0()->ResetMovement(0);
		sb->ndUpper2->rb0->ResetMovement(0);
	}
}

void AppMainAMP::morphToFace(float dur)
{
	matrix4 tgt; arRoot->getFocusPos(tgt); auto sb = curChar();
	vector3df vel(0, 1, 0);
	if (sb->ndUpper2)
	{
		tgt = sb->mmd2irr(sb->ndUpper2->GetGlobalTransform());
		auto hd = sb->getAbsoluteTransformation() * (sb->ndHead->GetGlobalTransform());
		vel = hd.getTranslation() - sb->mmd2irr(sb->ndHeadTgt->getGlobalPos());
	}
	else tgt = sb->mmd2irr(sb->Rb0()->GetTransform());
	auto pos = tgt.getTranslation(), rtt = vel.getHorizontalAngle();// hd.getRotationDegrees(); //rtt.z = 0; rtt.x = std::clamp(rtt.x,-60.f,60.f);// // rtt.x = rtt.z = 0;///rtt.x == -rtt.x ;  
	if (rtt.x > 0) rtt.x = 0;
	Ctx->morphCamTgtTo(dur, &pos, 1 ? &rtt : 0);

}


void AppMainAMP::mediaSeekToPercent(double st)
{
	if (!vp.mFF->getMedia(0)) return;
	vp.clearVFrames();
	vp.clearScrFrames();
	vp.mediaTime = vp.mFF->getMediaDuration(0) * st;
	if (editMode != 2 && vp.mFF->setMediaSeek(0, st) < 0)//preview play finished
	{
		startDecVideo(editMode, vp.mediaTime);
		vp.startPause = 1;
	}
	if (vp.paused)
		vp.pausedPlayOneFrame = 1;
}

void AppMainAMP::saveVFrame(irr::video::ITexture* tex, int idx)
{
#ifdef _WIN32
	Driver->saveTexture(tex, wstrFmt(L"R:/frames_%05d.jpg", idx).c_str());
#endif
}

void AppMainAMP::changeSpeed(double spd) {
	Ctx->changeSpeed(spd);
}



void AppMainAMP::writeTimelineToFile(const std::string& filename) {
	

	for (int i = 0; i < 8; i++) if (g_timeline[i].size()) {
		std::ofstream file(std::string("r:/img/")+filename + std::to_string(i) + ".txt");
		if (!file.is_open()) {
			std::cerr << "Error opening file: " << filename << std::endl;
			return;

		}

		for (const auto& item : g_timeline[i]) {
			std::ostringstream oss;
			oss << item.l.pos.x << " " << item.l.pos.y << " " << item.l.pos.z << " ";
			oss << item.l.dir.x << " " << item.l.dir.y << " " << item.l.dir.z << " ";
			oss << item.e.pos.x << " " << item.e.pos.y << " " << item.e.pos.z << " ";
			oss << item.e.dir.x << " " << item.e.dir.y << " " << item.e.dir.z << " ";
			oss << item.fElapsedTime << std::endl;
			file << oss.str();
		}

		file.close();
	}
}



void AppMainAMP::onDamakuCmd(const std::string& pm1, bool resetPtc)
{
	static 		float idleTime = 1;
#define MAX_DANMAKU_CHAR 10u   //弹幕文字限制
	bool isAI = false;
	idleTime = 10.f;
#if APP_HAS_CLOCK
	isClockOn = false; clockLastTimeS = L"XXXXXXXX";
#endif
	std::wstring text = ualib::Utf8toWcs(pm1);

	UaJsonSetting jss;
	Json::Value jRoot;
	jss.parseJsonString(pm1, jRoot, false);

	auto type = jRoot["type"].asString();


	if (type == "comment")
	{
		static int ti_ = 200, t0_ = 200, fw_ = 0, rty_ = 0, ft_ = 0, ol_ = 0;
		static uint32_t c_ = 0, type_ = 0;
		vector3df pos_, vec_;
		std::string fw;
		std::wstring username = ualib::Utf8toWcs(jRoot["userName"].asString());
		std::wstring comment = ualib::Utf8toWcs(jRoot["text"].asString());
		if (username == L"{Axile's Danmaku AI}") 	isAI = true, idleTime = 5.0f, username = L"弹幕AI";


		size_t sp = comment.find_last_of(L'#');
		if (sp == std::wstring::npos)
			sp = comment.find_last_of(L'＃');
#if DANMAKU_FORCE
		if (sp == std::wstring::npos)
		{
			comment += L'#';
			sp = comment.find_last_of(L'#');
		}
#endif
		if (sp != std::wstring::npos) {
			std::wstring outtext = comment.substr(0, std::min(sp, (size_t)MAX_DANMAKU_CHAR));
			std::wstring paramStr = comment.substr(sp + 1);
			paramStr = wcsReplaceAll(paramStr, L"，", L",");
			if (paramStr.length() > 0)
			{
				std::string pm = ualib::WcstoUtf8(paramStr);
				ualib::StrVec sv;
				ualib::split(pm, ',', sv);

				for (size_t si = 0; si < sv.size(); si++)
				{
					DP(("%d %s", si, sv[si].c_str()));
					ualib::StrVec nv;
					ualib::split(sv[si], '=', nv);
					auto s0 = nv[0];
					if (nv.size() == 1)
					{
						if (s0 == "r")
						{
							ti_ = 333, t0_ = 2000, fw_ = 0, rty_ = 0, ft_ = 0, ol_ = 0;
							c_ = 0, type_ = 0;
							Eqv->setCurPtrFwId(0);
						}
						else if (s0 == "pause")
						{
							bool paused = IFppt->togglePause();
#if HAS_MMD
							mmd->setAnimating(!paused);
#endif
							vp.paused = paused;
						}
						else try
						{
							int fid = std::stoi(s0);
							Eqv->setCurPtrFwId(fid);
						}
						catch (...)
						{
							DP(("convert err"));
						}
					}
					else if (nv.size() == 2) {
						bool isNum = true;
						int num = 0;
						try {
							num = std::stoi(nv[1]);
						}
						catch (...) {
							isNum = false;
						}
						if (s0 == "fw" && isNum) {
							Eqv->setCurPtrFwId(num);
						}
						else if (s0 == "t0" && isNum) {
							t0_ = std::max(100, num);
						}
						else if (s0 == "ti" && isNum) {
							ti_ = std::max(0, num);
						}
						else if (s0 == "rty" && isNum)		rty_ = num;

						else if (s0 == "ft" && isNum)		ft_ = num;
						else if (s0 == "ol" && isNum)		ol_ = num;
						else if (s0 == "c") {
							c_ = UaJson::parseHex(nv[1]);
							if (c_ > 0)	Eqv->getTfd()->color0 = 0xFF000000 | (0xFFFFFF & c_);
						}
						else if (s0 == "fw")fw = nv[1];
						else if (s0 == "pos") {
							std::string pms = nv[1];
							sscanf_s(pms.c_str(), "%f  %f  %f", &pos_.X, &pos_.Y, &pos_.Z);
						}

						else if (s0 == "lnMdC" && isNum)
						{
							Eqv->ptrFwCC[Eqv->getCurPtrFwId()] = num;
						}
						else if (s0 == "vfId" && isNum)
						{
							mmd->sabas[0]->fwLch->vtxFwId = num;
						}
						else if (s0 == "vfIdR" && isNum)
						{
							IFppt->ResetParticles();
							mmd->sabas[0]->fwLch->vtxFwId = num;
						}
						else if (s0 == "fwScP" && isNum)
						{
							IFppt->PtScale = num / 100.f;
						}

					}
				}
			}

			if (outtext.size() > 0)
			{
				FrameWaiter fw;
				fw.waitNframeAndRun(1, [=](FWTask& t) {
					if (outtext == L"木条" || outtext == L"woods") {
						sendKeyEvent(KEY_KEY_Y);
					}
					else if (outtext == L"投币" || outtext == L"coin") {
						addMmdObjParam apm{
							.flag = 0,		.pm1 = 0,		.scale = 5.f
						};
						addMmdObj(apm);
					}
					else if (outtext == L"跑步" || outtext == L"run") {
						sb0->loadAnimation("d:/mmd/vmd/runNearFar.vmd"); sb0->setPlaying(true);
					}
					else if (outtext == L"立正" || outtext == L"stand") {
						sb0->clearAnimation(); sb0->resetAnimation();  sb0->setPlaying(true);
					}
					else if (outtext == L"躺平" || outtext == L"lay") {
						sb0->loadAnimation("data/mmd/layDA.vmd"); sb0->setPlaying(true);
					}
					else if (outtext == L"尺寸+" || outtext == L"large") {
						double sc = sb0->ndRoot->GetScale().x;	sc = std::clamp(sc * 1.5f, 0.1, 10.0);
						sb0->ndRoot->SetScale({ sc,sc,sc }); //if (sc > 1.f) snLight->setPosition(baseLightPos * sc);
					}
					else if (outtext == L"尺寸-" || outtext == L"small") {
						double sc = sb0->ndRoot->GetScale().x;	sc = std::clamp(sc / 1.5, 0.5, 10.0);
						sb0->ndRoot->SetScale({ sc,sc,sc }); //if (sc > 1.f) snLight->setPosition(baseLightPos * sc);
					}
					else if (outtext == L"怠惰-") {
						sb0->rd.phyAniMul = std::min(sb0->rd.phyAniMul * 4.f, 10.0f);
					}
					else if (outtext == L"怠惰+") {
						sb0->rd.phyAniMul = std::max(sb0->rd.phyAniMul * 0.25f, 0.001f);
					}
					else if (outtext == L"清理" || outtext == L"clear") {
						Pom->removeAllObjs();
						for (auto& cube : CCubeGridSceneNode::Walls) cube->remove();
						CCubeGridSceneNode::Walls.clear();
					}
					else
					{
						//clearFws();
						std::wstring msg = username + L" 说： " + comment;
						//showMessage(msg, 3.f);
						Eqv->dmFtTxtPm.beginWaitMs = t0_; Eqv->dmFtTxtPm.charDurMs = ti_; //Ctx->gd.CamRtt->bindTargetAndRotation
						Eqv->dmFtTxtPm.fontId = ft_;
						Eqv->dmFtTxtPm.outlineType = ol_ % 10;
						Eqv->dmFtTxtPm.outlineThickness = ol_ / 10 / 100.f;
						Eqv->dmFtTxtPm.charDurMs = 500;
						idleTime += (t0_ + ti_ * outtext.length()) / 1000.f;
						testTextFw(true, outtext, 1);
					}

					});

			}

		}
	}
	else if (type == "giftSend") {
		testTextFw(true, L"恭喜发财", 1);
	}
}

