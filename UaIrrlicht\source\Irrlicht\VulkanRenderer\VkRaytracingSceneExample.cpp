#include "VkRaytracingSceneExample.h"

#ifdef _IRR_COMPILE_WITH_VULKAN_
#if VK_ENABLE_RAYTRACING
#include "irrlicht.h"
#include "VkDriver.h"
#include "VkRaytracingSceneManager.h"
#include "VkRaytracingDemo.h"
#include "ISceneManager.h"
#include "ICameraSceneNode.h"
#include "IMeshSceneNode.h"
#include "os.h"
namespace irr {
namespace video {

bool setupRaytracingScene(VkDriver* driver, scene::ISceneManager* sceneManager) {
    if (!driver || !sceneManager) {
        return false;
    }
    
    // Check if raytracing is supported
    if (!driver->isRaytracingSupported()) {
        os::Printer::log("Raytracing is not supported on this device", ELL_WARNING);
        return false;
    }
    
    // Create raytracing scene manager
    VkRaytracingSceneManager* rtSceneManager = new VkRaytracingSceneManager(driver, sceneManager);
    if (!rtSceneManager->initialize()) {
        delete rtSceneManager;
        os::Printer::log("Failed to initialize raytracing scene manager", ELL_ERROR);
        return false;
    }
    
    // Set up camera for optimal viewing
    scene::ICameraSceneNode* camera = sceneManager->addCameraSceneNode(
        nullptr,                                    // parent
        core::vector3df(0, 5, -15),                // position
        core::vector3df(0, 0, 0),                  // look at
        -1,                                        // id
        true                                       // make active
    );
    
    if (camera) {
        camera->setFOV(core::PI / 4.0f); // 45 degrees
        camera->setNearValue(0.1f);
        camera->setFarValue(1000.0f);
    }
    
    // Create cube mirror at origin
    scene::IMeshSceneNode* cubeMirror = rtSceneManager->createCubeMirror(
        core::vector3df(0, 0, 0),      // position
        core::vector3df(0, 0, 0),      // rotation
        core::vector3df(1, 1, 1),      // scale
        4.0f                           // size
    );
    
    if (cubeMirror) {
        os::Printer::log("Created cube mirror for raytracing", ELL_INFORMATION);
    }
    
    // Create reflective sphere to the right
    scene::IMeshSceneNode* sphere = rtSceneManager->createReflectiveSphere(
        core::vector3df(8, 0, 0),      // position
        core::vector3df(0, 0, 0),      // rotation
        core::vector3df(1, 1, 1),      // scale
        3.0f,                          // radius
        20,                            // poly count
        core::vector3df(0.8f, 0.2f, 0.2f) // red color
    );
    
    if (sphere) {
        os::Printer::log("Created reflective sphere for raytracing", ELL_INFORMATION);
    }
    
    // Create another sphere to the left with different color
    scene::IMeshSceneNode* sphere2 = rtSceneManager->createReflectiveSphere(
        core::vector3df(-8, 0, 0),     // position
        core::vector3df(0, 0, 0),      // rotation
        core::vector3df(1, 1, 1),      // scale
        2.5f,                          // radius
        16,                            // poly count
        core::vector3df(0.2f, 0.8f, 0.2f) // green color
    );
    
    if (sphere2) {
        os::Printer::log("Created second reflective sphere for raytracing", ELL_INFORMATION);
    }
    
    // Update raytracing objects
    rtSceneManager->updateObjects();
    
    os::Printer::log("Raytracing scene setup completed successfully", ELL_INFORMATION);
    return true;
}

bool testRaytracingWithScene(VkDriver* driver, scene::ISceneManager* sceneManager) {
    if (!driver || !sceneManager) {
        return false;
    }
    
    // Get active camera
    scene::ICameraSceneNode* camera = sceneManager->getActiveCamera();
    if (!camera) {
        os::Printer::log("No active camera found for raytracing test", ELL_ERROR);
        return false;
    }
    
    // Set up raytracing test parameters using the correct structure
    VkRaytracingDemo::TestRtPm params;
    params.width = 1080;
    params.height = 1920;
    params.skyTexture = driver->NullTexture; // Will use procedural sky

    // Set up camera parameters
    params.cameraPos = camera->getAbsolutePosition();
    params.cameraUp = camera->getUpVector();

    // Calculate camera direction and right vectors
    core::vector3df target = camera->getTarget();
    params.cameraDir = (target - params.cameraPos).normalize();
    params.cameraRight = params.cameraDir.crossProduct(params.cameraUp).normalize();

    // Set up projection parameters
    params.fovY = camera->getFOV();
    params.nearPlane = camera->getNearValue();
    params.farPlane = camera->getFarValue();
    params.time = 0.0f;

    // Calculate view and projection matrices
    params.viewMatrix = camera->getViewMatrix();
    params.projMatrix = camera->getProjectionMatrix();
	DP(("Camera Pos: %f %f %f", params.cameraPos.X, params.cameraPos.Y, params.cameraPos.Z));
	DP(("Camera Dir: %f %f %f", params.cameraDir.X, params.cameraDir.Y, params.cameraDir.Z));
	DP(("Camera Target: %f %f %f", target.X, target.Y, target.Z));
    // Get raytracing demo and test
    VkRaytracingDemo* rtDemo = driver->getRaytracingDemo(params);
    if (!rtDemo) {
        os::Printer::log("Failed to get raytracing demo", ELL_ERROR);
        return false;
    }
    
    // Test raytracing with our scene
    if (rtDemo->testRaytracing(params)) {
        os::Printer::log("Raytracing test with scene completed successfully", ELL_INFORMATION);
        return true;
    } else {
        os::Printer::log("Raytracing test with scene failed", ELL_ERROR);
        return false;
    }
}

// Example usage function that can be called from your main application
void exampleRaytracingSceneUsage(VkDriver* driver, scene::ISceneManager* sceneManager) {
    os::Printer::log("=== Raytracing Scene Example ===", ELL_INFORMATION);
    
    // Step 1: Set up the raytracing scene with cube mirror and spheres
    if (setupRaytracingScene(driver, sceneManager)) {
        os::Printer::log("Scene setup successful", ELL_INFORMATION);
        
        // Step 2: Test raytracing with the scene
        if (testRaytracingWithScene(driver, sceneManager)) {
            os::Printer::log("Raytracing test successful", ELL_INFORMATION);
            // Get active camera
            scene::ICameraSceneNode* camera = sceneManager->getActiveCamera();
            if (!camera) {
                os::Printer::log("No active camera found for raytracing test", ELL_ERROR);
                return ;
            }
            // Set up raytracing test parameters using the correct structure
            VkRaytracingDemo::TestRtPm params;
            params.width = 800;
            params.height = 600;
            params.skyTexture = nullptr; // Will use procedural sky

            // Set up camera parameters
            params.cameraPos = camera->getAbsolutePosition();
            params.cameraUp = camera->getUpVector();

            // Calculate camera direction and right vectors
            core::vector3df target = camera->getTarget();
            params.cameraDir = (target - params.cameraPos).normalize();
            params.cameraRight = params.cameraDir.crossProduct(params.cameraUp).normalize();

            // Set up projection parameters
            params.fovY = camera->getFOV();
            params.nearPlane = camera->getNearValue();
            params.farPlane = camera->getFarValue();
            params.time = 0.0f;

            // Calculate view and projection matrices
            params.viewMatrix = camera->getViewMatrix();
            params.projMatrix = camera->getProjectionMatrix();
            // The raytracing output texture can now be displayed or saved
            VkRaytracingDemo* rtDemo = driver->getRaytracingDemo(params);
            if (rtDemo) {
                VkTexture* outputTexture = rtDemo->getOutputTexture();
                if (outputTexture) {
                    os::Printer::log("Raytracing output texture is ready for display", ELL_INFORMATION);
                    // You can now use this texture for display in your application
                }
            }
        }
    } else {
        os::Printer::log("Scene setup failed", ELL_ERROR);
    }
    
    os::Printer::log("=== End Raytracing Scene Example ===", ELL_INFORMATION);
}

} // namespace video
} // namespace irr

#endif // VK_ENABLE_RAYTRACING
#endif // _IRR_COMPILE_WITH_VULKAN_
