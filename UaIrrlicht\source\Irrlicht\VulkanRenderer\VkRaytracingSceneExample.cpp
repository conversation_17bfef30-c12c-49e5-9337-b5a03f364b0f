#include "VkRaytracingSceneExample.h"

#ifdef _IRR_COMPILE_WITH_VULKAN_
#if VK_ENABLE_RAYTRACING
#include "irrlicht.h"
#include "VkDriver.h"
#include "VkRaytracingSceneManager.h"
#include "VkRaytracingDemo.h"
#include "ISceneManager.h"
#include "ICameraSceneNode.h"
#include "IMeshSceneNode.h"
#include "os.h"
namespace irr {
namespace video {

// NOTE: These functions are not used by the current raytracing implementation.
// Raytracing geometry is created directly in VkRaytracingDemo::createTestGeometry()

bool setupRaytracingScene(VkDriver* driver, scene::ISceneManager* sceneManager) {
    // Unused - kept for API compatibility
    return true;
}

void exampleRaytracingSceneUsage(VkDriver* driver, scene::ISceneManager* sceneManager) {
    // Unused - kept for API compatibility
}

} // namespace video
} // namespace irr

#endif // VK_ENABLE_RAYTRACING
#endif // _IRR_COMPILE_WITH_VULKAN_
