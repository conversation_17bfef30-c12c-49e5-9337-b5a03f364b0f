@echo off
rem THIS CODE AND INFORMATION IS PROVIDED "AS IS" WITHOUT WARRANTY OF
rem ANY KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO
rem THE IMPLIED WARRANTIES OF ME<PERSON>HANTABILITY AND/OR FITNESS FOR A
rem PARTICULAR PURPOSE.
rem
rem Copyright (c) Microsoft Corporation. All rights reserved.

setlocal

start  cmd /c "cpf2.cmd"
start  cmd /c "cpfcs.cmd"
set fxDebug=1
set error=0

set FXCOPTS= -g -V

if %fxDebug% == 1 (
	set FXCOPTS=  -g -V 
)

set PCFXC=glslangValidator

:continue

call :CompileFX FFCode vert 
call :CompileFX FFCodeNL vert 
call :CompileFX FFCodePoint vert 
call :CompileFX FFCodeNoTex frag 
call :CompileFX FFCode frag 
call :CompileFX FFCode3DUI vert 
call :CompileFX FFCode3DUI frag
call :CompileFX FFCodeSpec vert 
call :CompileFX FFCodeSpec_I vert 
call :CompileFX FFCodeDF2D frag 
call :CompileFX FFCodeSpec frag
call :CompileFX FFCodePick vert 
call :CompileFX FFCodePick frag 
call :CompileFX FFCubemap vert 
call :CompileFX FFCubemap frag 
rem call :CompileFX FFCode2DColor vert 
rem call :CompileFX FFCode2DVertexAlpha frag 
rem call :CompileFX FFCode2DVertexColorDuDv frag 
rem call :CompileFX FFCode2DVertexColor2a frag 
rem  call :CompileFX FFCode2D_BlurV frag 
rem  call :CompileFX FFCode2D_BlurH frag 
rem rem call :CompileFX FFCode2D_Shadow frag 
rem call :CompileFXN FFCode2D_Blur frag FFCode2D_BlurHM "-DBLUR_COLOR_TO_BW_HMAP=1"
rem call :CompileFX FFCode2D_Blur frag "-DBLUR_COLOR_TO_BW_HMAP=0"
rem rem call :CompileFX FFCode2D_NormMap frag 
rem rem call :CompileFX FFCode2D_Gradient frag 
rem rem call :CompileFX FFCode2DYuv420spCvt frag 
rem rem call :CompileFX FFCode2DYuv420pCvt frag 
rem rem call :CompileFXN FFCode2DYuv420spCvt frag FFCode2DYuv420spCvt_RGB "-DRGBA_ORDER=1"
rem rem call :CompileFXN FFCode2DYuv420pCvt frag FFCode2DYuv420pCvt_RGB "-DRGBA_ORDER=1"
rem call :CompileFX FFCode2D_HDR frag 
rem call :CompileFX FFCode2D_ToneMapping frag 
rem rem call :CompileFX FFCode2D_CubeToSphere frag 
rem call :CompileFX FFCode2D_ProcessImage frag 
rem  call :CompileFX FfSsaoGBuf vert 
rem  call :CompileFX FfSsaoGBuf frag 
rem  call :CompileFXDbg FfSsaoFullscreen vert 
rem  call :CompileFXDbg FfSsao frag 
rem  call :CompileFXDbg FfSsaoBlur frag 
rem  call :CompileFXDbg FfSsaoComposition frag 
call :CompileFX mmd vert
call :CompileFX mmd frag
call :CompileFX mmd_pick frag
call :CompileFX mmdAnd frag 
rem call :CompileFXN mmd frag mmd_depth_frag "-DONLY_OUT_DEPTH=1"
call :CompileFX mmd_edge vert 
call :CompileFX mmd_edge frag
call :CompileFX mmd_trans_vtx comp 
call :CompileFX mmd_trans_vtx vert 
rem call :CompileFX mmd_ground_shadow vert 
rem call :CompileFX mmd_ground_shadow frag 
call :CompileFXN mmd frag mmdOIT "-DIS_OIT"

echo.

if %error% == 0 (
    echo Main Shaders compiled OK ~~~~~~~~~~~~~~~~~~
) else (
    echo There were shader compilation errors!
)

endlocal
exit /b

:CompileFX

set fxc=%PCFXC% %FXCOPTS%   -o Compiled\%1_%2_SpirV_base.spv -S %2  %1.%2 -e main %3   --target-env vulkan1.3
echo.
echo %fxc%
%fxc% || set error=1
spirv-opt -O Compiled\%1_%2_SpirV_base.spv -o Compiled\%1_%2_SpirV.spv  
call spv2h.exe %1 %2 
exit /b

 

:CompileFXN
set fxc=%PCFXC% %FXCOPTS%  -o Compiled\%3_%2_SpirV_base.spv -S %2  %1.%2 -e main  %4   --target-env vulkan1.3
echo.
echo %fxc%
%fxc% || set error=1
spirv-opt -O Compiled\%3_%2_SpirV_base.spv -o Compiled\%3_%2_SpirV.spv  
call spv2h.exe %3 %2 
exit /b

:CompileFXDbg
set fxc=%PCFXC% %FXCOPTS%   -o Compiled\%3_%2_SpirV.spv -S %2  %1.%2 -e main  
echo.
echo %fxc%
%fxc% || set error=1
exit /b

:needxdk
echo ERROR: CompileShaders xbox requires the Microsoft Xbox One XDK
echo        (try re-running from the XDK Command Prompt)