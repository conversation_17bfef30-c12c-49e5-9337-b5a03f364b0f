cmake_minimum_required(VERSION 3.10)
project(RaytracingTest)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Add compile definitions
add_definitions(-DVK_ENABLE_RAYTRACING=1)
add_definitions(-D_IRR_COMPILE_WITH_VULKAN_=1)

# Find Vulkan
find_package(Vulkan REQUIRED)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR})
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/UaIrrlicht/include)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/UaIrrlicht/source/Irrlicht)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/UaIrrlicht/source/Irrlicht/VulkanRenderer)

# Add executable
add_executable(raytracing_test raytracing_test.cpp)

# Link libraries
target_link_libraries(raytracing_test 
    ${Vulkan_LIBRARIES}
    # Add other required libraries here
)

# Set compiler flags
if(MSVC)
    target_compile_options(raytracing_test PRIVATE /W3)
else()
    target_compile_options(raytracing_test PRIVATE -Wall -Wextra)
endif() 